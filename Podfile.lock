PODS:
  - AFNetworking/NSURLConnection (2.7.0):
    - AFNetworking/Reachability
    - AFNetworking/Security
    - AFNetworking/Serialization
  - AFNetworking/NSURLSession (2.7.0):
    - AFNetworking/Reachability
    - AFNetworking/Security
    - AFNetworking/Serialization
  - AFNetworking/Reachability (2.7.0)
  - AFNetworking/Security (2.7.0)
  - AFNetworking/Serialization (2.7.0)
  - Anura (1.0.0)
  - AnuraGrowthBookIntegration (1.0.1):
    - Anura
    - GrowthBook-IOS
  - AppAuth (1.7.3):
    - AppAuth/Core (= 1.7.3)
    - AppAuth/ExternalUserAgent (= 1.7.3)
  - AppAuth/Core (1.7.3)
  - AppAuth/ExternalUserAgent (1.7.3):
    - AppAuth/Core
  - AppsFlyerFramework (6.15.2):
    - AppsFlyerFramework/Main (= 6.15.2)
  - AppsFlyerFramework/Main (6.15.2)
  - DateTools (2.0.0)
  - DateToolsSwift (5.0.0)
  - Expecta (1.0.6)
  - Fakery (5.1.0)
  - FBAEMKit (18.0.0):
    - FBSDKCoreKit_Basics (= 18.0.0)
  - FBSDKCoreKit (18.0.0):
    - FBAEMKit (= 18.0.0)
    - FBSDKCoreKit_Basics (= 18.0.0)
  - FBSDKCoreKit_Basics (18.0.0)
  - FBSDKLoginKit (18.0.0):
    - FBSDKCoreKit (= 18.0.0)
  - FingerprintJS (1.5.0):
    - FingerprintJS/Core (= 1.5.0)
  - FingerprintJS/Core (1.5.0):
    - FingerprintJS/SystemControl
  - FingerprintJS/SystemControl (1.5.0)
  - FingerprintPro (2.2.0)
  - FirebaseABTesting (10.23.0):
    - FirebaseCore (~> 10.0)
  - FirebaseAnalytics (10.23.0):
    - FirebaseAnalytics/AdIdSupport (= 10.23.0)
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - FirebaseAnalytics/AdIdSupport (10.23.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleAppMeasurement (= 10.23.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - FirebaseCore (10.23.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.12)
    - GoogleUtilities/Logger (~> 7.12)
  - FirebaseCoreExtension (10.23.0):
    - FirebaseCore (~> 10.0)
  - FirebaseCoreInternal (10.23.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseCrashlytics (10.23.0):
    - FirebaseCore (~> 10.5)
    - FirebaseInstallations (~> 10.0)
    - FirebaseRemoteConfigInterop (~> 10.23)
    - FirebaseSessions (~> 10.5)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/Environment (~> 7.8)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesObjC (~> 2.1)
  - FirebaseInstallations (10.23.0):
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - PromisesObjC (~> 2.1)
  - FirebasePerformance (10.23.0):
    - FirebaseCore (~> 10.5)
    - FirebaseInstallations (~> 10.0)
    - FirebaseRemoteConfig (~> 10.0)
    - FirebaseSessions (~> 10.5)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/ISASwizzler (~> 7.8)
    - GoogleUtilities/MethodSwizzler (~> 7.8)
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - FirebaseRemoteConfig (10.23.0):
    - FirebaseABTesting (~> 10.0)
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - FirebaseRemoteConfigInterop (~> 10.23)
    - FirebaseSharedSwift (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseRemoteConfigInterop (10.23.0)
  - FirebaseSessions (10.23.0):
    - FirebaseCore (~> 10.5)
    - FirebaseCoreExtension (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/Environment (~> 7.10)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesSwift (~> 2.1)
  - FirebaseSharedSwift (10.23.0)
  - GoogleAppMeasurement (10.23.0):
    - GoogleAppMeasurement/AdIdSupport (= 10.23.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - GoogleAppMeasurement/AdIdSupport (10.23.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 10.23.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (10.23.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - GoogleDataTransport (9.4.1):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleMaps (8.3.1):
    - GoogleMaps/Maps (= 8.3.1)
  - GoogleMaps/Base (8.3.1)
  - GoogleMaps/Maps (8.3.1):
    - GoogleMaps/Base
  - GoogleSignIn (7.1.0):
    - AppAuth (< 2.0, >= 1.7.3)
    - GTMAppAuth (< 5.0, >= 4.1.1)
    - GTMSessionFetcher/Core (~> 3.3)
  - GoogleUtilities/AppDelegateSwizzler (7.13.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (7.13.0):
    - GoogleUtilities/Privacy
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/ISASwizzler (7.13.0):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (7.13.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (7.13.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (7.13.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.13.0)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (7.13.0)
  - GoogleUtilities/Reachability (7.13.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (7.13.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GrowthBook-IOS (1.0.41)
  - GTMAppAuth (4.1.1):
    - AppAuth/Core (~> 1.7)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3)
  - GTMSessionFetcher/Core (3.5.0)
  - iCarousel (1.8.3)
  - IGListDiffKit (5.0.0)
  - IGListKit (5.0.0):
    - IGListDiffKit (= 5.0.0)
  - Instructions (2.3.0)
  - IQKeyboardManager (6.4.2)
  - KLCPopup (1.1)
  - libPhoneNumber-iOS (0.9.15)
  - lottie-ios (3.4.1)
  - MagicalRecord (2.3.2):
    - MagicalRecord/Core (= 2.3.2)
  - MagicalRecord/Core (2.3.2)
  - Mantle (2.2.0):
    - Mantle/extobjc (= 2.2.0)
  - Mantle/extobjc (2.2.0)
  - Masonry (1.1.0)
  - Mixpanel-swift (4.0.6):
    - Mixpanel-swift/Complete (= 4.0.6)
  - Mixpanel-swift/Complete (4.0.6)
  - MoEngage-iOS-SDK (9.17.5)
  - MoEngageCards (4.16.1):
    - MoEngage-iOS-SDK (< 9.18.0, >= 9.17.0)
  - MoEngageInApp (6.00.3):
    - MoEngage-iOS-SDK (< 9.18.0, >= 9.17.1)
    - MoEngageTriggerEvaluator (< 1.02.0, >= 1.01.0)
  - MoEngageTriggerEvaluator (1.01.1):
    - MoEngage-iOS-SDK (< 9.18.0, >= 9.17.0)
  - nanopb (2.30910.0):
    - nanopb/decode (= 2.30910.0)
    - nanopb/encode (= 2.30910.0)
  - nanopb/decode (2.30910.0)
  - nanopb/encode (2.30910.0)
  - Nimble (10.0.0)
  - OCMock (3.4.3)
  - PanModal (1.2.7)
  - Phone-Country-Code-and-Flags (0.2.1)
  - PhoneCountryCodePicker (0.2.0):
    - Phone-Country-Code-and-Flags
  - PINCache (3.0.3):
    - PINCache/Arc-exception-safe (= 3.0.3)
    - PINCache/Core (= 3.0.3)
  - PINCache/Arc-exception-safe (3.0.3):
    - PINCache/Core
  - PINCache/Core (3.0.3):
    - PINOperation (~> 1.2.1)
  - PINOperation (1.2.1)
  - PINRemoteImage (3.0.3):
    - PINRemoteImage/PINCache (= 3.0.3)
  - PINRemoteImage/Core (3.0.3):
    - PINOperation
  - PINRemoteImage/PINCache (3.0.3):
    - PINCache (~> 3.0.3)
    - PINRemoteImage/Core
  - PromisesObjC (2.4.0)
  - PromisesSwift (2.4.0):
    - PromisesObjC (= 2.4.0)
  - PullUpController (0.7.0)
  - PulsingHalo (0.2.10)
  - Quick (5.0.1)
  - ReactiveCocoa (9.0.0):
    - ReactiveSwift (~> 5.0)
  - ReactiveObjC (3.1.1)
  - ReactiveSwift (5.0.1):
    - Result (~> 4.1)
  - Result (4.1.0)
  - Sentry (8.36.0):
    - Sentry/Core (= 8.36.0)
  - Sentry/Core (8.36.0)
  - Shimmer (1.0.2)
  - Siren (5.7.1)
  - SnapKit (5.7.1)
  - Specta (1.0.7)
  - SVProgressHUD (2.2.5)
  - UICollectionViewLeftAlignedLayout (1.0.2)
  - "UITextView+Placeholder (1.3.3)"
  - UXCam (3.4.5)
  - Valet (4.1.1)
  - youtube-ios-player-helper (1.0.4)

DEPENDENCIES:
  - AFNetworking/NSURLConnection (~> 2.7.0)
  - AFNetworking/NSURLSession (~> 2.7.0)
  - AFNetworking/Reachability (~> 2.7.0)
  - AFNetworking/Security (~> 2.7.0)
  - AFNetworking/Serialization (~> 2.7.0)
  - "Anura (from `*************************:hf/tpd/cx/ios/anura-ios.git`)"
  - "AnuraGrowthBookIntegration (from `*************************:hf/tpd/cx/ios/anura-ios.git`)"
  - AppAuth (~> 1.7.3)
  - AppsFlyerFramework (~> 6.15.2)
  - DateTools
  - DateToolsSwift
  - Expecta (~> 1.0.5)
  - Fakery (~> 5.1.0)
  - FBSDKCoreKit (~> 18.0.0)
  - FBSDKLoginKit (~> 18.0.0)
  - FingerprintJS (~> 1.5.0)
  - FingerprintPro (~> 2.2.0)
  - FirebaseAnalytics (~> 10.23.0)
  - FirebaseCrashlytics (~> 10.23.0)
  - FirebasePerformance (~> 10.23.0)
  - GoogleMaps (~> 8.3.1)
  - GoogleSignIn (~> 7.0)
  - iCarousel (from `https://github.com/happyfresh/iCarousel`)
  - IGListKit (~> 5.0.0)
  - Instructions (from `https://github.com/ephread/Instructions`, tag `2.3.0`)
  - IQKeyboardManager (~> 6.4.0)
  - KLCPopup (from `https://github.com/happyfresh/KLCPopup`)
  - libPhoneNumber-iOS (~> 0.8)
  - lottie-ios
  - MagicalRecord (~> 2.2)
  - Mantle
  - Masonry (~> 1.1.0)
  - Mixpanel-swift (~> 4.0.4)
  - MoEngage-iOS-SDK (~> 9.17.5)
  - MoEngageCards (~> 4.16.1)
  - MoEngageInApp (~> 6.00.3)
  - Nimble (~> 10.0.0)
  - OCMock (~> 3.4.1)
  - PanModal (from `https://github.com/happyfresh/PanModal`)
  - PhoneCountryCodePicker
  - PINRemoteImage
  - PullUpController (from `https://github.com/happyfresh/PullUpController`)
  - PulsingHalo
  - Quick (~> 5.0.1)
  - ReactiveCocoa (~> 9.0)
  - ReactiveObjC (~> 3.1.0)
  - Sentry (~> 8.36.0)
  - Shimmer
  - Siren (~> 5.7.1)
  - SnapKit (~> 5.7.1)
  - Specta (~> 1.0.5)
  - SVProgressHUD
  - UICollectionViewLeftAlignedLayout
  - "UITextView+Placeholder (~> 1.3.3)"
  - UXCam (~> 3.4.5)
  - Valet (~> 4.1.1)
  - youtube-ios-player-helper (~> 1.0.4)

SPEC REPOS:
  trunk:
    - AFNetworking
    - AppAuth
    - AppsFlyerFramework
    - DateTools
    - DateToolsSwift
    - Expecta
    - Fakery
    - FBAEMKit
    - FBSDKCoreKit
    - FBSDKCoreKit_Basics
    - FBSDKLoginKit
    - FingerprintJS
    - FingerprintPro
    - FirebaseABTesting
    - FirebaseAnalytics
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseCrashlytics
    - FirebaseInstallations
    - FirebasePerformance
    - FirebaseRemoteConfig
    - FirebaseRemoteConfigInterop
    - FirebaseSessions
    - FirebaseSharedSwift
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleMaps
    - GoogleSignIn
    - GoogleUtilities
    - GrowthBook-IOS
    - GTMAppAuth
    - GTMSessionFetcher
    - IGListDiffKit
    - IGListKit
    - IQKeyboardManager
    - libPhoneNumber-iOS
    - lottie-ios
    - MagicalRecord
    - Mantle
    - Masonry
    - Mixpanel-swift
    - MoEngage-iOS-SDK
    - MoEngageCards
    - MoEngageInApp
    - MoEngageTriggerEvaluator
    - nanopb
    - Nimble
    - OCMock
    - Phone-Country-Code-and-Flags
    - PhoneCountryCodePicker
    - PINCache
    - PINOperation
    - PINRemoteImage
    - PromisesObjC
    - PromisesSwift
    - PulsingHalo
    - Quick
    - ReactiveCocoa
    - ReactiveObjC
    - ReactiveSwift
    - Result
    - Sentry
    - Shimmer
    - Siren
    - SnapKit
    - Specta
    - SVProgressHUD
    - UICollectionViewLeftAlignedLayout
    - "UITextView+Placeholder"
    - UXCam
    - Valet
    - youtube-ios-player-helper

EXTERNAL SOURCES:
  Anura:
    :git: "*************************:hf/tpd/cx/ios/anura-ios.git"
  AnuraGrowthBookIntegration:
    :git: "*************************:hf/tpd/cx/ios/anura-ios.git"
  iCarousel:
    :git: https://github.com/happyfresh/iCarousel
  Instructions:
    :git: https://github.com/ephread/Instructions
    :tag: 2.3.0
  KLCPopup:
    :git: https://github.com/happyfresh/KLCPopup
  PanModal:
    :git: https://github.com/happyfresh/PanModal
  PullUpController:
    :git: https://github.com/happyfresh/PullUpController

CHECKOUT OPTIONS:
  Anura:
    :commit: 5321e38be60b59f019b5cc9826ec130ade2b459a
    :git: "*************************:hf/tpd/cx/ios/anura-ios.git"
  AnuraGrowthBookIntegration:
    :commit: 5321e38be60b59f019b5cc9826ec130ade2b459a
    :git: "*************************:hf/tpd/cx/ios/anura-ios.git"
  iCarousel:
    :commit: 78b1641dad0a34a0249fcd1819eb8fef1093b63d
    :git: https://github.com/happyfresh/iCarousel
  Instructions:
    :git: https://github.com/ephread/Instructions
    :tag: 2.3.0
  KLCPopup:
    :commit: 3e3bc6df421d39a106f6a1995c723e4a598a398d
    :git: https://github.com/happyfresh/KLCPopup
  PanModal:
    :commit: 5b4f48b16f74d97610a2d8c5fba58df8b1fb3efc
    :git: https://github.com/happyfresh/PanModal
  PullUpController:
    :commit: ae25de825a6809912006c8d36f4c2a948264ecb7
    :git: https://github.com/happyfresh/PullUpController

SPEC CHECKSUMS:
  AFNetworking: 9d57de7506959955d82fb5274ee4bec86b930e52
  Anura: ef28b7cad9f117c949611ab6cc144f9a0aa77566
  AnuraGrowthBookIntegration: 2a0295fe2916427b833baea4cce346d9c739e699
  AppAuth: a13994980c1ec792f7e2e665acd4d4aa6be43240
  AppsFlyerFramework: 3c6c50b7b3f0f37f648a5b23114940e0766e65e8
  DateTools: 933ac9c490f21f92127cf690ccd8c397e0126caf
  DateToolsSwift: 4207ada6ad615d8dc076323d27037c94916dbfa6
  Expecta: 3b6bd90a64b9a1dcb0b70aa0e10a7f8f631667d5
  Fakery: a90caff00ca5cacde6c161c3eafc72314a03d34d
  FBAEMKit: e34530df538b8eb8aeb53c35867715ba6c63ef0c
  FBSDKCoreKit: d3f479a69127acebb1c6aad91c1a33907bcf6c2f
  FBSDKCoreKit_Basics: 017b6dc2a1862024815a8229e75661e627ac1e29
  FBSDKLoginKit: 5875762d1fe09ddcb05d03365d4f5dc34413843d
  FingerprintJS: 96410117a394cca04d0f1e2374944c8697f2cceb
  FingerprintPro: 72e35dc0315b75ba36eafaaed996dbb2f35f7326
  FirebaseABTesting: aec61ed9a34d85a95e2013a3fdf051426a2419df
  FirebaseAnalytics: 45f6e2e5ef8ccbb90be73ae983c3b20fa78837f7
  FirebaseCore: 63efb128decaebb04c4033ed4e05fb0bb1cccef1
  FirebaseCoreExtension: cb88851781a24e031d1b58e0bd01eb1f46b044b5
  FirebaseCoreInternal: 6a292e6f0bece1243a737e81556e56e5e19282e3
  FirebaseCrashlytics: b7aca2d52dd2440257a13741d2909ad80745ac6c
  FirebaseInstallations: 42d6ead4605d6eafb3b6683674e80e18eb6f2c35
  FirebasePerformance: 702cd035354b2d2b2896e128129b978b41d02ad7
  FirebaseRemoteConfig: 70ebe9542cf5242d762d1c0b4d53bfc472e0a4ce
  FirebaseRemoteConfigInterop: cbc87ffa4932719a7911a08e94510f18f026f5a7
  FirebaseSessions: f06853e30f99fe42aa511014d7ee6c8c319f08a3
  FirebaseSharedSwift: c92645b392db3c41a83a0aa967de16f8bad25568
  GoogleAppMeasurement: 453eb0de99fcf2bdec9403e9ac5d7871fdba3e3f
  GoogleDataTransport: 6c09b596d841063d76d4288cc2d2f42cc36e1e2a
  GoogleMaps: b47b67bd63d708477d6ff457da2d695c0d8ceb5f
  GoogleSignIn: d4281ab6cf21542b1cfaff85c191f230b399d2db
  GoogleUtilities: d053d902a8edaa9904e1bd00c37535385b8ed152
  GrowthBook-IOS: 972500403abdcd0c99af336a0c54c0bb2f64b4f2
  GTMAppAuth: f69bd07d68cd3b766125f7e072c45d7340dea0de
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  iCarousel: d782f635afac190c49bb8ee455882284cff8b85f
  IGListDiffKit: 02d937d7e714d6f558771f284ed80668d483b522
  IGListKit: baf7e87b4c2262cd569c380e46ca5667a4a82c82
  Instructions: 3aac22d036495ba4c40e00747e0a275da40ce53d
  IQKeyboardManager: b41e0a4943fbac4c11a239d31a688667675f962e
  KLCPopup: f233d3334c4808d1087ede03b3ae72fc3fc8a2aa
  libPhoneNumber-iOS: 0a32a9525cf8744fe02c5206eb30d571e38f7d75
  lottie-ios: 016449b5d8be0c3dcbcfa0a9988469999cd04c5d
  MagicalRecord: 53bed74b4323b930992a725be713e53b37d19755
  Mantle: c5aa8794a29a022dfbbfc9799af95f477a69b62d
  Masonry: 678fab65091a9290e40e2832a55e7ab731aad201
  Mixpanel-swift: fb7051fdfa36ec65891000af7cbefe05cffc4a00
  MoEngage-iOS-SDK: aeb31076a11030d63dd67c3dbd712a6aa56507c5
  MoEngageCards: 85a129813a4ac69ca3071a8266cc7c29638b9e45
  MoEngageInApp: cfa846bdca03334148996b3ebb4065439b6da461
  MoEngageTriggerEvaluator: fea5448dd4545e427f4e01caa0bc5bef50896573
  nanopb: 438bc412db1928dac798aa6fd75726007be04262
  Nimble: 5316ef81a170ce87baf72dd961f22f89a602ff84
  OCMock: 43565190abc78977ad44a61c0d20d7f0784d35ab
  PanModal: 3e16ead1a907fb06f4df3f13492fd00149fa4974
  Phone-Country-Code-and-Flags: 19b303483d6e14091a1e3a404ec2aac8f08a985c
  PhoneCountryCodePicker: 3d8a797562bbcd2309944e0b683eadaa824cacad
  PINCache: 7a8fc1a691173d21dbddbf86cd515de6efa55086
  PINOperation: 00c935935f1e8cf0d1e2d6b542e75b88fc3e5e20
  PINRemoteImage: f1295b29f8c5e640e25335a1b2bd9d805171bd01
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  PromisesSwift: 9d77319bbe72ebf6d872900551f7eeba9bce2851
  PullUpController: 5c8710cb686dca43d5850ae994debb7fdb824b75
  PulsingHalo: 1a9c6b65a8e12ac9eee1627fc8476efcce8e1918
  Quick: 749aa754fd1e7d984f2000fe051e18a3a9809179
  ReactiveCocoa: 29709cf0cc7502ddd04a51220e10ee0336d8bec8
  ReactiveObjC: 011caa393aa0383245f2dcf9bf02e86b80b36040
  ReactiveSwift: cac20a5bbe560c5806bd29c0fccf90d03b996ac1
  Result: bd966fac789cc6c1563440b348ab2598cc24d5c7
  Sentry: f8374b5415bc38dfb5645941b3ae31230fbeae57
  Shimmer: c5374be1c2b0c9e292fb05b339a513cf291cac86
  Siren: 76ac728f4f2f4a3576c322272e94a0e37955664f
  SnapKit: d612e99e678a2d3b95bf60b0705ed0a35c03484a
  Specta: 3e1bd89c3517421982dc4d1c992503e48bd5fe66
  SVProgressHUD: 1428aafac632c1f86f62aa4243ec12008d7a51d6
  UICollectionViewLeftAlignedLayout: 830bf6fa5bab9f9b464f62e3384f9d2e00b3c0f6
  "UITextView+Placeholder": c407b27599ea23cca425946fee3cf1db5d547008
  UXCam: c24a71093f9051c553380e647a0c55ee0c586091
  Valet: 11ab0e318b7284888a81392e36136563b4af5deb
  youtube-ios-player-helper: e9b97535e816db3152179d84d999bc1807ecd689

PODFILE CHECKSUM: 2a7c968786ccf891b1908fda706c21c6666aac37

COCOAPODS: 1.16.2
