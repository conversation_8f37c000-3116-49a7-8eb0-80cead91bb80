<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="23094" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" launchScreen="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES" initialViewController="01J-lp-oVM">
    <device id="retina5_9" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23084"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--View Controller-->
        <scene sceneID="EHf-IW-A2E">
            <objects>
                <viewController id="01J-lp-oVM" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="Ze5-6b-2t3">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="812"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="hf_img_splash_screen_independence2025" translatesAutoresizingMaskIntoConstraints="NO" id="FwW-Qw-Qtg" userLabel="hf_img_splash_screen_independence2025">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="812"/>
                            </imageView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="Bcu-3y-fUS"/>
                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <constraints>
                            <constraint firstItem="FwW-Qw-Qtg" firstAttribute="top" secondItem="Ze5-6b-2t3" secondAttribute="top" id="25n-I3-csf"/>
                            <constraint firstItem="FwW-Qw-Qtg" firstAttribute="leading" secondItem="Bcu-3y-fUS" secondAttribute="leading" id="H4U-cG-asJ"/>
                            <constraint firstItem="Bcu-3y-fUS" firstAttribute="trailing" secondItem="FwW-Qw-Qtg" secondAttribute="trailing" id="ZA0-e3-8Bd"/>
                            <constraint firstItem="FwW-Qw-Qtg" firstAttribute="centerY" secondItem="Ze5-6b-2t3" secondAttribute="centerY" id="cTG-6a-5gA"/>
                        </constraints>
                    </view>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="iYj-Kq-Ea1" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="52.671755725190835" y="374.64788732394368"/>
        </scene>
    </scenes>
    <resources>
        <image name="hf_img_splash_screen_independence2025" width="375.66665649414062" height="813.33331298828125"/>
    </resources>
</document>
