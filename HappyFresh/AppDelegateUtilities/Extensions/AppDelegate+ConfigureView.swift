//
//  AppDelegate+ConfigureView.swift
//  HappyFresh
//
//  Created by <PERSON> on 08/07/22.
//  Copyright © 2022 HappyFresh Inc. All rights reserved.
//

import UIKit

extension AppDelegate {
    var orderViewController: UIViewController {
        let myOrdersViewController = MyOrdersViewController()
        myOrdersViewController.isTabViewController = true
        let navigationController = UINavigationController(rootViewController: myOrdersViewController)
        navigationController.tabBarItem = UITabBarItem(
            title: NSLocalizedString("Order", comment: ""),
            image: UIImage(named: "hf_icon_bar_orders_24px_dark"),
            selectedImage: UIImage(named: "hf_icon_bar_orders_24px_orange"))
        
        return navigationController
    }
    
    func configureNavigationBar() {
        if #available(iOS 13.0, *) {
            let tabBarAppearance: UITabBarAppearance = UITabBarAppearance()
            tabBarAppearance.configureWithOpaqueBackground()
            tabBarAppearance.backgroundColor = .white
            UITabBar.appearance().standardAppearance = tabBarAppearance

            if #available(iOS 15.0, *) {
                UITabBar.appearance().barTintColor = .white
                UITabBar.appearance().scrollEdgeAppearance = tabBarAppearance
            }
        } else {
            UITabBar.appearance().barTintColor = .hf_white()
            UITabBar.appearance().isTranslucent = false
        }
    }

    @objc func setupRootViewController() {
        if (window == nil) {
            window = UIWindow(frame: UIScreen.main.bounds)
            window?.makeKeyAndVisible()
        }

        if !HFUserDefault.sharedInstance().isOnBoardingShown {
            AuthService.sharedInstance().authState = nil
            setOnboardingViewControllerAsRoot()
        } else {
            setTabBarControllerAsRoot()
        }
    }

    private func setOnboardingViewControllerAsRoot() {
        let onBoardingViewModel = OnBoardingViewModel(language: LanguageService.sharedInstance.language.code)
        let onBoardingViewController = OnBoardingViewController(viewModel: onBoardingViewModel)
        let navigationController = UINavigationController(rootViewController: onBoardingViewController)
        window?.rootViewController = navigationController
    }

    public func setTabBarControllerAsRoot(selectedSupplier: Supplier? = nil) {
        let tabBarController = UITabBarController()
        tabBarController.tabBar.tintColor = UIColor.hf_orange_1()
        
        let mainNavigationController: UINavigationController = UINavigationController(rootViewController:
            HomeViewController(selectedSupplier: selectedSupplier))
        let notificationViewController = UINavigationController(rootViewController: NotificationViewController(viewModel: NotificationViewModel()))
        let newsfeedsViewController = UINavigationController(rootViewController: NewsfeedsViewController(viewModel: NewsfeedsViewModel()))
        let accountMenuViewController = UINavigationController(rootViewController: AccountMenuViewController())
        
        mainNavigationController.tabBarItem = UITabBarItem(title: NSLocalizedString("Home", comment: ""),
                                                           image: UIImage(named: "hf_icon_bar_home_24px_dark"),
                                                           selectedImage: UIImage(named: "hf_icon_bar_home_24px_orange"))
        
        newsfeedsViewController.tabBarItem = UITabBarItem(
            title: NSLocalizedString("Promo", comment: ""),
            image: UIImage(named: "hf_icon_deals_general_black_24px"),
            selectedImage: UIImage(named: "hf_icon_deals_general_orange_24px"))
        
        notificationViewController.tabBarItem = UITabBarItem(title: NSLocalizedString("Notification", comment: ""),
                                                             image: UIImage(named: "hf_icon_bar_notifications_24px_dark"),
                                                             selectedImage: UIImage(named: "hf_icon_bar_notifications_24px_orange"))
        
        accountMenuViewController.tabBarItem = UITabBarItem(title: NSLocalizedString("Account", comment: "tab bar"),
                                                            image: UIImage(named: "hf_icon_my_account_dark_24px"),
                                                            selectedImage: UIImage(named: "hf_icon_my_account_orange_24px"))
        
        tabBarController.viewControllers = [mainNavigationController,
                                            orderViewController,
                                            newsfeedsViewController,
                                            notificationViewController,
                                            accountMenuViewController]
        
        tabBarController.tabBar.isHidden = true
        tabBarController.delegate = self
        
        window?.rootViewController = tabBarController
    }

    func checkAppStoreVersion() {
        InAppUpdateManager.checkAppStoreVersion()
    }
}
