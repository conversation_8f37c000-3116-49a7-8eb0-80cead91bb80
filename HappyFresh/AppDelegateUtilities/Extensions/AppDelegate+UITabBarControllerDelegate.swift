//
//  AppDelegate+UITabBarControllerDelegate.swift
//  HappyFresh
//
//  Created by <PERSON> on 08/07/22.
//  Copyright © 2022 HappyFresh Inc. All rights reserved.
//

import UIKit

extension AppDelegate: UITabBarControllerDelegate {
    public func tabBarController(_ tabBarController: UITabBarController, shouldSelect viewController: UIViewController) -> <PERSON><PERSON> {  
        if let navigationController = viewController as? UINavigationController,
            tabBarController.selectedViewController != navigationController {
            if let destinationVC = navigationController.topViewController as? BaseViewController,
                let sourceVC = App.topViewController() as? BaseViewController {
                //currently Auth screen from tab bar only accessed via My Orders
                destinationVC.source = destinationVC is AccountNotLoggedInViewController ? ScreenNameMyOrders : sourceVC.screenName
                if let newsfeedsViewController = destinationVC as? NewsfeedsViewController {
                    newsfeedsViewController.source = "navbar"
                    newsfeedsViewController.section = "navbar"
                }
            }
        }
        return true
    }
    
    public func tabBarController(_ tabBarController: UITabBarController, didSelect viewController: UIViewController) {
        // Track recipe screen viewed when recipe tab is selected
        if let navigationController = viewController as? UINavigationController,
           let _ = navigationController.topViewController as? RecipeListViewController {
            Tracker.sharedInstance().trackRecipeScreenViewed(triggerSource: "navbar")
        }
    }
}
