//
//  Tracker.swift
//  HappyFresh
//
//  Created by <PERSON> on 10/21/20.
//  Copyright © 2020 HappyFresh Inc. All rights reserved.
//

import Foundation

public enum EventViewSupermarketScreenSource: String {
    case myOrder = "my order"
    case notification
    case onboarding
}

extension Tracker {
    @objc func track(_ event: String, properties: [String:Any]?) {
        analytics.track(event, properties: properties)
    }
    
    func trackLogInSkipped(source: String?) {
        analytics.track("Login Skipped", properties: ["source" : source ?? ""])
    }
    
    func trackGoogleLoginClicked() {
        analytics.track("Google Login Clicked")
    }

    func trackAppleLoginClicked() {
        analytics.track("Apple Login Clicked")
    }
    
    func trackViewPaymentScreen(totalCards: Int,
                                totalPromotions: Int,
                                totalEWallets: Int,
                                orderNumber: String,
                                redeemPoint: String?,
                                redeemToggle: Bool?) {
        var properties: [String: Any] = [
            "credit_card_counts" : totalCards,
            "cc_promotion_count" : totalPromotions,
            "ewallet_counts" : totalEWallets,
            "order_number" : orderNumber
        ]
        
        if let redeemPoint = redeemPoint {
            properties["redeem_point"] = redeemPoint
        }
        
        if let redeemToggle = redeemToggle {
            properties["redeem_toggle"] = redeemToggle
        }
        

        analytics.track("Checkout - View Payment Screen", properties: properties)
    }
    
    func trackViewReplacementScreen(source: String?,
                                    totalItem: Int,
                                    totalWithReplacement: Int,
                                    totalWithoutReplacement: Int,
                                    percentage: Int,
                                    defaultReplacementOption: String) {
        let properties: [String: Any] = [
            "source": source ?? "",
            "total_item": totalItem,
            "count_with_replacement": totalWithReplacement,
            "count_without_replacement": totalWithoutReplacement,
            "percentage_replacement": percentage,
            "default_replacement_option": defaultReplacementOption
        ]

        analytics.track("View Replacement Screen", properties: properties)
    }
    
    func trackViewPointInfoFlyout(source: String) {
        let properties: [String: Any] = [
            "type": source
        ]

        analytics.track("View Point Info Flyout", properties: properties)
    }

    func trackViewContactPreferencePopup(orderNumber: String) {
        let properties = ["order_number": orderNumber]

        analytics.track("View Contact Preference Popup", properties: properties)
    }

    func trackConfirmReplacementOption(_ option: ReplacementOptionTrackingType?,
                                       orderNumber: String,
                                       percentage: Int,
                                       percentageDoNotReplace: Int) {
        let properties: [String: Any] = ([
            "option_selected": option?.rawValue,
            "order_number": orderNumber,
            "percentage_replacement": percentage,
            "percentage_do_not_replace": percentageDoNotReplace
        ] as [String: Any?]).compactMapValues { $0 }

        analytics.track("Confirm Replacement Option", properties: properties)
    }

    func trackViewLowStockPopup(variant: Variant,
                                type: String,
                                suggestionCount: Int,
                                stockLocationID: NSNumber,
                                supplierID: NSNumber,
                                orderNumber: String,
                                placement: String,
                                screen: String) {
        let product = variant.product
        let properties: [String: Any] = [
            "item_name": product?.name ?? "",
            "sku": variant.sku ?? "",
            "taxon_ids": product?.taxonIds() ?? NSNull(),
            "category": product?.categories() ?? NSNull(),
            "suggestion_count": String(suggestionCount),
            "type": type,
            "stock_location_id": stockLocationID,
            "supplier_id": supplierID,
            "order_number": orderNumber,
            "placement": placement,
            "screen": screen
        ]
        analytics.track("View Low Stock Popup", properties: properties)
    }

    func trackViewReplacementOptionPopup(variant: Variant,
                                         stockLocationID: NSNumber,
                                         supplierID: NSNumber,
                                         orderNumber: String,
                                         previousSelectedReplacement: ReplacementPreferenceSelected?,
                                         placement: String,
                                         screen: String,
                                         source: String?) {
        let product = variant.product
        var properties: [String: Any] = [
            "item_name": product?.name ?? "",
            "sku": variant.sku ?? "",
            "taxon_ids": product?.taxonIds() ?? NSNull(),
            "category": product?.categories() ?? NSNull(),
            "stock_location_id": stockLocationID,
            "supplier_id": supplierID,
            "order_number": orderNumber,
            "placement": placement,
            "screen": screen,
            "source": source ?? ""
        ]
        if let previousReplacement = previousSelectedReplacement {
            properties["previous_option"] = previousReplacement.selectedOptionForTracking
            properties["previous_preference"] = previousReplacement.selectedPreferenceForTracking
        }
        analytics.track("View Replacement Option Popup", properties: properties)
    }

    func trackViewReplacementPreferencePopup(variant: Variant,
                                             stockLocationID: NSNumber,
                                             supplierID: NSNumber,
                                             orderNumber: String,
                                             placement: String,
                                             screen: String,
                                             source: String?) {

        let product = variant.product
        let properties: [String: Any] = [
            "item_name": product?.name ?? "",
            "sku": variant.sku ?? "",
            "taxon_ids": product?.taxonIds() ?? NSNull(),
            "category": product?.categories() ?? NSNull(),
            "stock_location_id": stockLocationID,
            "supplier_id": supplierID,
            "order_number": orderNumber,
            "placement": placement,
            "screen": screen,
            "source": source ?? ""
        ]
        analytics.track("View Replacement Preference Popup", properties: properties)
    }

    func trackSelectReplacement(variant: Variant,
                                stockLocationID: NSNumber,
                                supplierID: NSNumber,
                                orderNumber: String,
                                placement: String,
                                screen: String,
                                selectedReplacement: ReplacementPreferenceSelected,
                                previousSelectedReplacement: ReplacementPreferenceSelected?,
                                replacementPosition: Int
    ) {
        let product = variant.product
        var properties: [String: Any] = [
            "item_name": product?.name ?? "",
            "sku": variant.sku ?? "",
            "taxon_ids": product?.taxonIds() ?? NSNull(),
            "category": product?.categories() ?? NSNull(),
            "stock_location_id": stockLocationID,
            "selected_option": selectedReplacement.selectedOptionForTracking,
            "supplier_id": supplierID,
            "order_number": orderNumber,
            "placement": placement,
            "screen": screen
        ]

        if let selectedPreference = selectedReplacement.selectedPreferenceForTracking {
            properties["selected_preference"] = selectedPreference
        }

        if let replacementVariant = selectedReplacement.replacementVariant {
            properties["replacement_item_name"] = replacementVariant.name ?? ""
            properties["replacement_sku"] = replacementVariant.sku ?? ""
            properties["replacement_taxon_ids"] = replacementVariant.product?.taxonIds() ?? NSNull()
            properties["replacement_category"] = replacementVariant.product?.categories() ?? NSNull()
            properties["replacement_position"] = replacementPosition
        }

        if let previousReplacement = previousSelectedReplacement {
            properties["previous_option"] = previousReplacement.selectedOptionForTracking
            properties["previous_preference"] = previousReplacement.selectedPreferenceForTracking
        }

        analytics.track("Select Replacement", properties: properties)
    }

    func trackReplacementRemoved(
        orderNumber: String,
        supplierID: NSNumber?,
        stockLocationID: NSNumber?,
        previousSelectedReplacement: ReplacementPreferenceSelected,
        productName: String?,
        sku: String?,
        placement: ReplacementPlacement
    ) {
        let properties: [String: Any] = ([
            "order_number": orderNumber,
            "supplier_id": supplierID,
            "stock_location_id": stockLocationID,
            "previous_option": previousSelectedReplacement.selectedOptionForTracking,
            "previous_preference": previousSelectedReplacement.selectedPreferenceForTracking,
            "item_name": productName,
            "sku": sku,
            "placement": placement.rawValue,
        ] as [String: Any?]).compactMapValues { $0 }

        analytics.track("Replacement Removed", properties: properties)
    }

    func trackViewReplacementItemScreen(variant: Variant,
                                        stockLocationID: NSNumber,
                                        supplierID: NSNumber,
                                        orderNumber: String,
                                        itemsCount: Int,
                                        placement: String,
                                        source: String?) {
        let product = variant.product
        let properties: [String: Any] = [
            "category": product?.categories() ?? NSNull(),
            "item_name": product?.name ?? "",
            "sku": variant.sku ?? "",
            "taxon_ids": product?.taxonIds() ?? NSNull(),
            "stock_location_id": stockLocationID,
            "supplier_id": supplierID,
            "order_number": orderNumber,
            "items_count": itemsCount,
            "placement": placement,
            "source": source ?? ""
        ]

        analytics.track("View Replacement Item Screen", properties: properties)
    }

    func trackViewReplacementSearchResultScreen(variant: Variant,
                                                stockLocationID: NSNumber,
                                                supplierID: NSNumber,
                                                orderNumber: String,
                                                placement: String,
                                                source: String?,
                                                normalizedQuery: String,
                                                query: String,
                                                result: Int) {
        let product = variant.product
        let properties: [String: Any] = [
            "category": product?.categories() ?? NSNull(),
            "item_name": product?.name ?? "",
            "sku": variant.sku ?? "",
            "taxon_ids": product?.taxonIds() ?? NSNull(),
            "stock_location_id": stockLocationID,
            "supplier_id": supplierID,
            "order_number": orderNumber,
            "placement": placement,
            "source": source ?? "",
            "normalized_query": normalizedQuery,
            "query": query,
            "result": result
        ]

        analytics.track("View Replacement Search Result Screen", properties: properties)
    }
    
    @objc
    func trackMyItemsViewPastOrdersScreen(stockLocation: StockLocation?) {
        let properties: [String: Any] = [
            "supplier_id": stockLocation?.supplier?.supplierID ?? 0,
            "stock_location_id": stockLocation?.stockLocationID ?? 0,
            "stock_location_name": stockLocation?.name ?? ""
        ]
        analytics.track("My Items - View Past Orders Screen", properties: properties)
    }
    
    @objc
    func trackMyItemsViewPastOrdersDetailScreen(withSource source: String?,
                                                orderStatus: String,
                                                stockLocation: StockLocation?) {
        analytics.track(
            EcommerceEvent.productListViewed,
            properties: [
                "source": source ?? "",
                "order_status": orderStatus,
                "past_order_stock_location_name": stockLocation?.name ?? "",
                "past_order_stock_location_id": stockLocation?.stockLocationID ?? 0,
                HFAnalyticsParams.screenName: HFAnalyticsProductListScreenName.pastOrderDetail
            ])
    }
    
    @objc func trackViewSignupScreen(source: String?) {
        analytics.track(
            "View Signup Screen",
            properties: ["source": source ?? "", "th_privacy_consent_shown": false])
        
        appsFlyerTracker.trackViewSignupScreen()
    }
    
    @objc func trackOrderFeeFlyout() {
        analytics.track("View Order Fee Flyout")
    }
    
    func trackShareProduct(media: String,
                           category: String,
                           subcategory: String,
                           taxon: String,
                           supplierID: Int,
                           stockLocationID: Int) {
        let properties: [String: Any] = [
            "method" : media,
            "category" : category,
            "subcategory" : subcategory,
            "taxon" : taxon,
            "supplier_id" : supplierID,
            "stock_location_id" : stockLocationID
        ]
        
        analytics.track("Item Shared", properties: properties)
    }
    
    func trackShareButtonTapped(
                           category: String,
                           subcategory: String,
                           taxon: String,
                           supplierID: Int,
                           stockLocationID: Int) {
        let properties: [String: Any] = [
            "category" : category,
            "subcategory" : subcategory,
            "taxon" : taxon,
            "supplier_id" : supplierID,
            "stock_location_id" : stockLocationID,
            "screen" : ScreenNameProductDetail
        ]
        
        analytics.track("Item Share Clicked", properties: properties)
    }
    
    func trackViewCampaignScreen(withSKU sku: String,
                                 isSupplierAvailable: Bool,
                                 suppliersCount: Int,
                                 supplierID: Int?,
                                 stockLocationID: Int?,
                                 category: String,
                                 source: String? = nil) {
        analytics.track(
            "View Campaign Screen",
            properties: [
                "SKU": sku,
                "supplier_count": suppliersCount,
                "supplier_available": isSupplierAvailable,
                "supplier_id": supplierID ?? 0,
                "stock_location_id": stockLocationID ?? 0,
                "category": category,
                "source": source ?? ""
            ])
    }
    
    func trackViewStoreInfoFlyout(withStockLocationId stockLocationId: Int?, supplierId: Int?) {
        let properties: [String: Any] = [
            "stock_location_id": stockLocationId,
            "supplier_id": supplierId,
        ].compactMapValues { $0 }

        analytics.track(
            "View Store Info Flyout",
            properties: properties
        )
    }
    
    func trackVendorSectionClicked(vendorID: Int,
                                   vendorName: String,
                                   supplierID: NSNumber,
                                   stockLocationID: NSNumber,
                                   itemSource: String,
                                   taxonSource: String,
                                   categorySource: String,
                                   ctaSource: String) {
        analytics.track(
            "Vendor Section Clicked",
            properties: [
                "vendor_id": vendorID,
                "vendor_name": vendorName,
                "supplier_id": supplierID,
                "stock_location_id": stockLocationID,
                "item_source": itemSource,
                "taxon_source": taxonSource,
                "category_source": categorySource,
                "cta_source": ctaSource
            ])
    }

    func trackAllowTrackingAccess(_ status: Bool) {
        analytics.track("Allow Tracking Access", properties: ["status": status])
    }
    
    func trackViewDeliveryPromoFlyout(orderNumber: String,
                                      supplierID: NSNumber,
                                      stockLocationID: NSNumber) {
        analytics.track("View Delivery Promo Flyout",
                        properties: [
                            "order_number" : orderNumber,
                            "supplier_id" : supplierID,
                            "stock_location_id" : stockLocationID
                        ])
    }
    
    @objc
    func trackClearCartTapped() {
        analytics.track("Clear Cart Clicked", properties: nil)
    }
    
    func trackGlobalCartClicked() {
        analytics.track("Global Cart Clicked", properties: nil)
    }
    
    func trackStoreEmptySlotCLicked() {
        analytics.track("No Slot Banner Clicked", properties: nil)
    }
    
    func trackViewCopyCartFlyout(
        previousSupplierID: NSNumber,
        previousStockLocationID: NSNumber,
        supplierID: NSNumber,
        stockLocationID: NSNumber,
        screen: String?,
        productAvailabilityPercentage: Int,
        previousCartCount: Int,
        availableCartCount: Int
    ) {
        analytics.track(
            "View Copy Cart Flyout",
            properties: [
                "previous_supplier_id": previousSupplierID,
                "previous_stock_location_id": previousStockLocationID,
                "same_supplier": supplierID.isEqual(to: previousSupplierID),
                "supplier_id": supplierID,
                "stock_location_id": stockLocationID,
                "screen": screen ?? NSNull(),
                "product_availability_percentage": productAvailabilityPercentage,
                "previous_cart_count": previousCartCount,
                "available_cart_count": availableCartCount,
                "is_shown": true
            ])
    }
    
    func trackCopyCartSelected(
        previousSupplierID: NSNumber,
        previousStockLocationID: NSNumber,
        supplierID: NSNumber,
        stockLocationID: NSNumber,
        screen: String?,
        success: Bool
    ) {
        analytics.track(
            "Copy Cart Selected",
            properties: [
                "previous_supplier_id": previousSupplierID,
                "previous_stock_location_id": previousStockLocationID,
                "same_supplier": supplierID.isEqual(to: previousSupplierID),
                "supplier_id": supplierID,
                "stock_location_id": stockLocationID,
                "screen": screen ?? NSNull(),
                "success": success
            ])
    }

    func trackViewVoucherListScreen(order: Order,
                                    vouchers: [Voucher],
                                    paymentVouchers: [PaymentPromotion],
                                    trackData: [String: Any]?,
                                    source: String?,
                                    tab: String) {
        let totalVoucherCount: Float = Float(vouchers.count + paymentVouchers.count)
        
        let eligibleVouchers = vouchers.filter { (voucher) -> Bool in voucher.isEligible }
        let ineligibleVouchers = vouchers.filter { (voucher) -> Bool in !voucher.isEligible }
        
        let eligiblePaymentVouchers = paymentVouchers.filter { $0.eligible }
        let ineligiblePaymentVouchers = paymentVouchers.filter { !($0.eligible) }
        
        let eligibleVoucherPromotionIds = eligibleVouchers.map { $0.promotionId }
        let eligiblePaymentPromotionIds = eligiblePaymentVouchers.map { $0.promotionId }
        
        let ineligibleVoucherPromotionIds = ineligibleVouchers.map { $0.promotionId }
        let ineligiblePaymentPromotionIds = ineligiblePaymentVouchers.map { $0.promotionId }
        
        let totalEligibleVoucherCount = eligibleVouchers.count + eligiblePaymentVouchers.count
        let totalIneligibleVoucherCount = ineligibleVouchers.count + ineligiblePaymentVouchers.count
        
        let eligiblePercentage: Float = roundf(Float(totalEligibleVoucherCount) / totalVoucherCount * 100)
        let ineligiblePercentage: Float = roundf(Float(totalIneligibleVoucherCount) / totalVoucherCount * 100)
        
        let ineligibleVoucherCode: [String] = ineligibleVouchers.compactMap { (ineligibleVoucherViewModels) -> String? in
            ineligibleVoucherViewModels.voucherCode
        }
        
        let cartTotal = order.totalUSD?.doubleValue ?? 0
        let usdToIdrRate = App.sharedInstance().usdToIDRRate 
        let itemTotal = order.itemTotal?.doubleValue
        let itemTotalUSD = ((itemTotal ?? 0) / usdToIdrRate * 100).rounded() / 100

        var properties: [String : Any] = ([
            "total_voucher_count" : totalVoucherCount,
            "eligible_percentage" : eligiblePercentage.isNaN ? 0 : eligiblePercentage,
            "ineligible_percentage" : ineligiblePercentage.isNaN ? 0 : ineligiblePercentage,
            "ineligible_voucher_code": ineligibleVoucherCode,
            "source": source ?? "",
            "total_eligible_voucher_count": totalEligibleVoucherCount,
            "cart_total": cartTotal.isNaN ? 0 : cartTotal,
            "total_eligible_payment_promotions_count": eligiblePaymentVouchers.count,
            "total_payment_promotions_count": paymentVouchers.count,
            "tab": tab,
            "subtotal": itemTotal,
            "subtotal_usd": itemTotalUSD,
            "eligible_voucher_promotion_ids": eligibleVoucherPromotionIds,
            "eligible_payment_promotion_ids": eligiblePaymentPromotionIds,
            "ineligible_voucher_promotion_ids": ineligibleVoucherPromotionIds,
            "ineligible_payment_promotion_ids": ineligiblePaymentPromotionIds
        ] as [String: Any?]).compactMapValues { $0 }
        properties.merge(trackData ?? [:]) { (first, _) -> Any in first }
        analytics.track("View Voucher List Screen", properties: properties)
    }

    func trackViewVoucherListScreen(tab: String) {
        let properties = ["tab": tab]

        analytics.track("View Voucher List Screen", properties: properties)
    }

    func trackSubmittedVoucher(
        withCode voucherCode: String?,
        success: Bool,
        screen: String?,
        voucherAvailable: Int,
        voucherCount: Int,
        type: String?,
        amount: Double?,
        message: String?,
        orderNumber: String?,
        discountType: [String]?,
        tab: String?,
        paymentPromotionId: Int?,
        voucherPromotionId: Int?,
        isVoucherSuccess: Bool?,
        isVoucherCombinable: Bool?,
        isPaymentPromotionSuccess: Bool?,
        isPaymentPromotionCombinable: Bool?,
        submissionMessage: String?,
        itemTotal: Double?,
        itemTotalUSD: Double?
    ) {
        let properties: [String: Any] = ([
            "voucher_code": voucherCode,
            "success": success,
            "order_number": orderNumber ?? "",
            "voucher_available": voucherAvailable > 0 ? voucherAvailable : nil,
            "voucher_count": voucherCount > 0 ? voucherCount : nil,
            "screen": screen,
            "type": type,
            "amount": amount,
            "voucher_discount_type": discountType,
            "message": message,
            "tab": tab,
            "voucher_combinable": isVoucherCombinable,
            "voucher_success": isVoucherSuccess,
            "payment_promotion_combinable": isPaymentPromotionCombinable,
            "payment_promotion_success": isPaymentPromotionSuccess,
            "payment_promotion_id": paymentPromotionId,
            "voucher_promotion_id": voucherPromotionId,
            "submission_message": submissionMessage,
            "subtotal": itemTotal,
            "subtotal_usd": itemTotalUSD
        ] as [String : Any?]).compactMapValues { $0 }

        analytics.track("Submitted Voucher", properties: properties)
    }

    func trackVoucherRemoved(orderNumber: String, screen: String, voucherCode: String) {
        var reason: String? = nil
        if screen == ScreenNameShoppingCart {
            reason = "detached in cart"
        } else if screen == SourceCartVoucherList {
            reason = "remove by user"
        } else if screen == ScreenNameDelivery {
            reason = "time slot limitation"
        }
        var properties = [
            "order_number" : orderNumber,
            "screen" : screen,
            "voucherCode" : voucherCode
        ]
        
        if let reason = reason {
            properties["reason"] = reason
        }
        analytics.track("Voucher Removed", properties: properties)
    }

    func trackVoucherListExpanded(
        orderNumber: String,
        stockLocationId: Int,
        supplierId: Int,
        totalVoucherCount: Int
    ) {
        let properties: [String: Any] = [
            "order_number": orderNumber,
            "stock_location_id": stockLocationId,
            "supplier_id": supplierId,
            "total_voucher_count": totalVoucherCount
        ]

        analytics.track("Voucher List Expanded", properties: properties)
    }

    func trackViewStoreScreen(supplierID: NSNumber,
                              supplierName: String?,
                              stockLocation: StockLocation,
                              recommendationCount: Int,
                              recommendationType: String,
                              source: String,
                              section: String,
                              sectionPosition: Int?,
                              supplierType: String?,
                              storeInfoSection: Bool,
                              isNewCart: Bool,
                              sections: [String],
                              categorySections: [String],
                              totalSectionsCount: Int,
                              vouchersCount: Int,
                              globalSearchPosition: Int?,
                              fromGlobalSearch: Bool,
                              fromGlobalCategory: Bool,
                              storeFleetUtilization: StoreFleetUtilization?,
                              isAutoShow: Bool,
                              supermarketType: SupermarketType?,
                              storeDistance: String?,
                              storeCategory: String?,
                              quickActionType: String?,
                              trackingProperties: [String: Any]?
    ) {
        var properties: [String: Any] = [
            "supplier_id" : supplierID,
            "stock_location_id" : stockLocation.stockLocationID ?? 0,
            "recommended_count": recommendationCount,
            "recommendation_type": recommendationType,
            "trigger_source": source,
            "trigger_section_source": section,
            "store_info_section" : storeInfoSection,
            "new_cart" : isNewCart,
            "store_sections": sections,
            "personalized_category_sections": categorySections,
            "sections_count": totalSectionsCount,
            "vouchers_count": vouchersCount,
            "from_global_search": fromGlobalSearch,
            "from_global_category": fromGlobalCategory,
            "automatic_popup": isAutoShow,
        ]
        
        if let supplierName = supplierName {
            properties["supplier_name"] = supplierName
        }
        
        if let stockLocationName = stockLocation.name {
            properties["stock_location_name"] = stockLocationName
        }
        
        if let sectionPosition = sectionPosition {
            properties["home_section_position"] = sectionPosition
        }

        if let supplierType = supplierType {
            properties["store_type"] = supplierType
        }
        
        if let supermarketType = supermarketType {
            properties["supermarket_type"] = supermarketType.trackingValue
        }
        
        if let storeDistance = storeDistance {
            properties["store_distance"] = storeDistance
        }

        if let globalSearchPosition = globalSearchPosition {
            properties["store_position"] = globalSearchPosition
        }
        
        if let storeCategory = storeCategory {
            properties["store_category"] = storeCategory
        }

        if let storeFleetUtilization = storeFleetUtilization {
            properties["slots_available"] = storeFleetUtilization.isAvailable
            properties["high_demand"] = storeFleetUtilization.isHighDemand
            properties["on_demand_available"] = storeFleetUtilization.isOnDemandAvailable
            properties["on_demand_exist_in_store"] = storeFleetUtilization.isOnDemandEnabled

            if let utilization = storeFleetUtilization.utilization {
                properties["fleet_utilization_percentage"] = utilization
            }

            if let earliestAvailableDate = storeFleetUtilization.earliestAvailableDate {
                let calendar = Calendar.current
                let dateComponents = calendar.dateComponents([.day], from: calendar.startOfDay(for: Date()), to: calendar.startOfDay(for: earliestAvailableDate))
                if let dayDiff = dateComponents.day {
                    properties["slot_earliest_day"] = dayDiff
                }
            }
        }
        
        if let quickActionType = quickActionType {
            properties["quick_action_type"] = quickActionType
        }
        
        if let trackingProperties = trackingProperties {
            properties.merge(trackingProperties) { _, new in new }
        }
        
        let currentStoreInfo = Tracker.currentStoreInfoProperties(stockLocation: stockLocation)
        properties.merge(currentStoreInfo) { _, new in new }

        analytics.track("Store Screen Viewed", properties: properties)
        appsFlyerTracker.trackViewStoreScreen(supplierID: supplierID, stockLocationID: stockLocation.stockLocationID)
    }

    func trackViewSpecificStoreOption(count: Int,
                                      sourceSection: String,
                                      supplierID: NSNumber?,
                                      stockLocationID: NSNumber?) {
        var properties: [String: Any] = [
            "count": count,
            "source_section": sourceSection
        ]

        if let supplierID = supplierID {
            properties["supplier_id"] = supplierID
        }

        if let stockLocationID = stockLocationID {
            properties["stock_location_id"] = stockLocationID
        }

        analytics.track("View Specific Store Option", properties: properties)
    }
    
    func trackSlotInformationClicked(supplierID: NSNumber, stockLocationID: NSNumber) {
        analytics.track("Slot Information Clicked", properties: [
            "supplier_id": supplierID,
            "stock_location_id": stockLocationID
        ])
    }
    
    @objc func trackViewNotificationScreen(cardsCount: Int, section: String?, tab: String, source: String?) {
        var properties: [String: Any] = [
            "is_empty": cardsCount == 0,
            "promo_counts": cardsCount,
            "tab": tab,
            "source": source ?? ""
        ]
        if var section = section {
            if !section.isEmpty { section = "\(section) section" }
            properties["section"] = section
        }
        analytics.track("More - Notifications", properties: properties)
        appsFlyerTracker.trackViewNewsfeedScreen(properties: properties)
    }
    
    func trackViewSupermarketsScreen(
        basketsCount: Int,
        source: String?,
        storesCount: Int,
        warehouseCount: Int,
        supermarketCount: Int,
        specialtyCount: Int,
        wetmarketCount: Int
    ) {
        analytics.track(
            "View Supermarkets Screen",
            properties: [
                "baskets_count": basketsCount,
                "source": source ?? NSNull(),
                "stores_count": storesCount,
                "warehouse_count": warehouseCount,
                "supermarket_count": supermarketCount,
                "specialty_count": specialtyCount,
                "wetmarket_count": wetmarketCount
            ])
    }
    
    @objc
    func trackViewSupermarketsScreen(basketsCount: Int, source: String?, suppliers: [Supplier]) {
        let storesCount = suppliers.count
        let warehouseCount = suppliers.filter({ $0.supplierTypeEnum == .warehouse }).count
        let supermarketCount = suppliers.filter({ $0.supplierTypeEnum == .supermarket }).count
        let specialtyCount = suppliers.filter({ $0.supplierTypeEnum == .speciality }).count
        let wetmarketCount = suppliers.filter({ $0.supplierTypeEnum == .wetMarket }).count
        
        trackViewSupermarketsScreen(
            basketsCount: basketsCount,
            source: source,
            storesCount: storesCount,
            warehouseCount: warehouseCount,
            supermarketCount: supermarketCount,
            specialtyCount: specialtyCount,
            wetmarketCount: wetmarketCount)
    }
    
    func trackSelectCategory(_ category: String) {
        analytics.track("Select Category", properties: ["category": category])
    }
    
    func trackOnboardingCompleted() {
        analytics.track("Onboarding Completed")
    }

    @objc
    func trackLogout(isForced: Bool, isRefreshToken: Bool, message: String?) {
        var properties: [String: Any] = [
            "is_force_logout": isForced,
            "is_refresh_token": isRefreshToken
        ]

        if let message = message {
            properties["message"] = message
        }

        analytics.track("Log Out", properties: properties)
    }
    
    func trackExpressConfirmationFlyout(orderNumber: String) {
        analytics.track("View Express Confirmation Flyout", properties: ["order_number": orderNumber])
    }
    
    @objc
    func trackViewLogInOAuthRequestPopUp(source: String?) {
        analytics.track("View Log In Pop Up", properties: [
                            "source": source ?? NSNull(),
                            HFAnalyticsParams.isNewIdentity: true])
    }
    
    func trackViewSurveyPopUp(order: Order?, version: Double?, surveyType: String) {
        let orderNumber = order?.number
        let supplierId = order?.currentStockLocation()?.supplier?.supplierID
        let stockLocationId = order?.currentStockLocation()?.stockLocationID
        
        analytics.track("View Survey Pop Up", properties: [
                            "order_number": orderNumber ?? NSNull(),
                            "supplier_id": supplierId ?? NSNull(),
                            "stock_location_id": stockLocationId ?? NSNull(),
                            "survey_version": version ?? NSNull(),
                            "survey_type": surveyType])
    }
    
    func trackCloseSurveyPopUp(order: Order?, version: Double?, isFirstSurveyPopup: Bool) {
        let orderNumber = order?.number
        let supplierId = order?.currentStockLocation()?.supplier?.supplierID
        let stockLocationId = order?.currentStockLocation()?.stockLocationID

        analytics.track("Close Survey Pop Up", properties: [
                            "order_number": orderNumber ?? NSNull(),
                            "supplier_id": supplierId ?? NSNull(),
                            "stock_location_id": stockLocationId ?? NSNull(),
                            "survey_version": version ?? NSNull(),
                            "layer": isFirstSurveyPopup ? "first" : "second"])
    }
    
    func trackSurveySubmitted(order: Order?, version: Double?, isGoodSurveyResult: Bool) {
        let orderNumber = order?.number
        let supplierId = order?.currentStockLocation()?.supplier?.supplierID
        let stockLocationId = order?.currentStockLocation()?.stockLocationID
        
        analytics.track("Survey Submitted", properties: [
                            "order_number": orderNumber ?? NSNull(),
                            "supplier_id": supplierId ?? NSNull(),
                            "stock_location_id": stockLocationId ?? NSNull(),
                            "survey_version": version ?? NSNull(),
                            "survey_response": isGoodSurveyResult ? "good" : "bad"])
    }
    
    func trackSurveyLinkClicked(order: Order?, version: Double?, isGoodSurveyResult: Bool) {
        let orderNumber = order?.number
        let supplierId = order?.currentStockLocation()?.supplier?.supplierID
        let stockLocationId = order?.currentStockLocation()?.stockLocationID
        
        analytics.track("Survey Link Clicked", properties: [
                            "order_number": orderNumber ?? NSNull(),
                            "supplier_id": supplierId ?? NSNull(),
                            "stock_location_id": stockLocationId ?? NSNull(),
                            "survey_version": version ?? NSNull(),
                            "survey_response": isGoodSurveyResult ? "good" : "bad"])
    }
    
    /// Track whether getProfile API call **after new identity authentication** succeeded or not.
    @objc
    func trackUserProfileUpdated(success: Bool) {
        analytics.track("User Profile Updated",
                        properties: [
                            "success": success
                        ])
    }
    
    func trackViewStoreCategoryScreen(storeCategoryName: String,
                                      supplierIds: [Int]) {
        analytics.track("View Store Category Screen", properties: [
            "store_category_name": storeCategoryName,
            "supplier_ids": supplierIds
        ])
    }
    
    func trackViewStoreListScreen(sectionName: String,
                                  triggerSource: String,
                                  storesCount: Int
    ) {
        analytics.track("Store List Viewed", properties: [
            "section_name": sectionName,
            "trigger_source": triggerSource,
            "stores_count": storesCount
        ])
    }
    
    func trackApplyFilterStoreListScreen(source: String,
                                         type: String,
                                         category: String) {
        analytics.track("Apply Filter", properties: [
            "source": source,
            "type": type,
            "category": category
        ])
    }
    
    @objc
    func trackViewHfsPlusFlyout(hfsPlusSelected: Bool, isSavePreference: Bool, viaPopup: Bool = true) {
        analytics.track("View HFS+ Flyout", properties: [
            "hfs_plus" : hfsPlusSelected,
            "do_not_show_flyout_again" : isSavePreference,
            "via_popup": viaPopup
        ])
    }
    
    func trackViewMyAccountMenu(source: String?, vouchersCount: String?) {
        var properties: [String: String] = ["source": source ?? ""]
        if let vouchersCount = vouchersCount, !vouchersCount.isEmpty {
            properties["my_vouchers_count"] = vouchersCount
        }
        analytics.track("View My Account Menu Screen", properties: properties)
    }
    
    func trackCartExpanded(orderNumber: String) {
        analytics.track("Cart Expanded", properties: ["order_number": orderNumber])
    }
    
    func trackPackagingSectionViewed() {
        analytics.track("View Packaging Section")
    }
    
    func trackViewSelectPackagingAlert(orderNumber: String) {
        analytics.track("View Select Packaging Alert", properties: ["order_number": orderNumber])
    }
    
    func trackViewFlashSaleScreen(orderNumber: String, supplierId: NSNumber, stockLocationId: NSNumber, isGetNotified: Bool) {
        analytics.track("View Flash Sale Page", properties: ["order_id": orderNumber, "supplier_id": supplierId, "stock_location_id": stockLocationId, "get_notified": isGetNotified])
    }
    
    func trackGetNotified(newsfeedId: String) {
        analytics.track("Get Notified", properties: ["newsfeed_id": newsfeedId])
    }
    
    func trackViewFlashSaleInfoFlyout(orderNumber: String, supplierId: NSNumber, stockLocationId: NSNumber) {
        analytics.track("View Flash Sale Info Flyout", properties: ["order_number": orderNumber, "supplier_id": supplierId, "stock_location_id": stockLocationId])
    }
    
    func trackViewManageAccountScreen(source: String?) {
        analytics.track("View Manage Account Screen", properties: ["source": source ?? ""])
    }
    
    @objc
    func trackViewMyAddressList(source: String?) {
        analytics.track("View My Address List Screen", properties: ["source": source ?? ""])
    }
    
    @objc
    func trackQuantityAlertFlyout(orderNumber: String?, supplierId: NSNumber?, stockLocationId: NSNumber?) {
        analytics.track("View Quantity Alert Flyout", properties: ["order_id": orderNumber ?? "", "supplier_id": supplierId ?? NSNull(), "stock_location_id": stockLocationId ?? NSNull()])
    }
    
    func trackClickReferralTnc(source: String?) {
        analytics.track("Click Referral TnC", properties: ["source": source ?? ""])
    }
        
    func trackViewEditAccountScreen(source: String?) {
        analytics.track("View Edit Account Screen", properties: ["source": source ?? ""])
    }
    
    func trackEditEmailClicked(source: String?) {
        analytics.track("Edit Email Clicked", properties: ["source": source ?? ""])
    }
    
    func trackEditPhoneNumberClicked(source: String?) {
        analytics.track("Edit Phone Number Clicked", properties: ["source": source ?? ""])
    }
    
    func trackViewPasswordConfirmationScreen(source: String?) {
        analytics.track("View Password Confirmation Screen", properties: ["source": source ?? ""])
    }
    
    func trackPhoneNumberChanged(success: Bool, errorType: String?) {
        var properties: [String: Any] = ["success": success]
        if !success {
            properties["error_type"] = errorType ?? "system_error"
        }
        analytics.track("Phone Number Changed", properties: properties)
    }
    
    // Experiment Identification
    
    func trackViewClearanceInfoFlyout(orderNumber: String = "",
                                      supplierId: NSNumber?,
                                      stockLocationId: NSNumber?,
                                      screen: String) {
        analytics.track("View Clearance Info Flyout", properties: ["order_number":orderNumber,
                                                                   "supplier_id":supplierId ?? NSNull(),
                                                                   "stock_location_id":stockLocationId ?? NSNull(),
                                                                   "screen":screen])
    }
    
    func trackClearanceFlyoutClicked(orderNumber: String = "",
                                      supplierId: NSNumber?,
                                      stockLocationId: NSNumber?,
                                      flyoutResponse: String) {
        analytics.track("Clearance Flyout Clicked", properties: ["order_number":orderNumber,
                                                                   "supplier_id":supplierId ?? NSNull(),
                                                                   "stock_location_id":stockLocationId ?? NSNull(),
                                                                   "screen":flyoutResponse])
    }
    
    func trackClearanceItemsFlyout(orderNumber: String?,
                                   supplierId: NSNumber?,
                                   stockLocationId: NSNumber?,
                                   screen: String?) {
        analytics.track("View Clearance Items Flyout", properties: ["order_number" : orderNumber ?? "",
                                                                   "supplier_id" : supplierId ?? NSNull(),
                                                                   "stock_location_id" : stockLocationId ?? NSNull(),
                                                                   "screen" : screen ?? ""])
    }
    
    func trackClearanceItemsFlyoutClicked(orderNumber: String?,
                                          supplierId: NSNumber?,
                                          stockLocationId: NSNumber?,
                                          flyoutResponse: String) {
        analytics.track("View Clearance Items Flyout Clicked", properties: ["order_number" : orderNumber ?? "",
                                                                   "supplier_id" : supplierId ?? NSNull(),
                                                                   "stock_location_id" : stockLocationId ?? NSNull(),
                                                                   "flyout_response" : flyoutResponse])
    }
    
    func trackViewHfsSubscriptionFlyout(orderNumber: String?,
                                        supplierId: NSNumber?,
                                        stockLocationId: NSNumber?) {
        analytics.track("View HFS Subscription Flyout", properties: ["order_number" : orderNumber ?? "",
                                                                     "supplier_id" : supplierId ?? NSNull(),
                                                                     "stock_location_id" : stockLocationId ?? NSNull()])
    }
    
    func trackViewHfsSubscriptionFlyoutClicked(orderNumber: String?,
                                               supplierId: NSNumber?,
                                               stockLocationId: NSNumber?,
                                               action: String) {
        analytics.track("HFS Subscription Flyout Clicked", properties: ["order_number" : orderNumber ?? "",
                                                                        "supplier_id" : supplierId ?? NSNull(),
                                                                        "stock_location_id" : stockLocationId ?? NSNull(),
                                                                        "action" : action])
    }
    
    func identifyJoinedSubscriptionWaitlist(variant: String) {
        analytics.identify(
            userIDString(),
            properties: ["join_hfs_subscription_waitlist": variant])
    }
    
    func trackViewDeliveryAddressList(source: String?,
                                      countSavedAddresses: Int) {
        analytics.track("View Delivery Address List",
                        properties: ["source": source ?? "",
                                     "count_saved_address": countSavedAddresses])
    }
    
    func trackViewEditDeliveryAddress() {
        analytics.track("View Edit Delivery Address")
    }
    
    func trackDeliveryAddressSaved() {
        analytics.track("Delivery Address Saved")
    }
    
    func trackViewItemsSuggestion(orderNumber: String?,
                                  supplierId: NSNumber?,
                                  stockLocationId: NSNumber?) {
        analytics.track("View Items Suggestion", properties: ["order_number" : orderNumber ?? "",
                                                               "supplier_id" : supplierId ?? NSNull(),
                                                               "stock_location_id" : stockLocationId ?? NSNull()])
    }
    
    func trackViewMySavedItems(orderNumber: String?,
                               supplierId: NSNumber?,
                               stockLocationId: NSNumber?,
                               coachmarkShown: Bool,
                               savedItemsShown: Bool) {
        analytics.track("View My Saved Items", properties: ["order_number" : orderNumber ?? "",
                                                            "supplier_id" : supplierId ?? NSNull(),
                                                            "stock_location_id" : stockLocationId ?? NSNull(),
                                                            "coach_mark_shown" : coachmarkShown,
                                                            "saved_items_shown" : savedItemsShown])
    }

    func trackInAppUpdateAction(fromVersion: String, toVersion: String, updateType: InAppUpdateType, action: InAppUpdateAction) {
        analytics.track("In App Update Action", properties: [
            "from_version": fromVersion,
            "to_version": toVersion,
            "update_type": updateType.rawValue,
            "action": action.rawValue
        ])
    }
    
    func trackItemSaved(orderNumber: String?,
                        supplierId: NSNumber?,
                        stockLocationId: NSNumber?,
                        count: Int) {
        analytics.track("Items Suggestion Saved", properties: ["order_number" : orderNumber ?? "",
                                                               "supplier_id" : supplierId ?? NSNull(),
                                                               "stock_location_id" : stockLocationId ?? NSNull(),
                                                               "saved_items_count" : count])
    }
    
    @objc
    func trackLoadingPageErrorFlyout(errorType: String? = nil) {
        var properties: [String: Any] = [:]
        if let errorType {
            properties["error_type"] = errorType
        }
        analytics.track("Error Loading Page Flyout", properties: properties)
    }
    
    func trackPullToRefreshHomeScreen() {
        analytics.track("Pull to Refresh")
    }
    
    func trackNewsfeedViewed(messageId: String?,
                             messageType: String?,
                             newsfeedId: String?,
                             newsfeedName: String?,
                             newsfeedPosition: Int,
                             newsfeedType: String?,
                             supplierIds: [NSNumber]?,
                             newsfeedSection: String,
                             triggerSource: String) {
        
        var properties: [String: Any] = ["message_id": messageId ?? "",
                                         "message_type": messageType ?? "",
                                         "newsfeed_name": newsfeedName ?? "",
                                         "newsfeed_position ": newsfeedPosition,
                                         "newsfeed_type": newsfeedType ?? "",
                                         "supplier_ids": supplierIds ?? "",
                                         "newsfeed_section": newsfeedSection,
                                         "trigger_source": triggerSource]
        if let newsfeedId = newsfeedId {
            properties["newsfeed_id"] = newsfeedId
        }
        
        analytics.track("Newsfeed Viewed", properties: properties)
    }
    
    func trackSubscriptionLandingPageView() {
        analytics.track("Subscription Landing Page Viewed")
    }
    
    func trackSubscriptionBenefitViewed() {
        analytics.track("Subscription Benefit Viewed")
    }
    
    func trackRetrySubscriptionPaymentClicked() {
        analytics.track("Retry Subscription Payment Clicked")
    }
    
    func trackManageSubscriptionClicked() {
        analytics.track("Manage Subscription Clicked")
    }
    
    func trackSubscriptionPlanClicked(plan: String) {
        analytics.track("Subscription Plan Clicked", properties: ["plan_selected": plan])
    }
    
    func identifyIsSubscriber(isSubscriber: Bool) {
        analytics.identify(
            userIDString(),
            properties: ["is_subscriber": isSubscriber])
    }
    
    func trackReactivateSubscriptionClicked() {
        analytics.track("Reactivate Subscription Clicked")
    }
    
    func trackSubscriptionPlanPurchased(subscriptionStatus: String) {
        analytics.track("Subscription Plan Purchased", properties: ["subscription_status" : subscriptionStatus])
    }
    
    func trackSubscriptionPlanConfirmed() {
        analytics.track("Subscription Plan Confirmed")
    }
    
    func trackDeleteAccountSelected(isEligible: Bool) {
        analytics.track("Delete Account Selected", properties: ["deletion_eligible": isEligible])
    }
    
    func trackAccountDeletionReasonsSubmitted(reasons: [String], otherReason: String?) {
        var properties: [String: Any] = [
            "deletion_reasons" : reasons
        ]
        if !otherReason.isNilOrEmpty {
            properties["other_reason"] = otherReason
        }
        analytics.track("Account Deletion Reason Submitted", properties: properties)
    }
    
    func trackAccountDeleted(success: Bool) {
        analytics.track("Account Deleted", properties: ["success": success])
    }
    
    func trackPendingCartScreenViewed(cartCount: Int,
                                      orderNumbers: [NSNumber],
                                      cardDetail: [[String: Any]]) {
        let properties: [String: Any] = [
            "pending_cart_count": cartCount,
            "order_numbers": orderNumbers
        ]
        analytics.track("Pending Cart Screen Viewed", properties: properties)
    }
    
    func trackCartDeleted(orderNumber: NSNumber?,
                          position: Int) {
        var properties: [String: Any] = [
            "basket_position": position
        ]
        if let orderNumber = orderNumber {
            properties["order_number"] = orderNumber
        }
        analytics.track("Cart Deleted", properties: properties)
    }
    
    func trackHomeSectionViewed(sectionName: String,
                                sectionPosition: Int,
                                sectionCount: Int) {
        analytics.track("Home Section Viewed", properties: ["last_section_name": sectionName,
                                                            "last_home_section_position" : sectionPosition,
                                                            "home_section_count" : sectionCount])
    }
    
    @objc func trackCustomNotificationAction(isAllowed: Bool, prePermissionLocation: PrePermissionLocation) {
        let source: String
        switch prePermissionLocation {
        case .home:
            source = "global home"
        case .newsFeed:
            source = "newsfeed"
        case .replacementScreen:
            source = "replacementscreen"
        case .onboarding:
            source = "onboarding"
        @unknown default:
            source = ""
        }
        analytics.track("View Push Notif Permission Flyout", properties: ["source": source, "cta": isAllowed ? "allow" : "not allow"])
    }
    
    func trackViewSuperMarketScreen(filterId: [Int], filterCount: Int, storeCount: Int, source: EventViewSupermarketScreenSource) {
        analytics.track("View Supermarkets Screen", properties: ["filter_id": filterId,
                                                                 "filter_count":filterCount,
                                                                 "stores_count": storeCount,
                                                                 "source": source.rawValue])
    }
    
    func trackChooseFilter(filterName: String) {
        analytics.track("Choose Filter", properties: ["filter_clicked": filterName])
    }
    
    func trackStoreSectionViewed(lastSectionPosition: Int, lastSectionName: String, sectionCount: Int) {
        analytics.track("Store Section Viewed", properties: ["last_store_section_position" : lastSectionPosition,
                                                             "last_store_section_name" : lastSectionName,
                                                             "store_section_count" : sectionCount])
    }
    
    func trackApplicationInstalled(version: String, build: String) {
        analytics.track("Application Installed", properties: [
            "version": version,
            "build": build
        ])
    }
    
    func trackApplicationUpdated(version: String, build: String, prevVersion: String, prevBuild: String) {
        analytics.track("Application Updated", properties: [
            "version": version,
            "build": build,
            "previous_version": prevVersion,
            "previous_build": prevBuild
        ])
    }
    
    func trackApplicationOpened(version: String, build: String, fromBackground: Bool, url: String, referringApplication: String) {
        analytics.track("Application Opened", properties: [
            "version": version,
            "build": build,
            "fromBackground": fromBackground,
            "url": url,
            "referringApplication": referringApplication
        ])
    }
    
    func trackApplicationBackgrounded() {
        analytics.track("Application Backgrounded")
    }
    
    func trackPushNotificationReceived(userInfo: [String:Any]) {
        analytics.track("Push Notification Received", properties: userInfo)
    }
    
    func trackPushNotificationTapped(userInfo: [String:Any]) {
        analytics.track("Push Notification Tapped", properties: userInfo)
    }
    
    @objc
    func trackBxgpPromotionApplied(screen: String, sku: String, productId: String, orderNumber: String, stockLocationId: String, supplierId: String) {
        analytics.track("BXGP Promotion Applied", properties: [
            "trigger_source": screen,
            "sku": sku,
            "product_id": productId,
            "order_number": orderNumber,
            "stock_location_id": stockLocationId,
            "supplier_id": supplierId
        ])
    }
    
    @objc
    static func currentStoreInfoProperties(stockLocation: StockLocation?) -> [String: Any] {
        guard let stockLocation else { return [:] }

        var properties: [String: Any] = [
            "current_store_id" : stockLocation.stockLocationID ?? 0,
        ]
        
        if let stockLocationName = stockLocation.name {
            properties["current_store"] = stockLocationName
        }
        
        if let supplierID = stockLocation.supplier?.supplierID {
            properties["current_supplier_id"] = supplierID
        }
        
        if let supplierName = stockLocation.supplier?.name {
            properties["current_supplier"] = supplierName
        }

        if let stateName = stockLocation.stateName {
            properties["current_store_city"] = stateName
        }
        
        if let country = stockLocation.country?.name {
            properties["current_store_country"] = country
        }
        
        return properties
    }

    func trackVIPSlotFlyoutViewed(
        stockLocationID: Int?,
        supplierID: Int?,
        orderNumber: String?
    ) {
        let properties = ([
            "stock_location_id": stockLocationID,
            "supplier_id": supplierID,
            "order_number": orderNumber
        ] as [String: Any?]).compactMapValues { $0 }

        analytics.track("VIP Slot Flyout Viewed", properties: properties)
    }

    func trackVIPMemberScreenViewed(
        userID: Int,
        source: String?
    ) {
        let properties = ([
            "user_id": userID,
            "trigger_source": source
        ] as [String: Any?]).compactMapValues { $0 }

        analytics.track("VIP Member Screen Viewed", properties: properties)
    }

    func trackCallCSButtonClicked(
        userID: Int,
        source: String?
    ) {
        let properties = ([
            "user_id": userID,
            "trigger_source": source
        ] as [String: Any?]).compactMapValues { $0 }

        analytics.track("Call CS Clicked", properties: properties)
    }

    @objc func trackSearchProducts(
        query: String?,
        autoSuggest: Bool,
        autoCorrect: Bool,
        isSorted: Bool,
        termType: String?,
        termPosition: Int,
        totalResult: Int,
        searchProperties: SearchProperties?,
        supplierCount: Int,
        isFromGlobalSearch: Bool,
        storePosition: Int,
        nullResultRecommendation: String?,
        adsProductNames: [String]?,
        adsSKUs: [String]?
    ) {
        let stockLocation = App.sharedInstance().stockLocation
        let stockLocationID = stockLocation?.stockLocationID?.intValue
        let supplierID = stockLocation?.supplier?.supplierID?.intValue
        let position = {
            if termType == EventPropertySearchTermTypeUserInput {
                return 0
            }

            return termPosition + 1
        }()

        let properties: [String: Any] = ([
            "auto_suggest": autoSuggest,
            "from_autocorrect": autoCorrect,
            "sort": isSorted,
            "query": query,
            "result": totalResult,
            "store_id": stockLocationID ?? 0,
            "supplier_id": supplierID ?? 0,
            "term_type": termType,
            "term_position": position,
            "source": self.searchSource,
            "level" : self.productTypeLevel,
            "supplier_count": supplierCount,
            "null_result_recommendation": nullResultRecommendation,
            "search_id": searchProperties?.searchID,
            "normalized_query": searchProperties?.normalizedQuery,
            "from_global_search": isFromGlobalSearch,
            "store_position": storePosition,
            "search_ads_products": adsProductNames,
            "search_ads_skus": adsSKUs
        ] as [String: Any?]).compactMapValues { $0 }

        analytics.track(EcommerceEvent.productSearched, properties: properties)
    }

    func trackViewMyOrdersScreen(
        orderCount: Int?,
        activeOrderCount: Int?,
        finishedOrderCount: Int?,
        outstandingOrderCount: Int?
    ) {
        let properties = ([
            "order_count": orderCount,
            "active_order_count": activeOrderCount,
            "finished_order_count": finishedOrderCount,
            "outstanding_order_count": outstandingOrderCount
        ] as [String: Any?]).compactMapValues { $0 }

        analytics.track("View My Orders Screen", properties: properties)
    }

    func trackOrderPaymentConfirmed(
        orderStatus: OrderStatus,
        orderNumber: String,
        paymentMethod: String?,
        supplierId: Int?,
        stockLocationId: Int?
    ) {
        let properties = ([
            "order_status": orderStatus.rawValue,
            "order_number": orderNumber,
            "payment_method": paymentMethod,
            "supplier_id": supplierId,
            "stock_location_id": stockLocationId
        ] as [String: Any?]).compactMapValues { $0 }

        analytics.track("Order Payment Confirmed", properties: properties)
    }

// MARK: - New Home Screen

    func trackViewHomeScreen(with property: HomeTrackingProperty) {
        let properties: [String: Any] = ([
            "banner_shown": property.promoBannerShown,
            "location_is_set": property.isLocationSet,
            "tracking_location_allowed": property.allowLocation,
            "foi_shown": property.firstOrderIncentiveShown,
            "foi_count": property.firstOrderIncentiveCount,
            "category_shown": property.categoryShown,
            "store_category_shown": property.storeCategoryShown,
            "order_delivered_tiles_count": property.orderDeliveredCount,
            "store_categories": property.storeCategories,
            "trigger_source": property.source,
            "change_store_type": self.changeStoreType,
            "newsfeed_type": self.newsfeedType,
            "baskets_count": property.basketCount >= 0 ? property.basketCount : nil,
            "newsfeed_count": property.promoBannerCount >= 0 ? property.promoBannerCount : nil,
            "stores_count": property.storesCount >= 0 ? property.storesCount : nil,
            "supermarket_count": property.supermarketsCount >= 0 ? property.supermarketsCount : nil,
            "speciality_count": property.specialitiesCount >= 0 ? property.specialitiesCount : nil,
            "recipe_count": property.recipeCount >= 0 ? property.recipeCount : nil,
            "personalised_store_count": property.personalisedStoreCount >= 0 ? property.personalisedStoreCount : nil,
            "top_picks_count": property.topPicksCount >= 0 ? property.topPicksCount : nil,
            "store_promo_count": property.storePromoCount >= 0 ? property.storePromoCount : nil,
            "supplier_ids": property.supplierIDs.count > 0 ? Array(property.supplierIDs) : nil,
            "home_sections": property.sections.count > 0 ? Array(property.sections) : nil,
            "location_out_of_range": property.outRange,
            "my_vouchers_count": property.voucherSectionShown ? property.voucherSectionCount : nil,
            "hfs_shown": property.hfsShown,
            "hfs_above_fold": property.hfsShown ? property.hfsAboveFold : nil,
            "quick_action_tiles": property.quickActionTiles.count > 0 ? property.quickActionTiles : nil,
            "shown_supplier_ids": property.shownSupplierIDs.count > 0 ? Array(property.shownSupplierIDs) : nil,
            "order_outstanding_tiles_count": property.outstandingOrdersCount
        ] as [String: Any?]).compactMapValues { $0 }

        analytics.track("Home Screen Viewed", properties: properties)
        appsFlyerTracker.trackViewHomeScreen()

        self.changeStoreType = nil
        self.newsfeedType = nil
    }
    
    func trackFloatingCartClicked(
        promotionId: Int?,
        achievedPromotionId: Int?,
        barPercentage: Float?,
        nudgeMessage: String?,
        subtotal: Double?,
        orderNumber: String,
        supplierId: Int?,
        stockLocationId: Int?
    ) {
        let properties = ([
            "promotion_id": promotionId,
            "achieved_promotion_id": achievedPromotionId,
            "bar_percentage": barPercentage,
            "nudge_message": nudgeMessage,
            "subtotal": subtotal,
            "order_number": orderNumber,
            "supplier_id": supplierId,
            "stock_location_id": stockLocationId
        ] as [String: Any?]).compactMapValues { $0 }
        
        analytics.track("Floating Cart Clicked", properties: properties)
    }

    // MARK: - Recipe Tracking
    
    func trackRecipeDetailViewed(
        triggerSource: String,
        recipeID: NSNumber,
        recipeName: String?,
        timeToCook: String?,
        recipePosition: Int?,
        deeplinkSource: String?
    ) {
        let properties = ([
            "trigger_source": triggerSource,
            "recipe_id": recipeID,
            "recipe_name": recipeName,
            "time_to_cook": timeToCook,
            "recipe_position": recipePosition,
            "deeplink_source": deeplinkSource
        ] as [String: Any?]).compactMapValues { $0 }
        
        analytics.track("Recipe Detail Viewed", properties: properties)
    }
    
    func trackRecipeScreenViewed(triggerSource: String) {
        let properties = [
            "trigger_source": triggerSource
        ]
        
        analytics.track("Recipe Screen Viewed", properties: properties)
    }

}
