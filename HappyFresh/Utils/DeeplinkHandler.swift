//
//  DeeplinkHandler.swift
//  HappyFresh
//
//  Created by <PERSON><PERSON> on 25/08/20.
//  Copyright © 2020 HappyFresh Inc. All rights reserved.
//

import Foundation
import SVProgressHUD

extension DeeplinkHandler {
    @objc func goToOrderStatus(order: Order) {
        App.sharedInstance().forceReloadFromBeginning(completion: {
            guard let viewModel = OrderViewModel(order: order, source: ScreenNameHomeScreen) else { return }
            let viewController = OrderViewController(viewModel: viewModel)
            let navigationController = App.topViewController()?.navigationController
            
            navigationController?.pushViewController(viewController, animated: true)
        })
    }
    
    @objc
    func handleCampaign(productID: Int, promoID: Int) {
        let viewModel = CampaignViewModel(productId: productID, promotionId: promoID)
        let vc = CampaignViewController(viewModel: viewModel)
        vc.source = Tracker.sharedInstance().campaignSource
        vc.leftBarButtonItemTypes = .close
        let navigationController = UINavigationController(rootViewController: vc)
        
        if #available(iOS 13, *) {
            navigationController.modalPresentationStyle = .fullScreen
        }
        
        App.topViewController()?.present(navigationController,
                                        animated: true)
        Tracker.sharedInstance().campaignSource = nil
    }
    
    @objc
    func showProductDetail(with stockLocation: StockLocation?,
                           product: Product?,
                           source: String?) {
        guard let stockLocation = stockLocation,
              let productID = product?.productID,
              let order = App.sharedInstance().order else { return }
        let viewModel = ProductDetailViewModel(
            order: order,
            stockLocation: stockLocation,
            productID: productID,
            source: source)
        App.topViewController()?.navigationController?.pushViewController(ProductDetailViewController(viewModel), animated: true)
    }
    
    @objc
    func showProductDetailFromCampaign(with stockLocation: StockLocation?,
                                       product: Product?,
                                       storePosition: Int,
                                       isLastVisitedStore: Bool,
                                       source: String?) {
        guard let stockLocation = stockLocation,
              let productID = product?.productID,
              let order = App.sharedInstance().order else { return }
        let viewModel = ProductDetailViewModel(
            order: order,
            stockLocation: stockLocation,
            productID: productID,
            storePosition: storePosition,
            isLastVisited: isLastVisitedStore,
            isFromDeeplinkCampaignScreen: true,
            source: source)
        App.topViewController()?.navigationController?.pushViewController(ProductDetailViewController(viewModel), animated: true)
    }
    
    @objc
    func popToStoreHome(stockLocation: StockLocation, navigationController: UINavigationController) {
        for viewController in navigationController.viewControllers {
            let storeHomeViewModel = (viewController as? StoreHomeViewController)?.viewModel
            if let stockLocationID = stockLocation.stockLocationID,
               let storeHomeStockLocationID = storeHomeViewModel?.stockLocationID,
               storeHomeStockLocationID.isEqual(to: stockLocationID) {
                navigationController.popToViewController(viewController, animated: false)
                return
            }
        }
    }
    
    @objc
    func goToStoreHome(stockLocation: StockLocation,
                       navigationController: UINavigationController) {
        App.sharedInstance().goToStoreHome(
            stockLocation: stockLocation,
            navigationController: navigationController,
            source: "deeplink")
    }
    
    @objc
    func openNewsfeedsViewController() {
        let navigationVC = UINavigationController(rootViewController: NewsfeedsViewController(isTabViewController: false,
                                                                                              section: nil,
                                                                                              viewModel: NewsfeedsViewModel()))
        navigationVC.modalPresentationStyle = .fullScreen
        App.topViewController()?.present(navigationVC, animated: true, completion: nil)
    }

    @objc
    func openCategoryListScreen() {
        let navController = App.topViewController()?.navigationController ?? UINavigationController()
        navController.pushViewController(CategoryListViewController(), animated: true)
    }
    
    @objc
    func handleOrderDetail(orderNumber: String?) {
        guard let navController = App.topViewController()?.navigationController,
              let orderNumber = orderNumber else { return }
        
        let viewModel = OrderViewModel(orderNumber: orderNumber, initialScreen: .detail, source: SourceDeeplink)
        let viewController = OrderViewController(viewModel: viewModel)
        viewController.hidesBottomBarWhenPushed = true
        
        navController.pushViewController(viewController, animated: true)
    }
    
    @objc
    func handleNewsfeed(newsfeedId: String) {
        SVProgressHUD.show()
        NewsfeedService.shared.requestDataRefresh()
        NewsfeedService.shared.newsfeedsSignalProducer().skip(first: 1).take(first: 1).startWithValues { [weak self] (newsfeeds) in
            SVProgressHUD.dismiss()
            if let newsfeed = newsfeeds?.first(where: { $0.idString == newsfeedId }) {
                if newsfeed.isFlashSale && newsfeed.isLtss {
                    guard let promotionDeeplink = newsfeed.deeplinkUrl,
                          let newsfeedId = newsfeed.idString,
                          let deeplinkUrl = NSURL(string: promotionDeeplink),
                          var params = deeplinkUrl.queryDictionary() else { return }
                    params["newsfeed_id"] = newsfeedId
                    params["type"] = "flashsale"
                    self?.handle(Deeplink(params: params))
                } else {
                    let newsfeedDetailRouter = NewsfeedDetailRouter()
                    newsfeedDetailRouter.setup(
                        with: newsfeed,
                        source: SourceDeeplink,
                        showStartShoppingButton: true
                    )
                    newsfeedDetailRouter.present(from: App.topViewController(), embedInNavigationController: true, animated: true)
                }
            } else {
                self?.openNewsfeedsViewController()
            }
        }
    }
    
    @objc
    func handleFlashSale(newsfeedId: String) {
        let viewModel = FlashSalePageViewModel(newsfeedId: newsfeedId)
        let popup = FlashSaleLoadingPopup(viewModel: viewModel)
        App.topViewController()?.showPopup(popup)
    }
    
    @objc
    func handleRefer() {
        let viewModel = ReferralWebViewModel(source: SourceDeeplink)
        let viewController = ReferralWebViewController(viewModel: viewModel)
        viewController.source = SourceDeeplink
        App.topViewController()?.navigationController?.pushViewController(viewController, animated: true)
    }
    
    @objc
    func handleSubscriptionLandingPage(isFromExpired: Bool = false)  {
        let viewModel = SubscriptionLandingViewModel(isFromExpired: isFromExpired)
        let vc = SubscriptionLandingViewController(viewModel: viewModel)
        
        SVProgressHUD.show()
        viewModel.configSubscriptions.startWithValues {(subscriptionResponse, errorMessage) in
            if let errorMessage = errorMessage {
                let popup = HFPopup(
                    title: NSLocalizedString("Uh-oh, something went wrong", comment: ""),
                    subtitle: errorMessage,
                    buttonTitle: NSLocalizedString("OK", comment: ""),
                    isHideCloseButton: true)
                App.topViewController()?.showPopup(popup)
                
                SVProgressHUD.dismiss()
                return
            }
            
            guard let subscriptionPlans = subscriptionResponse?.subscriptions else {
                SVProgressHUD.dismiss()
                return
            }
            
            SVProgressHUD.show()
            let productIds = subscriptionPlans.compactMap { plan in
                plan.providerProductId
            }
            
            InAppPurchaseService.shared.requestProducts(productIdentifiers: productIds) { products, _ in
                guard let productGroupIdentifier = products[safe: 0]?.subscriptionGroupIdentifier else {
                    SVProgressHUD.dismiss()
                    return
                }
                
                InAppPurchaseService.shared.hasSubscribedForDifferentUser(subscriptionGroupId: productGroupIdentifier) { result in
                    DispatchQueue.main.async {
                        SVProgressHUD.dismiss()
                        if result {
                            SubscriptionService.showSubscribedToDifferentAccount()
                        } else {
                            let navigationController = UINavigationController(rootViewController: vc)
                            navigationController.modalPresentationStyle = .fullScreen
                            App.topViewController()?.present(navigationController, animated: true, completion: nil)
                        }
                    }
                }
            }
        }
    }

    @objc
    func handleWebView(title: String?, urlString: String?) {
        guard let urlString = urlString?.removingPercentEncoding else { return }

        let title = title?.removingPercentEncoding
        let viewModel = NewsfeedWebViewModel(title: title, urlString: urlString)
        let viewController = NewsfeedWebViewController(viewModel: viewModel, viewModeldisplayItem: nil)
        let navigationController = UINavigationController(rootViewController: viewController)
        navigationController.modalPresentationStyle = .fullScreen

        App.topViewController()?.present(navigationController, animated: true)
    }

    @objc func goToEWalletOrderCompleteScreen(order: Order) {
        SVProgressHUD.show()
        order.checkStatus { status in
            SVProgressHUD.dismiss()

            var paymentMethodName = ""
            if let activePaymentMethod = order.activePaymentMethod() {
                if activePaymentMethod.type?.caseInsensitiveCompare(PaymentMethodStringEWallet) == .orderedSame {
                    paymentMethodName = activePaymentMethod.variant ?? ""
                } else {
                    paymentMethodName = activePaymentMethod.type ?? ""
                }
            }

            let viewModel = OrderCompleteViewModel(
                order: order,
                orderStatus: status,
                paymentMethod: paymentMethodName
            )
            let orderCompleteVC = OrderCompleteViewController(viewModel: viewModel)

            App.sharedInstance().forceReloadFromBeginning {
                if let topVC = App.topViewController() {
                    topVC.navigationController?.pushViewController(orderCompleteVC, animated: true)
                }
            }
        }
    }
    
    @objc
    func openRecipeDetail(recipeID: NSNumber) {
        let recipeDetailVC = RecipeDetailViewController(recipeID: recipeID)
        recipeDetailVC.hidesBottomBarWhenPushed = true
        if let topVC = App.topViewController() {
            topVC.navigationController?.pushViewController(recipeDetailVC, animated: true)
        }
    }
}
