
#import "DeeplinkHandler.h"

#import "DeeplinkStoreHandler.h"
#import "AuthService.h"

#import "NewsfeedDetailRouter.h"
#import "MapRouter.h"
#import "LoginViewController.h"
#import "StoreSelectionRouter.h"
#import "CategoryViewController.h"
#import "StoreSelectionViewController.h"
#import "TaxonomyService.h"
#import "CategoryRouter.h"

#import "OrderAPI.h"
#import "PromotionAPI.h"
#import "App.h"
#import "HappyFresh-Swift.h"

@import SVProgressHUD;
@import ReactiveObjC;

@interface DeeplinkHandler()

@property (nonatomic) NewsfeedDetailRouter *newsfeedDetailRouter;
@property (nonatomic) CategoryRouter *categoryRouter;
@property (nonatomic) MapRouter *mapRouter;
@property (nonatomic) StoreSelectionRouter *storeSelectionRouter;

@end

@implementation DeeplinkHandler

#pragma mark - Lazy inits
NSInteger retryCompleteEWalletPaymentCallIndex = 0;
static NSInteger const MaximumCompleteEWalletPaymentRetryCall = 1;


- (NewsfeedDetailRouter *)newsfeedDetailRouter {
    if (!_newsfeedDetailRouter) {
        _newsfeedDetailRouter = [NewsfeedDetailRouter new];
    }

    return _newsfeedDetailRouter;
}

- (CategoryRouter *)categoryRouter {
    if (_categoryRouter == nil) {
        _categoryRouter = [CategoryRouter new];
    }
    return _categoryRouter;
}

- (MapRouter *)mapRouter {
    if (!_mapRouter) {
        _mapRouter = [MapRouter new];
    }

    return _mapRouter;
}

- (StoreSelectionRouter *)storeSelectionRouter {
    if (!_storeSelectionRouter) {
        _storeSelectionRouter = [StoreSelectionRouter new];
    }
    return _storeSelectionRouter;
}

#pragma mark -

+ (instancetype)sharedInstance {
    static DeeplinkHandler *instance;
    static dispatch_once_t onceToken;

    dispatch_once(&onceToken, ^{
        instance = [DeeplinkHandler new];
    });

    return instance;
}

- (void)handleDeeplink:(Deeplink *)deeplink {
    if (deeplink.type == DeeplinkTypeUnknown) {
        return;
    }

    if ([[App sharedInstance] isInOnboardingFunnel]) {
        [DeeplinkHandler sharedInstance].deferredDeeplink = deeplink;
        return;
    }

    if (deeplink.loginToken.length > 0) {
        [self handleLoginWithDeeplink:deeplink];
        return;
    }

    if (deeplink.type == DeeplinkTypeCheckoutDelivery) {
        [DeeplinkHandler sharedInstance].deferredDeeplink = nil;
        [self goToDeliveryCheckoutScreenWithOrderNumber:deeplink.orderNumber
                                               platform:deeplink.platform];
        return;
    }

    if ([App sharedInstance].address == nil) {
        if ([LocationService.shared authorizationStatus] == kCLAuthorizationStatusNotDetermined) {
            [DeeplinkHandler sharedInstance].deferredDeeplink = deeplink;
            return;
        }

        [DeeplinkHandler sharedInstance].deferredDeeplink = deeplink;
        UIViewController *topViewController = [App topViewController];
        if ([topViewController isKindOfClass:[HomeViewController class]])  {
            [((HomeViewController *)topViewController) showLocationPrompt];
        }
        return;
    }

    // Store handler
    if ([deeplink needsStoreHandling]) {
        deeplink.hasSupplierOrStockLocation = YES;
        [[DeeplinkStoreHandler sharedInstance] handleStoreWithDeeplink:deeplink];
        return;
    } else if ((deeplink.type == DeeplinkTypePromotion || deeplink.type == DeeplinkTypeFlashSale) && !deeplink.promotionStoreChecked) {
        [[DeeplinkStoreHandler sharedInstance] handlePromotionDeeplink:deeplink];
        return;
    }

    [DeeplinkHandler sharedInstance].deferredDeeplink = nil;

    // Facebook
    if (deeplink.facebookId.length > 0) {
        [[Tracker sharedInstance] identifyFacebookID:deeplink.facebookId];
    }
    
    if (deeplink.type == DeeplinkTypeBestDeals ||
        deeplink.type == DeeplinkTypeProductDetail ||
        deeplink.type == DeeplinkTypeCategory ||
        deeplink.type == DeeplinkTypeInspirationDetail ||
        deeplink.type == DeeplinkTypePastItems ||
        deeplink.type == DeeplinkTypeLastOrder ||
        deeplink.type == DeeplinkTypeRecommendation ||
        deeplink.type == DeeplinkTypePromotion ||
        deeplink.type == DeeplinkTypeFlashSale) {
        
        if (!deeplink.storeSelected
            && !deeplink.hasSupplierOrStockLocation
            && deeplink.type != DeeplinkTypePromotion) {
            deeplink.storeSelected = YES;
            
            [DeeplinkHandler sharedInstance].deferredDeeplink = deeplink;
            //show store selection
            UIViewController *topViewController = [App topViewController];
            if (![topViewController isKindOfClass:[StoreSelectionViewController class]]) {
                [self.storeSelectionRouter presentFromViewController:topViewController
                                                     showCloseButton:YES
                                               popToRootAfterDismiss:YES
                                                     deliveryAddress:[App sharedInstance].address
                                                              source:deeplink.fromDeeplinkCampaignScreen ? ScreenNameCampaign : @"deeplink"];
            }
            return;
        } else {
            [self openStoreHome];
        }
    }

    switch (deeplink.type) {
        case DeeplinkTypeBestDeals:
            [self handleBestDealsWithSortID:deeplink.sortID
                 fromDeeplinkCampaignScreen:deeplink.fromDeeplinkCampaignScreen];
            break;
        case DeeplinkTypeCampaign:
            [self handleCampaignWithProductID:deeplink.productId
                                      promoID:deeplink.promotionId];
            break;
        case DeeplinkTypeCart:
            [self handleCart];
            break;
        case DeeplinkTypeInspirationDetail:
            [self handleInspirationDetailWithShoppingListID:deeplink.shoppingListId
                                                     source:deeplink.source];
            break;
        case DeeplinkTypeLastOrder:
            [self handleLastOrder];
            break;
        case DeeplinkTypeNewsfeed:
            [self handleNewsfeedWithNewsfeedId:deeplink.newsfeedId];
            break;
        case DeeplinkTypePopular:
            [[App sharedInstance] goToGlobalHomeTabWithCompletion:nil];
            break;
        case DeeplinkTypeProductDetail:
            [self handleProductDetailWithProductID:deeplink.productId
                                            source:(deeplink.source ?: ScreenNameHomeScreen)];
            break;
        case DeeplinkTypeRecommendation:
            [self handleRecommendedWithStockLocation:[App sharedInstance].stockLocation];
            break;
        case DeeplinkTypeCategory:
            [self handleCategoryWithTaxonID:deeplink.taxonId
                                     source:(deeplink.source ?: ScreenNameHomeScreen)
                 fromDeeplinkCampaignScreen:deeplink.fromDeeplinkCampaignScreen
                                     sortID:deeplink.sortID];
            break;
        case DeeplinkTypePastItems:
            [self handlePastItems];
            break;
        case DeeplinkTypeRefer:
            [self handleRefer];
            break;
        case DeeplinkTypeAuth:
            [self handleLoginWithDeeplink:deeplink];
            break;
        case DeeplinkTypePromotion:
            if (deeplink.promotionId > 0) {
                [self handlePromotion:deeplink];
            }
            break;
        case DeeplinkTypeCheckoutDelivery:
            [self goToDeliveryCheckoutScreenWithOrderNumber:deeplink.orderNumber
                                                   platform:deeplink.platform];
            break;
        case DeeplinkTypePaymentComplete:
            [self handleEWalletPaymentStatusWithOrderNumber:deeplink.orderNumber];
            break;
        case DeeplinkTypeLoyalty:
            [self handleLoyalty];
            break;
        case DeeplinkTypeStore:
            [self openStoreHome];
            break;
        case DeeplinkTypeOrderDetail:
            [self handleOrderDetailWithOrderNumber:deeplink.orderNumber];
            break;
        case DeeplinkTypeFlashSale:
            if (deeplink.newsfeedId.length > 0) {
                [self handleFlashSaleWithNewsfeedId:deeplink.newsfeedId];
            }
            break;
        case DeeplinkTypeSubscriptionLandingPage:
            [self handleSubscriptionLandingPageWithIsFromExpired:NO];
        case DeeplinkTypeWebView:
            [self handleWebViewWithTitle:deeplink.title
                               urlString:deeplink.url];
        case DeeplinkTypeUnknown:
            break;
    }
}

#pragma mark - Handlers

- (void)handleBestDealsWithSortID:(SortID)sortID
       fromDeeplinkCampaignScreen: (BOOL)fromDeeplinkCampaignScreen {
    [App sharedInstance].lastSortID = @(sortID);
    
    BestDealsConfig *config = [[BestDealsConfig alloc] initWithPromoFilter:[PromoFilterService shared].promoFilters.firstObject
                                                      stockLocation:[App sharedInstance].stockLocation
                                                              taxon:nil
                                                               rank:0
                                                             source:ScreenNameStoreHome
                               fromDeeplinkCampaignScreen:fromDeeplinkCampaignScreen
                                                fromProductShowcase:false];
    BestDealsRouter *router = [[BestDealsRouter alloc] initWithConfig:config];
    [(CategoryViewController *)router.view setLeftBarButtonItemTypes:BarButtonItemTypeClose];
    UINavigationController *navigationController = [[UINavigationController alloc] initWithRootViewController:router.view];
    [[App topViewController] presentViewController:navigationController
                                          animated:YES
                                        completion:nil];
}

- (void)handleCart {
    [SVProgressHUD show];
    [[App sharedInstance] refreshOrdersWithOneOrderLimit:YES
                                                 success:^(NSArray<Order *> * _Nonnull orders) {
        [SVProgressHUD dismiss];
        Order *order = [orders firstObject];
        [App sharedInstance].order = order;

        UIViewController *vc = [App topViewController];
        [vc showCartWithScreenName:nil order:order type:CartScreenTypeCurrent position:1];
    } failure:^(AFHTTPRequestOperation *operation, NSError *error) {
        [SVProgressHUD dismiss];
    }];
}

#pragma mark - Category

- (void)handleCategoryWithTaxonID:(NSInteger)taxonID
                         source:(NSString *)source
       fromDeeplinkCampaignScreen:(BOOL)fromDeeplinkCampaignScreen
                           sortID:(SortID)sortID {
    if (taxonID == 0) {
        return;
    }

    [App sharedInstance].lastSortID = @(sortID);
    Taxon *cachedTaxon = [Taxon getTaxonWithTaxonID:taxonID];
    StockLocation *currentStockLocation = [App sharedInstance].stockLocation;

    if (cachedTaxon == nil) {
        [self getTaxonWithTaxonID:taxonID
                  stockLocationID:currentStockLocation.stockLocationID
                           source:source
       fromDeeplinkCampaignScreen:fromDeeplinkCampaignScreen];
    } else if ([currentStockLocation.taxonIDs containsObject:cachedTaxon.taxonID]) {
        [self showCategoryWithTaxon:cachedTaxon
                             source:source
         fromDeeplinkCampaignScreen:fromDeeplinkCampaignScreen];
    } else {
        [self getTaxonWithTaxonID:taxonID
                  stockLocationID:currentStockLocation.stockLocationID
                           source:source
       fromDeeplinkCampaignScreen:fromDeeplinkCampaignScreen];
    }
}

- (void)getTaxonWithTaxonID:(NSInteger)taxonID
            stockLocationID:(NSNumber *)stockLocationID
                     source:(NSString *)source
 fromDeeplinkCampaignScreen:(BOOL)fromDeeplinkCampaignScreen {
    [SVProgressHUD show];

    @weakify(self);
    [[TaxonomyService sharedInstance] getTaxonWithTaxonID:taxonID
                                          stockLocationID:stockLocationID
                                               completion:^(Taxon *taxon, NSArray<Taxon *> *taxons) {
        @strongify(self);
        [SVProgressHUD dismiss];

        if (taxon) {
            [self showCategoryWithTaxon:taxon
                                 source:source
             fromDeeplinkCampaignScreen:fromDeeplinkCampaignScreen];
        } else if (taxons.count > 0){
            [self openCategoryListScreen];
        }
    }];
}

- (void)showCategoryWithTaxon:(Taxon *)taxon
                       source:(NSString *)source
   fromDeeplinkCampaignScreen:(BOOL)fromDeeplinkCampaignScreen {
    [self.categoryRouter configureWithTaxon:taxon
                                productType:nil
                                       rank:0
                                     source:source
                                  isSpecial:NO
                 fromDeeplinkCampaignScreen:fromDeeplinkCampaignScreen];
    [self.categoryRouter presentFromViewController:[App topViewController]
                                          animated:YES];
}

#pragma mark - Product Detail

- (void)handleProductDetailWithProductID:(NSInteger)productID
                                  source:(NSString *)source {
    if (productID == 0) {
        return;
    }
    
    if ([[Tracker sharedInstance].source isEqualToString:ScreenNameCampaign]) {
        source = [Tracker sharedInstance].source;
    }
    StockLocation *stockLocation = [App sharedInstance].stockLocation;

    @weakify(self);
    [[API sharedInstance] getProductWithStockLocation:stockLocation
                                            productID:@(productID)
                                              success:^(AFHTTPRequestOperation *operation, Product *product) {
                                                  [SVProgressHUD dismiss];

                                                  @strongify(self);
                                                  [self showProductDetailWith:stockLocation product:product source:source];
                                              } failure:^(AFHTTPRequestOperation *operation, NSError *error) {
                                                  [SVProgressHUD dismiss];
                                              }];
    [SVProgressHUD show];
}

#pragma mark - Inspiration Detail

- (void)handleInspirationDetailWithShoppingListID:(NSInteger)shoppingListID
                                           source:(NSString *)source {
    if (shoppingListID == 0) {
        return;
    }
    
    [self openRecipeDetailWithRecipeID:[NSNumber numberWithInteger:shoppingListID]
                                source:source];
}

#pragma mark - Last Order

- (void)handleLastOrder {
    if (!UserService.shared.isLoggedIn) {
        return;
    }

    [SVProgressHUD show];

    [[OrderAPI sharedInstance] getLastReceivedOnSuccess:^(Order *order) {
        NSInteger orderStockLocationID = order.currentStockLocation.stockLocationID.integerValue;
        StockLocation *currentStockLocation = [StockLocationService sharedInstance].stockLocation;

        if (orderStockLocationID == currentStockLocation.stockLocationID.integerValue || orderStockLocationID <= 0) {
            [SVProgressHUD dismiss];
            [self showPastItemsViewControllerWithOrder:order];
        } else {
            [[StockLocationService sharedInstance] getStockLocationDetailWithID:orderStockLocationID
                                                                     completion:^(StockLocation *stockLocation) {
                [SVProgressHUD dismiss];
                if (stockLocation == nil) {
                    return;
                }

                [[StockLocationSwitchService sharedInstance] switchToStockLocation:stockLocation
                                                                            reload:YES
                                                                        completion:^(StockLocation *stockLocation) {
                    [self showPastItemsViewControllerWithOrder:order];
                }];
            }];
        }
    } failure:^(NSString *errorMessage) {
        [SVProgressHUD dismiss];
    }];
}

- (void)handlePastItems {
    if (!UserService.shared.isLoggedIn) {
        return;
    }

    LegacyPastItemsViewController *pastItemsVC = [[LegacyPastItemsViewController alloc] initPastItemsWithSourceScreen:nil stockLocation:[App sharedInstance].stockLocation];
    pastItemsVC.leftBarButtonItemTypes = BarButtonItemTypeClose;
    UINavigationController *navigationController = [[UINavigationController alloc] initWithRootViewController:pastItemsVC];
    [[App topViewController] presentViewController:navigationController
                                          animated:YES
                                        completion:nil];
}

- (void)showPastItemsViewControllerWithOrder:(Order *)order {
    LegacyPastItemsViewController *pastItemVC = [[LegacyPastItemsViewController alloc] initWithOrder:order
                                                                            sourceScreen:nil];
    pastItemVC.leftBarButtonItemTypes = BarButtonItemTypeClose;
    UINavigationController *navigationController = [[UINavigationController alloc] initWithRootViewController:pastItemVC];
    [[App topViewController] presentViewController:navigationController
                                          animated:YES
                                        completion:nil];
}

#pragma mark - Recommended

- (void)handleRecommendedWithStockLocation:(StockLocation *)stockLocation {
    if (stockLocation == nil) {
        InspirationListRouter *inspirationListRouter = [InspirationListRouter new];
        inspirationListRouter.source = EventPropertyInspirationDetailSourceDeeplink;
        InspirationListViewController *vc = (InspirationListViewController *)inspirationListRouter.view;
        vc.leftBarButtonItemTypes = BarButtonItemTypeClose;
        UINavigationController *navigationController = [[UINavigationController alloc] initWithRootViewController:vc];

        [[App topViewController] presentViewController:navigationController
                                              animated:YES
                                            completion:nil];
        return;
    }

    ShoppingListsViewController *vc = [ShoppingListsViewController createInstance];
    vc.leftBarButtonItemTypes = BarButtonItemTypeClose;
    vc.type = ShoppingListTypeInspirations;
    UINavigationController *navigationController = [[UINavigationController alloc] initWithRootViewController:vc];

    [[App topViewController] presentViewController:navigationController
                                          animated:YES
                                        completion:nil];
}

#pragma mark - Deeplink payment status
- (void)handleEWalletPaymentStatusWithOrderNumber:(NSString *)orderNumber {
    [SVProgressHUD show];
    [[App sharedInstance].api getOrderWithNumber:orderNumber
                                      hideBundle:YES
                                           token:nil
                                         success:^(AFHTTPRequestOperation *operation, Order *order) {
        if (order.isOrderCompleted) {
            [SVProgressHUD dismiss];
            [self goToEWalletOrderCompleteScreenWithOrder:order];
        } else {
            [self completeEWalletPaymentWithOrder:order];
        }
    } failure:^(AFHTTPRequestOperation *operation, NSError *error) {
        [SVProgressHUD dismiss];
        [AlertController showErrorWithMessage:[operation errorMessage]];
    }];
}

#pragma mark - Auth

- (void)handleLoginWithDeeplink:(Deeplink *)deeplink {
    NSString *loginToken = [deeplink.loginToken copy];

    if (loginToken.length == 0) {
        return;
    }

    [deeplink resetLoginToken];
    UserModel *user = UserService.shared.userModel;

    if (user != nil) {
        NSString *text = [NSString stringWithFormat:NSLocalizedString(@"You're already logged in as %@.", nil), user.fullName];
        [[BannerManager sharedInstance] showBannerWithText:text userInfo:nil];
        [self handleDeeplink:deeplink];
        return;
    }

    [SVProgressHUD show];

    @weakify(self);
    [[AuthService sharedInstance] loginWithToken:loginToken
                                         success:^(User *user) {
        [SVProgressHUD dismiss];
        UserModel *userModel = UserService.shared.userModel;
        NSString *text = [NSString stringWithFormat:NSLocalizedString(@"Welcome %@!", nil), userModel.fullName];
        [[BannerManager sharedInstance] showBannerWithText:text
                                                  userInfo:nil];
        [[Tracker sharedInstance] trackLogInWithType:EventPropertyAuthTypeDeeplink
                                              source:SourceDeeplink
                                  verificationMethod:nil
                                             success:YES
                                           errorType:nil];

        @strongify(self);
        [self handleDeeplink:deeplink];
    } failure:^(NSString *errorMessage, NSString *errorType) {
        [SVProgressHUD dismiss];
        @strongify(self);
        [self showInvalidLoginTokenAlert];
        [[Tracker sharedInstance] trackLogInWithType:EventPropertyAuthTypeDeeplink
                                              source:SourceDeeplink
                                  verificationMethod:nil
                                             success:NO
                                           errorType:errorType];
    }];
}

- (void)showInvalidLoginTokenAlert {
    NSString *title = NSLocalizedString(@"Your authentication link is not valid", nil);
    NSString *message = NSLocalizedString(@"Do you want to login manually?", nil);

    AlertController *alert = [[AlertController alloc] initWithTitle:title
                                                            message:message
                                                              image:nil];
    @weakify(self);
    AlertAction *loginAction = [AlertAction actionWithTitle:NSLocalizedString(@"Login", nil)
                                                      style:AlertActionStyleNeutral
                                                    handler:^{
        @strongify(self);
        [self showLoginScreen];
    }];
    [alert setupPrimaryAction:loginAction];
    [alert setupSecondaryActionTitle:NSLocalizedString(@"Cancel", nil)];
    [alert show];
}

- (void)showLoginScreen {
    LoginViewController *loginVC = [LoginViewController createInstance];
    loginVC.source = @"deeplink";

    [[App topViewController].navigationController pushViewController:loginVC
                                                            animated:YES];
}

#pragma mark - Promotion

- (void)handlePromotion:(Deeplink *)deeplink {
    PromotionAPI *api = [PromotionAPI new];

    [SVProgressHUD show];
    @weakify(self);
    NSNumber *promotionID = [NSNumber numberWithInteger: deeplink.promotionId];
    [api getPromotionDetailWithID:promotionID
                          success:^(NSString *displayName, NSArray<NSNumber *> *stockLocationIDs, NSArray<NSNumber *> *supplierIDs) {
                              [SVProgressHUD dismiss];

                              NSNumber *currentStockLocationID = [StockLocationService sharedInstance].stockLocation.stockLocationID;

                              if (stockLocationIDs.count > 0 && ![stockLocationIDs containsObject:currentStockLocationID]) {
                                  @strongify(self);
                                  [self showPromotionOutOfCoverageAlert];
                                  return;
                              }
                              
                              [self.categoryRouter configureWithDeeplink:deeplink
                                                       promotionDisplayName:displayName];
                              [self.categoryRouter presentFromViewController:[App topViewController]
                                                                    animated:YES];
                          } failure:^(NSString *errorMessage) {
                              [SVProgressHUD dismiss];
                              [AlertController showErrorWithMessage:errorMessage];
                          }];
}

- (void)showPromotionOutOfCoverageAlert {
    AlertController *alert = [AlertController alertWithTitle:NSLocalizedString(@"This promotion is only eligible in certain stores", nil)
                                                     message:NSLocalizedString(@"We cannot find such store nearby your delivery address.", nil)
                                                      action:[AlertAction okActionWithStyle:AlertActionStyleNeutral handler:nil]];
    [alert show];
}

- (void)openStoreHome {
    StockLocation *targetStockLocation = [App sharedInstance].stockLocation;
    UIViewController *topViewController = [App topViewController];

    if ([topViewController isKindOfStoreHome]) {
        return;
    }

    [self openStoreHomeWithStockLocation:targetStockLocation
                    navigationController:topViewController.navigationController];

}

- (void)handleLoyalty {
    [[App sharedInstance] goToGlobalHomeTabWithCompletion:^{
        UIViewController *topViewController = [App topViewController];
        if ([topViewController isKindOfClass:[HomeViewController class]])  {
            [((HomeViewController *)topViewController) handleOpenLoyaltyWebView];
        }
    }];
}

- (void)openStoreHomeWithStockLocation:(StockLocation *)stockLocation
                  navigationController:(UINavigationController *)navController {
    if (stockLocation == nil) {
        return;
    }
    
    UIViewController *presentingVC = navController.presentingViewController;
    if ([presentingVC isKindOfClass:[UINavigationController class]]) {
        UINavigationController *navVC = (UINavigationController *)presentingVC;
        [self popToStoreHomeWithStockLocation:stockLocation navigationController:navVC];
    } else {
        [self goToStoreHomeWithStockLocation:stockLocation navigationController:navController];
    }
}

- (void)goToDeliveryCheckoutScreenWithOrderNumber:(NSString *)orderNumber
                                         platform:(NSString *)platform {
    if (orderNumber.length == 0) {
        return;
    }

    [SVProgressHUD show];
    [[App sharedInstance].api getOrderWithNumber:orderNumber
                                      hideBundle:YES
                                           token:nil
                                         success:^(AFHTTPRequestOperation *operation, Order *order) {
        [SVProgressHUD dismiss];

        if (order.shipAddress != nil) {
            [App sharedInstance].address = order.shipAddress;
        }

        [[App sharedInstance] forceReloadAppFromBeginningWithCompletion:^{
            UIViewController *topViewController = [App topViewController];
            
            CartViewController *cartViewController = [topViewController getCartScreenWithScreenName:nil order:order type:CartScreenTypePending position:1];
            UINavigationController *cartNavigationController = [[UINavigationController alloc] initWithRootViewController:cartViewController];

            if (@available(iOS 13, *)) {
                cartNavigationController.modalPresentationStyle = UIModalPresentationFullScreen;
            }
            
            [topViewController presentViewController:cartNavigationController animated:YES completion:^{
                [cartViewController goToDeliveryWithSource:SourceDeeplink clientType:platform];
            }];
        }];
    } failure:^(AFHTTPRequestOperation *operation, NSError *error) {
        [SVProgressHUD dismiss];
        [AlertController showErrorWithMessage:[operation errorMessage]];
    }];
}

- (void)completeEWalletPaymentWithOrder:(Order *)order {
    [[App sharedInstance].api completeEWalletPaymentWithOrderNumber:order.number
                                                         orderToken:order.token
                                                       fetchSuccess:^(Order *_order) {
        [SVProgressHUD dismiss];
        if (_order == nil) {
            [self handleEWalletPaymentStatusWithOrderNumber:order.number];
        } else {
            if (order.isOrderCompleted) {
                [self goToEWalletOrderCompleteScreenWithOrder:order];
            } else {
                [self handleEWalletPaymentStatusWithOrderNumber:order.number];
            }
        }
    } fetchFailure:^(NSInteger statusCode, NSString *errorMessage) {
        if (retryCompleteEWalletPaymentCallIndex > MaximumCompleteEWalletPaymentRetryCall) {
            retryCompleteEWalletPaymentCallIndex = 0;
            [SVProgressHUD dismiss];
            [AlertController showErrorWithMessage:errorMessage];
            return;
        }
        double delayInSeconds = 5;
        dispatch_time_t popTime = dispatch_time(DISPATCH_TIME_NOW, (int64_t)(delayInSeconds * NSEC_PER_SEC));
        dispatch_after(popTime, dispatch_get_main_queue(), ^(void){
            [self completeEWalletPaymentWithOrder:order];
            retryCompleteEWalletPaymentCallIndex = retryCompleteEWalletPaymentCallIndex + 1;
        });
    }];
}

@end
