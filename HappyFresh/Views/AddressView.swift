//
//  HomeAddressView.swift
//  HappyFresh
//
//  Created by <PERSON> on 4/19/22.
//  Copyright © 2022 HappyFresh Inc. All rights reserved.
//

import Foundation
import ReactiveSwift

protocol AddressViewDelegate: AnyObject {
    func viewTapped()
    func notificationTapped()
}

class AddressView: UIView {
    
    // MARK: - Properties
    weak var delegate: AddressViewDelegate?
    
    private var disposables = CompositeDisposable()
    private var viewModel: AddressViewModel?
    private var notificationViewModel: NotificationViewModel?
    
    var addressLabel: UILabel = {
        let addressLabel = UILabel(frame: .zero)
        addressLabel.text = NSLocalizedString("Deliver to", comment: "")
        addressLabel.font = .hf_small_bold()
        addressLabel.textColor = .hf_grey_100()
        
        return addressLabel
    }()
    
    private let notificationButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage(named: "hf_icon_notification_outline_new"), for: .normal)
        button.contentMode = .center
        return button
    }()

    // Public getter for coach mark targeting
    var notificationButtonView: UIButton {
        return notificationButton
    }
    
    private let unreadCountLabel: UILabel = {
        let label = UILabel()
        label.font = .hf_micro()
        label.textColor = .white
        label.textAlignment = .center
        label.backgroundColor = .hf_error_100()
        label.layer.masksToBounds = true
        label.isHidden = true
        label.translatesAutoresizingMaskIntoConstraints = false
        return label
    }()
    
    var hasNotification: Bool = false {
        didSet {
            updateNotificationDisplay()
        }
    }
    
    private var unreadCount: Int = 0 {
        didSet {
            updateNotificationDisplay()
        }
    }
    
    override var intrinsicContentSize: CGSize {
        return UIView.layoutFittingExpandedSize
    }
    
    // MARK: - Init
    
    init(viewModel: AddressViewModel = AddressViewModel(), notificationViewModel: NotificationViewModel = NotificationViewModel()) {
        super.init(frame: .zero)
        self.viewModel = viewModel
        self.notificationViewModel = notificationViewModel
        self.commonInit()
        self.setupObserver()
        self.setupNotificationObserver()
    }
    
    required init?(coder aDecoder: NSCoder) {
        fatalError("This class does not support NSCoding")
    }
    
    deinit {
        disposables.dispose()
    }
    
    private func commonInit() {
        snp.makeConstraints { make in
            make.height.equalTo(32)
        }
        
        let stackView = UIStackView(frame: .zero)
        stackView.distribution = .fill
        stackView.axis = .horizontal
        stackView.spacing = 8
        
        addSubview(stackView)
        stackView.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview()
            make.leading.trailing.equalToSuperview().inset(10)
        }
        
        let icon = UIImageView(image: UIImage(named: "hf_icon_location_20px_orange"))
        icon.contentMode = .center
        
        stackView.addArrangedSubview(icon)
        icon.snp.makeConstraints { make in
            make.width.equalTo(12)
        }
        
        stackView.addArrangedSubview(addressLabel)
        
        let chevron = UIImageView(image: UIImage(named: "hf_icon_chevron_down_dark_16px"))
        chevron.contentMode = .center
        
        stackView.addArrangedSubview(chevron)
        chevron.snp.makeConstraints { make in
            make.width.equalTo(12)
        }
        
        // Add notification button container
        let notificationContainer = UIView()
        stackView.addArrangedSubview(notificationContainer)
        notificationContainer.snp.makeConstraints { make in
            make.width.equalTo(28)
        }
        
        // Add notification button
        notificationContainer.addSubview(notificationButton)
        notificationButton.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.height.equalTo(24)
        }
        
        // Add unread count label with circular background
        notificationContainer.addSubview(unreadCountLabel)
        unreadCountLabel.snp.makeConstraints { make in
            make.top.equalTo(notificationButton.snp.top).offset(-2)
            make.right.equalTo(notificationButton.snp.right).offset(2)
            make.width.height.greaterThanOrEqualTo(16)
        }
        
        // Add notification button action
        notificationButton.addTarget(self, action: #selector(onNotificationTapped), for: .touchUpInside)

        /// For development purpose only:
        /// check whether the app store rating widget can be shown on the build.
#if DEVELOPMENT
        let appStoreRatingTapGesture = UITapGestureRecognizer(target: self, action: #selector(showAppStoreRatingWidget))
        appStoreRatingTapGesture.numberOfTouchesRequired = 2
        appStoreRatingTapGesture.numberOfTapsRequired = 2
        addGestureRecognizer(appStoreRatingTapGesture)
#endif

        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(onTapped))
        addGestureRecognizer(tapGesture)
    }
    
    // MARK: - Functions
    private func setupObserver() {
        guard let viewModel = viewModel else { return }
        
        disposables += viewModel.address.producer.startWithValues { [weak self] address in
            guard let self = self else { return }
            self.addressLabel.text = address
        }
    }
    
    private func setupNotificationObserver() {
        guard let notificationViewModel = notificationViewModel else { return }
        
        // Observe unread notifications count
        disposables += notificationViewModel.unreadNotificationsCount.observeValues { [weak self] count in
            DispatchQueue.main.async {
                self?.unreadCount = count
            }
        }
        
        // Initial refresh to get current notification state
        notificationViewModel.refresh()
    }
    
    private func updateNotificationDisplay() {
        if unreadCount > 0 {
            // Show count label
            unreadCountLabel.isHidden = false
            
            // Update count text
            if unreadCount > 99 {
                unreadCountLabel.text = "99+"
            } else {
                unreadCountLabel.text = "\(unreadCount)"
            }
            
            // Update size and corner radius to make it circular
            DispatchQueue.main.async { [weak self] in
                guard let self = self else { return }
                self.unreadCountLabel.layoutIfNeeded()
                
                let text = self.unreadCountLabel.text ?? ""
                let size = text.count > 1 ? 20 : 16 // Larger for 2+ digits
                
                self.unreadCountLabel.snp.remakeConstraints { make in
                    make.top.equalTo(self.notificationButton.snp.top).offset(-2)
                    make.right.equalTo(self.notificationButton.snp.right).offset(2)
                    make.width.height.equalTo(size)
                }
                
                // Make it circular
                self.unreadCountLabel.layer.cornerRadius = CGFloat(size) / 2.0
                self.unreadCountLabel.layoutIfNeeded()
            }
            
        } else {
            // Hide count label when no unread notifications
            unreadCountLabel.isHidden = true
        }
    }
    
    @objc private func onTapped() {
        delegate?.viewTapped()
    }
    
    @objc private func onNotificationTapped() {
        delegate?.notificationTapped()
    }

    @objc private func showAppStoreRatingWidget() {
        AppStoreRatingService.shared.countOrderRating(
            5,
            showAppStoreRatingWidgetIfNecessary: true
        )
    }
}
