<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="21701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21678"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" reuseIdentifier="VideoReelCell" id="gTV-IL-0wX" customClass="VideoReelCell" customModule="HappyFresh" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="8bC-Xf-vdC" userLabel="Player Container">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <color key="backgroundColor" systemColor="blackColor"/>
                    </view>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="N6d-SI-hnr" userLabel="Overlay View">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <subviews>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Ze4-5a-6hd" userLabel="Play Pause Button">
                                <rect key="frame" x="171.66666666666666" y="401" width="50" height="50"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="50" id="8bh-gX-fhc"/>
                                    <constraint firstAttribute="height" constant="50" id="Qom-aY-8Nh"/>
                                </constraints>
                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                <state key="normal" image="play.fill" catalog="system">
                                    <color key="titleColor" systemColor="whiteColor"/>
                                </state>
                            </button>
                            <activityIndicatorView opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="750" verticalHuggingPriority="750" style="large" translatesAutoresizingMaskIntoConstraints="NO" id="fNZ-8a-g6I">
                                <rect key="frame" x="178" y="407.66666666666669" width="37" height="37"/>
                                <color key="color" systemColor="whiteColor"/>
                            </activityIndicatorView>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="leading" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="Bh7-fA-2Jr" userLabel="Content Stack">
                                <rect key="frame" x="16" y="652" width="361" height="166"/>
                                <subviews>
                                    <stackView opaque="NO" contentMode="scaleToFill" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="Xgf-gd-Yzg" userLabel="Stats Stack">
                                        <rect key="frame" x="0.0" y="0.0" width="361" height="20.333333333333332"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="❤️ 1.2K" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Zab-0a-fhd" userLabel="Likes Label">
                                                <rect key="frame" x="0.0" y="0.0" width="45.333333333333336" height="20.333333333333332"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                                <color key="textColor" systemColor="whiteColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="👁 15.6K" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Ztg-he-fhd" userLabel="Views Label">
                                                <rect key="frame" x="57.333333333333336" y="0.0" width="48" height="20.333333333333332"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                                <color key="textColor" systemColor="whiteColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <view contentMode="scaleToFill" horizontalHuggingPriority="249" translatesAutoresizingMaskIntoConstraints="NO" id="fhd-gd-Yzg" userLabel="Spacer">
                                                <rect key="frame" x="117.33333333333333" y="0.0" width="243.66666666666669" height="20.333333333333332"/>
                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            </view>
                                        </subviews>
                                    </stackView>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Ebifurai Saus Keju" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="5gN-32-tvb" userLabel="Title Label">
                                        <rect key="frame" x="0.0" y="28.333333333333314" width="361" height="21.666666666666671"/>
                                        <fontDescription key="fontDescription" type="boldSystem" pointSize="18"/>
                                        <color key="textColor" systemColor="whiteColor"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Crispy fried shrimp with creamy cheese sauce - perfect for lunch!" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="3" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="BIe-4a-9rv" userLabel="Description Label">
                                        <rect key="frame" x="0.0" y="58" width="361" height="50.333333333333343"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                        <color key="textColor" systemColor="whiteColor"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="by Chef Maria" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Ztg-he-fhd" userLabel="Author Label">
                                        <rect key="frame" x="0.0" y="116.33333333333334" width="361" height="16"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                        <color key="textColor" red="1" green="1" blue="1" alpha="0.80000000000000004" colorSpace="custom" customColorSpace="sRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <stackView opaque="NO" contentMode="scaleToFill" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="fhd-gd-Yzg" userLabel="Info Stack">
                                        <rect key="frame" x="0.0" y="140.33333333333334" width="361" height="16"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="⏱ 30 mins" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Bh7-fA-2Jr" userLabel="Cooking Time Label">
                                                <rect key="frame" x="0.0" y="0.0" width="60" height="16"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                                <color key="textColor" systemColor="whiteColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Medium" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="N6d-SI-hnr" userLabel="Difficulty Label">
                                                <rect key="frame" x="76" y="0.0" width="48" height="16"/>
                                                <fontDescription key="fontDescription" type="boldSystem" pointSize="12"/>
                                                <color key="textColor" systemColor="systemOrangeColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="🍽️ Lunch" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="8bC-Xf-vdC" userLabel="Category Label">
                                                <rect key="frame" x="140" y="0.0" width="221" height="16"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                                <color key="textColor" systemColor="whiteColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                    </stackView>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="2" translatesAutoresizingMaskIntoConstraints="NO" id="Ze4-5a-6hd" userLabel="Ingredients Stack">
                                        <rect key="frame" x="0.0" y="164.33333333333334" width="361" height="1.6666666666666572"/>
                                    </stackView>
                                </subviews>
                                <constraints>
                                    <constraint firstAttribute="trailing" secondItem="Xgf-gd-Yzg" secondAttribute="trailing" id="2Jr-fA-Bh7"/>
                                    <constraint firstAttribute="trailing" secondItem="5gN-32-tvb" secondAttribute="trailing" id="4a9-rv-BIe"/>
                                    <constraint firstAttribute="trailing" secondItem="BIe-4a-9rv" secondAttribute="trailing" id="6hd-5a-Ze4"/>
                                    <constraint firstAttribute="trailing" secondItem="Ztg-he-fhd" secondAttribute="trailing" id="8Xf-vd-8bC"/>
                                    <constraint firstAttribute="trailing" secondItem="fhd-gd-Yzg" secondAttribute="trailing" id="Yzg-gd-fhd"/>
                                    <constraint firstAttribute="trailing" secondItem="Ze4-5a-6hd" secondAttribute="trailing" id="fA2-Jr-Bh7"/>
                                </constraints>
                            </stackView>
                        </subviews>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="Ze4-5a-6hd" firstAttribute="centerX" secondItem="N6d-SI-hnr" secondAttribute="centerX" id="2Jr-Bh7-fA"/>
                            <constraint firstItem="Ze4-5a-6hd" firstAttribute="centerY" secondItem="N6d-SI-hnr" secondAttribute="centerY" id="4a9-BIe-rv"/>
                            <constraint firstItem="fNZ-8a-g6I" firstAttribute="centerX" secondItem="N6d-SI-hnr" secondAttribute="centerX" id="6hd-Ze4-5a"/>
                            <constraint firstItem="fNZ-8a-g6I" firstAttribute="centerY" secondItem="N6d-SI-hnr" secondAttribute="centerY" id="8Xf-8bC-vd"/>
                            <constraint firstAttribute="bottom" secondItem="Bh7-fA-2Jr" secondAttribute="bottom" constant="34" id="Bh7-2Jr-fA"/>
                            <constraint firstAttribute="trailing" secondItem="Bh7-fA-2Jr" secondAttribute="trailing" constant="16" id="SI-hnr-N6d"/>
                            <constraint firstItem="Bh7-fA-2Jr" firstAttribute="leading" secondItem="N6d-SI-hnr" secondAttribute="leading" constant="16" id="vdC-Xf-8b"/>
                        </constraints>
                    </view>
                </subviews>
            </view>
            <viewLayoutMarginsGuide key="safeArea" id="ZTg-uK-7eu"/>
            <constraints>
                <constraint firstItem="8bC-Xf-vdC" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="0wX-IL-gTV"/>
                <constraint firstAttribute="trailing" secondItem="8bC-Xf-vdC" secondAttribute="trailing" id="IL-0wX-gTV"/>
                <constraint firstItem="8bC-Xf-vdC" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" id="Xf-vdC-8bC"/>
                <constraint firstAttribute="bottom" secondItem="8bC-Xf-vdC" secondAttribute="bottom" id="gTV-0wX-IL"/>
                <constraint firstItem="N6d-SI-hnr" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="hnr-SI-N6d"/>
                <constraint firstAttribute="trailing" secondItem="N6d-SI-hnr" secondAttribute="trailing" id="N6d-hnr-SI"/>
                <constraint firstItem="N6d-SI-hnr" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" id="SI-N6d-hnr"/>
                <constraint firstAttribute="bottom" secondItem="N6d-SI-hnr" secondAttribute="bottom" id="wX-gTV-IL"/>
            </constraints>
            <size key="customSize" width="393" height="852"/>
            <connections>
                <outlet property="authorLabel" destination="Ztg-he-fhd" id="fhd-he-Ztg"/>
                <outlet property="categoryLabel" destination="8bC-Xf-vdC" id="vdC-Xf-8bC"/>
                <outlet property="cookingTimeLabel" destination="Bh7-fA-2Jr" id="2Jr-fA-Bh7"/>
                <outlet property="descriptionLabel" destination="BIe-4a-9rv" id="9rv-4a-BIe"/>
                <outlet property="difficultyLabel" destination="N6d-SI-hnr" id="hnr-SI-N6d"/>
                <outlet property="ingredientsStackView" destination="Ze4-5a-6hd" id="6hd-5a-Ze4"/>
                <outlet property="likesLabel" destination="Zab-0a-fhd" id="fhd-0a-Zab"/>
                <outlet property="loadingIndicator" destination="fNZ-8a-g6I" id="g6I-8a-fNZ"/>
                <outlet property="overlayView" destination="N6d-SI-hnr" id="N6d-hnr-SI"/>
                <outlet property="playPauseButton" destination="Ze4-5a-6hd" id="Ze4-6hd-5a"/>
                <outlet property="playerContainerView" destination="8bC-Xf-vdC" id="8bC-vdC-Xf"/>
                <outlet property="titleLabel" destination="5gN-32-tvb" id="tvb-32-5gN"/>
                <outlet property="viewsLabel" destination="Ztg-he-fhd" id="Ztg-fhd-he"/>
            </connections>
        </collectionViewCell>
    </objects>
    <resources>
        <image name="play.fill" catalog="system" width="116" height="128"/>
        <systemColor name="blackColor">
            <color white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
        <systemColor name="systemOrangeColor">
            <color red="1" green="0.58431372549019611" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
        <systemColor name="whiteColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
