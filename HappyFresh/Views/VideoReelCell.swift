//
//  VideoReelCell.swift
//  HappyFresh
//
//  Created by Augment Agent on 2025-07-07.
//

import UIKit
import AVFoundation

class VideoReelCell: UICollectionViewCell {
    
    // MARK: - IBOutlets
    @IBOutlet weak var playerContainerView: UIView!
    @IBOutlet weak var overlayView: UIView!
    @IBOutlet weak var titleLabel: UILabel!
    @IBOutlet weak var descriptionLabel: UILabel!
    @IBOutlet weak var authorLabel: UILabel!
    @IBOutlet weak var cookingTimeLabel: UILabel!
    @IBOutlet weak var difficultyLabel: UILabel!
    @IBOutlet weak var categoryLabel: UILabel!
    @IBOutlet weak var likesLabel: UILabel!
    @IBOutlet weak var viewsLabel: UILabel!
    @IBOutlet weak var playPauseButton: UIButton!
    @IBOutlet weak var loadingIndicator: UIActivityIndicatorView!
    @IBOutlet weak var ingredientsStackView: UIStackView!
    
    // MARK: - Properties
    private var player: AVPlayer?
    private var playerLayer: AVPlayerLayer?
    private var videoReel: VideoReel?
    private var isConfigured = false
    
    // MARK: - Lifecycle
    override func awakeFromNib() {
        super.awakeFromNib()
        setupUI()
    }
    
    override func prepareForReuse() {
        super.prepareForReuse()
        cleanup()
    }
    
    override func layoutSubviews() {
        super.layoutSubviews()
        playerLayer?.frame = playerContainerView.bounds
    }
    
    // MARK: - Configuration
    func configure(with videoReel: VideoReel) {
        self.videoReel = videoReel
        
        // Update UI with video reel data
        titleLabel.text = videoReel.title
        descriptionLabel.text = videoReel.description
        authorLabel.text = "by \(videoReel.author)"
        cookingTimeLabel.text = "⏱ \(videoReel.cookingTime)"
        difficultyLabel.text = videoReel.difficulty.rawValue
        difficultyLabel.textColor = videoReel.difficulty.color
        categoryLabel.text = "\(videoReel.category.icon) \(videoReel.category.rawValue)"
        likesLabel.text = "❤️ \(formatNumber(videoReel.likes))"
        viewsLabel.text = "👁 \(formatNumber(videoReel.views))"
        
        // Setup ingredients
        setupIngredients(videoReel.ingredients)
        
        // Setup video player
        setupVideoPlayer(with: videoReel.videoURL)
        
        isConfigured = true
    }
    
    // MARK: - Video Player Setup
    private func setupVideoPlayer(with url: URL) {
        // Show loading indicator
        loadingIndicator.startAnimating()
        playPauseButton.isHidden = true
        
        // Get player from video manager
        player = VideoManager.shared.getPlayer(for: url)
        
        // Setup player layer
        setupPlayerLayer()
        
        // Add observers
        addPlayerObservers()
    }
    
    private func setupPlayerLayer() {
        guard let player = player else { return }
        
        // Remove existing layer
        playerLayer?.removeFromSuperlayer()
        
        // Create new player layer
        playerLayer = AVPlayerLayer(player: player)
        playerLayer?.videoGravity = .resizeAspectFill
        playerLayer?.frame = playerContainerView.bounds
        
        // Add to container
        playerContainerView.layer.insertSublayer(playerLayer!, at: 0)
    }
    
    private func addPlayerObservers() {
        guard let player = player else { return }
        
        // Observe player item status
        player.currentItem?.addObserver(self, forKeyPath: "status", options: [.new], context: nil)
        
        // Observe playback end
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(playerDidFinishPlaying),
            name: .AVPlayerItemDidPlayToEndTime,
            object: player.currentItem
        )
    }
    
    private func removePlayerObservers() {
        player?.currentItem?.removeObserver(self, forKeyPath: "status")
        NotificationCenter.default.removeObserver(self, name: .AVPlayerItemDidPlayToEndTime, object: player?.currentItem)
    }
    
    // MARK: - Player Control
    func playVideo() {
        guard let player = player else { return }
        VideoManager.shared.playVideo(player: player)
        updatePlayPauseButton(isPlaying: true)
    }
    
    func pauseVideo() {
        player?.pause()
        updatePlayPauseButton(isPlaying: false)
    }
    
    func seekToBeginning() {
        guard let player = player else { return }
        VideoManager.shared.seekToBeginning(player: player)
    }
    
    // MARK: - UI Setup
    private func setupUI() {
        // Setup overlay gradient
        setupOverlayGradient()
        
        // Setup labels
        titleLabel.font = UIFont.boldSystemFont(ofSize: 18)
        titleLabel.textColor = .white
        titleLabel.numberOfLines = 2
        
        descriptionLabel.font = UIFont.systemFont(ofSize: 14)
        descriptionLabel.textColor = .white
        descriptionLabel.numberOfLines = 3
        
        authorLabel.font = UIFont.systemFont(ofSize: 12)
        authorLabel.textColor = UIColor.white.withAlphaComponent(0.8)
        
        cookingTimeLabel.font = UIFont.systemFont(ofSize: 12)
        cookingTimeLabel.textColor = .white
        
        difficultyLabel.font = UIFont.boldSystemFont(ofSize: 12)
        difficultyLabel.layer.cornerRadius = 8
        difficultyLabel.layer.masksToBounds = true
        difficultyLabel.backgroundColor = UIColor.black.withAlphaComponent(0.3)
        difficultyLabel.textAlignment = .center
        
        categoryLabel.font = UIFont.systemFont(ofSize: 12)
        categoryLabel.textColor = .white
        
        likesLabel.font = UIFont.systemFont(ofSize: 12)
        likesLabel.textColor = .white
        
        viewsLabel.font = UIFont.systemFont(ofSize: 12)
        viewsLabel.textColor = .white
        
        // Setup play/pause button
        playPauseButton.setImage(UIImage(systemName: "play.fill"), for: .normal)
        playPauseButton.setImage(UIImage(systemName: "pause.fill"), for: .selected)
        playPauseButton.tintColor = .white
        playPauseButton.backgroundColor = UIColor.black.withAlphaComponent(0.5)
        playPauseButton.layer.cornerRadius = 25
        playPauseButton.addTarget(self, action: #selector(playPauseButtonTapped), for: .touchUpInside)
        
        // Setup loading indicator
        loadingIndicator.style = .large
        loadingIndicator.color = .white
    }
    
    private func setupOverlayGradient() {
        let gradientLayer = CAGradientLayer()
        gradientLayer.colors = [
            UIColor.clear.cgColor,
            UIColor.black.withAlphaComponent(0.7).cgColor
        ]
        gradientLayer.locations = [0.0, 1.0]
        overlayView.layer.insertSublayer(gradientLayer, at: 0)
        
        // Update gradient frame in layoutSubviews
        DispatchQueue.main.async {
            gradientLayer.frame = self.overlayView.bounds
        }
    }
    
    private func setupIngredients(_ ingredients: [String]) {
        // Clear existing ingredient views
        ingredientsStackView.arrangedSubviews.forEach { $0.removeFromSuperview() }
        
        // Add first 3 ingredients
        let displayIngredients = Array(ingredients.prefix(3))
        for ingredient in displayIngredients {
            let label = UILabel()
            label.text = "• \(ingredient)"
            label.font = UIFont.systemFont(ofSize: 11)
            label.textColor = UIColor.white.withAlphaComponent(0.9)
            ingredientsStackView.addArrangedSubview(label)
        }
        
        // Add "and more" if there are more ingredients
        if ingredients.count > 3 {
            let moreLabel = UILabel()
            moreLabel.text = "• and \(ingredients.count - 3) more..."
            moreLabel.font = UIFont.systemFont(ofSize: 11)
            moreLabel.textColor = UIColor.white.withAlphaComponent(0.7)
            ingredientsStackView.addArrangedSubview(moreLabel)
        }
    }
    
    // MARK: - Helper Methods
    private func formatNumber(_ number: Int) -> String {
        if number >= 1000 {
            return String(format: "%.1fK", Double(number) / 1000.0)
        }
        return "\(number)"
    }
    
    private func updatePlayPauseButton(isPlaying: Bool) {
        playPauseButton.isSelected = isPlaying
        playPauseButton.isHidden = false
    }
    
    private func cleanup() {
        removePlayerObservers()
        player?.pause()
        playerLayer?.removeFromSuperlayer()
        player = nil
        playerLayer = nil
        videoReel = nil
        isConfigured = false
        loadingIndicator.stopAnimating()
    }
    
    // MARK: - Actions
    @objc private func playPauseButtonTapped() {
        guard let player = player else { return }
        
        if player.rate > 0 {
            pauseVideo()
        } else {
            playVideo()
        }
    }
    
    @objc private func playerDidFinishPlaying() {
        seekToBeginning()
        playVideo() // Auto-replay
    }
    
    // MARK: - KVO
    override func observeValue(forKeyPath keyPath: String?, of object: Any?, change: [NSKeyValueChangeKey : Any]?, context: UnsafeMutableRawPointer?) {
        if keyPath == "status" {
            DispatchQueue.main.async { [weak self] in
                self?.handlePlayerStatusChange()
            }
        }
    }
    
    private func handlePlayerStatusChange() {
        guard let player = player else { return }
        
        switch VideoManager.shared.getVideoState(for: player) {
        case .ready:
            loadingIndicator.stopAnimating()
            playPauseButton.isHidden = false
        case .failed:
            loadingIndicator.stopAnimating()
            playPauseButton.isHidden = true
            // Could show error state here
        default:
            break
        }
    }
}
