//
//  UILabel+Html.swift
//  HappyFresh
//
//  Created by Augment Agent on 2025-08-05.
//  Copyright © 2025 HappyFresh Inc. All rights reserved.
//

import UIKit
import Foundation

extension UILabel {
    
    /// Renders HTML text with proper support for lists, bullets, and numbering
    /// - Parameters:
    ///   - htmlText: The HTML string to render
    ///   - fontSize: Font size for the text
    ///   - alignment: Text alignment (default: .left)
    ///   - minimumLineHeight: Minimum line height (default: -1 for automatic)
    func setHtmlWithLists(_ htmlText: String, 
                         fontSize: CGFloat, 
                         alignment: NSTextAlignment = .left, 
                         minimumLineHeight: CGFloat = -1) {
        
        let paragraphStyle = NSMutableParagraphStyle()
        paragraphStyle.alignment = alignment
        if minimumLineHeight > 0 {
            paragraphStyle.minimumLineHeight = minimumLineHeight
        }
        
        let processedHtml = preprocessHTMLForListMarkers(htmlText)
        let attributedString = createAttributedString(from: processedHtml, 
                                                    fontSize: fontSize, 
                                                    paragraphStyle: paragraphStyle)
        
        self.attributedText = attributedString
    }
    
    /// Renders HTML text with proper support for lists using custom fonts
    /// - Parameters:
    ///   - htmlText: The HTML string to render
    ///   - normalFont: Font for normal text
    ///   - boldFont: Font for bold text
    ///   - alignment: Text alignment (default: .left)
    ///   - minimumLineHeight: Minimum line height (default: -1 for automatic)
    func setHtmlWithLists(_ htmlText: String,
                         normalFont: UIFont,
                         boldFont: UIFont,
                         alignment: NSTextAlignment = .left,
                         minimumLineHeight: CGFloat = -1) {
        
        let paragraphStyle = NSMutableParagraphStyle()
        paragraphStyle.alignment = alignment
        if minimumLineHeight > 0 {
            paragraphStyle.minimumLineHeight = minimumLineHeight
        }
        
        let processedHtml = preprocessHTMLForListMarkers(htmlText)
        let attributedString = createAttributedString(from: processedHtml,
                                                    normalFont: normalFont,
                                                    boldFont: boldFont,
                                                    paragraphStyle: paragraphStyle)
        
        self.attributedText = attributedString
    }
    
    // MARK: - Private Methods
    
    private func preprocessHTMLForListMarkers(_ htmlText: String) -> String {
        var processedHtml = htmlText
        
        // Process ordered lists first (to avoid conflicts with unordered lists)
        processedHtml = processOrderedLists(processedHtml)
        
        // Process unordered lists
        processedHtml = processUnorderedLists(processedHtml)
        
        return processedHtml
    }
    
    private func processOrderedLists(_ htmlText: String) -> String {
        var result = htmlText
        
        // Regex to find <ol>...</ol> blocks
        let pattern = #"<ol[^>]*>(.*?)</ol>"#
        guard let regex = try? NSRegularExpression(pattern: pattern, 
                                                 options: [.caseInsensitive, .dotMatchesLineSeparators]) else {
            return result
        }
        
        let matches = regex.matches(in: result, options: [], range: NSRange(location: 0, length: result.count))
        
        // Process matches in reverse order to avoid range shifting
        for match in matches.reversed() {
            let matchRange = match.range
            guard let swiftRange = Range(matchRange, in: result) else { continue }
            
            let matchString = String(result[swiftRange])
            
            // Extract list items
            let liPattern = #"<li[^>]*>(.*?)</li>"#
            guard let liRegex = try? NSRegularExpression(pattern: liPattern,
                                                       options: [.caseInsensitive, .dotMatchesLineSeparators]) else {
                continue
            }
            
            let liMatches = liRegex.matches(in: matchString, options: [], range: NSRange(location: 0, length: matchString.count))
            
            var listText = "<p>"
            for (index, liMatch) in liMatches.enumerated() {
                let contentRange = liMatch.range(at: 1)
                guard let contentSwiftRange = Range(contentRange, in: matchString) else { continue }
                
                let content = String(matchString[contentSwiftRange]).trimmingCharacters(in: .whitespacesAndNewlines)
                listText += "\(index + 1). \(content)"
                
                if index < liMatches.count - 1 {
                    listText += "<br/>"
                }
            }
            listText += "</p>"
            
            result = result.replacingCharacters(in: swiftRange, with: listText)
        }
        
        return result
    }
    
    private func processUnorderedLists(_ htmlText: String) -> String {
        var result = htmlText
        
        // Regex to find <ul>...</ul> blocks
        let pattern = #"<ul[^>]*>(.*?)</ul>"#
        guard let regex = try? NSRegularExpression(pattern: pattern,
                                                 options: [.caseInsensitive, .dotMatchesLineSeparators]) else {
            return result
        }
        
        let matches = regex.matches(in: result, options: [], range: NSRange(location: 0, length: result.count))
        
        // Process matches in reverse order to avoid range shifting
        for match in matches.reversed() {
            let matchRange = match.range
            guard let swiftRange = Range(matchRange, in: result) else { continue }
            
            let matchString = String(result[swiftRange])
            
            // Extract list items
            let liPattern = #"<li[^>]*>(.*?)</li>"#
            guard let liRegex = try? NSRegularExpression(pattern: liPattern,
                                                       options: [.caseInsensitive, .dotMatchesLineSeparators]) else {
                continue
            }
            
            let liMatches = liRegex.matches(in: matchString, options: [], range: NSRange(location: 0, length: matchString.count))
            
            var listText = "<p>"
            for (index, liMatch) in liMatches.enumerated() {
                let contentRange = liMatch.range(at: 1)
                guard let contentSwiftRange = Range(contentRange, in: matchString) else { continue }
                
                let content = String(matchString[contentSwiftRange]).trimmingCharacters(in: .whitespacesAndNewlines)
                listText += "• \(content)"
                
                if index < liMatches.count - 1 {
                    listText += "<br/>"
                }
            }
            listText += "</p>"
            
            result = result.replacingCharacters(in: swiftRange, with: listText)
        }
        
        return result
    }
    
    private func createAttributedString(from htmlText: String,
                                      fontSize: CGFloat,
                                      paragraphStyle: NSMutableParagraphStyle) -> NSMutableAttributedString {
        
        guard let data = htmlText.data(using: .unicode) else {
            return NSMutableAttributedString(string: htmlText)
        }
        
        guard let attributedString = try? NSMutableAttributedString(
            data: data,
            options: [.documentType: NSAttributedString.DocumentType.html],
            documentAttributes: nil
        ) else {
            return NSMutableAttributedString(string: htmlText)
        }
        
        let normalFont: UIFont = UIFont.systemFont(ofSize: fontSize)
        let boldFont: UIFont = UIFont.boldSystemFont(ofSize: fontSize)
        
        return applyFontStyling(to: attributedString,
                              normalFont: normalFont,
                              boldFont: boldFont,
                              paragraphStyle: paragraphStyle)
    }
    
    private func createAttributedString(from htmlText: String,
                                      normalFont: UIFont,
                                      boldFont: UIFont,
                                      paragraphStyle: NSMutableParagraphStyle) -> NSMutableAttributedString {
        
        guard let data = htmlText.data(using: .unicode) else {
            return NSMutableAttributedString(string: htmlText)
        }
        
        guard let attributedString = try? NSMutableAttributedString(
            data: data,
            options: [.documentType: NSAttributedString.DocumentType.html],
            documentAttributes: nil
        ) else {
            return NSMutableAttributedString(string: htmlText)
        }
        
        return applyFontStyling(to: attributedString,
                              normalFont: normalFont,
                              boldFont: boldFont,
                              paragraphStyle: paragraphStyle)
    }
    
    private func applyFontStyling(to attributedString: NSMutableAttributedString,
                                normalFont: UIFont,
                                boldFont: UIFont,
                                paragraphStyle: NSMutableParagraphStyle) -> NSMutableAttributedString {
        
        attributedString.beginEditing()
        
        // Apply font styling based on original font names
        attributedString.enumerateAttribute(.font, in: NSRange(location: 0, length: attributedString.length), options: []) { (value, range, _) in
            guard let oldFont = value as? UIFont else { return }
            
            attributedString.removeAttribute(.font, range: range)
            
            let newFont: UIFont
            switch oldFont.fontName {
            case "TimesNewRomanPSMT":
                newFont = normalFont
            case "TimesNewRomanPS-BoldMT":
                newFont = boldFont
            case "TimesNewRomanPS-ItalicMT":
                newFont = normalFont
            case "TimesNewRomanPS-BoldItalicMT":
                newFont = boldFont
            default:
                newFont = normalFont
            }
            
            attributedString.addAttribute(.font, value: newFont, range: range)
        }
        
        // Apply paragraph style
        attributedString.addAttribute(.paragraphStyle, value: paragraphStyle, range: NSRange(location: 0, length: attributedString.length))
        
        attributedString.endEditing()
        
        return attributedString
    }
}
