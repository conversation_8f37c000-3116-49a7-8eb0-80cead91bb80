//
//  UILabel+Html.m
//  HappyFresh
//
//  Created by <PERSON><PERSON><PERSON> on 5/19/16.
//  Copyright © 2016 HappyFresh Inc. All rights reserved.
//

#import "UILabel+Html.h"
#import "App.h"

@implementation UILabel (Html)

- (NSMutableAttributedString *)attributedString:(NSString *)htmlText fontSize:(int)fontSize paragraphStyle:(NSMutableParagraphStyle *)paragraphStyle {
    NSMutableAttributedString *attributedString = [[NSMutableAttributedString alloc] initWithData:[htmlText dataUsingEncoding:NSUnicodeStringEncoding] options:@{NSDocumentTypeDocumentAttribute : NSHTMLTextDocumentType} documentAttributes:nil error:nil];
    
    Styles *styles = [App sharedInstance].styles;
    UIFont *normalFont = [styles valueForKey:[NSString stringWithFormat:@"system%dFont", fontSize]];
    UIFont *boldFont = [styles valueForKey:[NSString stringWithFormat:@"system%dBoldFont", fontSize]];
    
    if (!normalFont) {
        normalFont = [UIFont systemFontOfSize:fontSize];
    }
    if (!boldFont) {
        boldFont = [UIFont boldSystemFontOfSize:fontSize];
    }
    
    [attributedString beginEditing];
    [attributedString enumerateAttribute:NSFontAttributeName inRange:NSMakeRange(0, attributedString.length) options:0 usingBlock:^(id value, NSRange range, BOOL *stop) {
        UIFont *oldFont = (UIFont *)value;
        
        [attributedString removeAttribute:NSFontAttributeName range:range];
        
        if ([oldFont.fontName isEqualToString:@"TimesNewRomanPSMT"])
            [attributedString addAttribute:NSFontAttributeName value:normalFont range:range];
        else if([oldFont.fontName isEqualToString:@"TimesNewRomanPS-BoldMT"])
            [attributedString addAttribute:NSFontAttributeName value:boldFont range:range];
        else if([oldFont.fontName isEqualToString:@"TimesNewRomanPS-ItalicMT"])
            [attributedString addAttribute:NSFontAttributeName value:normalFont range:range];
        else if([oldFont.fontName isEqualToString:@"TimesNewRomanPS-BoldItalicMT"])
            [attributedString addAttribute:NSFontAttributeName value:boldFont range:range];
        else
            [attributedString addAttribute:NSFontAttributeName value:normalFont range:range];
    }];
    [attributedString addAttribute:NSParagraphStyleAttributeName value:paragraphStyle range:NSMakeRange(0, attributedString.length)];
    [attributedString endEditing];

    return attributedString;
}

- (void)html:(NSString *)htmlText fontSize:(int)fontSize paragraphStyle:(NSMutableParagraphStyle *)paragraphStyle {
    self.attributedText = [self attributedString:htmlText fontSize:fontSize paragraphStyle:paragraphStyle];
}

- (void)html:(NSString *)htmlText fontSize:(int)fontSize alignment:(NSTextAlignment)alignment minimumLineHeight:(float)minLineHeight {
    NSMutableParagraphStyle *paragraphStyle = [NSMutableParagraphStyle new];
    [paragraphStyle setAlignment:alignment];
    if (minLineHeight != -1) {
        [paragraphStyle setMinimumLineHeight:minLineHeight];
    }
    
    htmlText = [self encodeStringToHTML:htmlText];
    
    self.attributedText = [self attributedString:htmlText fontSize:fontSize paragraphStyle:paragraphStyle];
}

- (void)html:(NSString *)htmlText fontSize:(int)fontSize alignment:(NSTextAlignment)alignment {
    [self html:htmlText fontSize:fontSize alignment:alignment minimumLineHeight:-1];
}

- (void)html:(NSString *)htmlText fontSize:(int)fontSize {
    [self html:htmlText fontSize:fontSize alignment:0];
}

- (NSString *)encodeStringToHTML:(NSString *)string {
    NSString *htmlString = [string stringByReplacingOccurrencesOfString:@"\n" withString:@"<br />"];
    return htmlString;
}

@end
