//
//  VideoReelsViewController.swift
//  HappyFresh
//
//  Created by Augment Agent on 2025-07-07.
//

import UIKit
import AVFoundation

class VideoReelsViewController: UIViewController {
    
    // MARK: - IBOutlets
    @IBOutlet weak var collectionView: UICollectionView!
    @IBOutlet weak var loadingIndicator: UIActivityIndicatorView!
    @IBOutlet weak var errorView: UIView!
    @IBOutlet weak var errorLabel: UILabel!
    @IBOutlet weak var retryButton: UIButton!
    
    // MARK: - Properties
    private var videoReels: [VideoReel] = []
    private var currentIndex: Int = 0
    private var isLoading = false
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupCollectionView()
        loadVideoReels()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        
        // Hide navigation bar for full-screen experience
        navigationController?.setNavigationBarHidden(true, animated: animated)
        
        // Resume video if returning to screen
        if !videoReels.isEmpty {
            playCurrentVideo()
        }
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        
        // Pause all videos when leaving screen
        VideoManager.shared.pauseAllVideos()
        
        // Show navigation bar again
        navigationController?.setNavigationBarHidden(false, animated: animated)
    }
    
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        
        // Update collection view layout
        if let layout = collectionView.collectionViewLayout as? UICollectionViewFlowLayout {
            layout.itemSize = CGSize(width: view.frame.width, height: view.frame.height)
        }
    }
    
    // MARK: - Setup
    private func setupUI() {
        view.backgroundColor = .black
        
        // Setup tab bar item
        tabBarItem = UITabBarItem(
            title: "Recipe",
            image: UIImage(systemName: "play.rectangle"),
            selectedImage: UIImage(systemName: "play.rectangle.fill")
        )
        
        // Setup error view
        errorView.isHidden = true
        errorView.backgroundColor = .black
        errorLabel.textColor = .white
        errorLabel.textAlignment = .center
        errorLabel.numberOfLines = 0
        
        retryButton.setTitle("Retry", for: .normal)
        retryButton.setTitleColor(.white, for: .normal)
        retryButton.backgroundColor = UIColor.systemBlue
        retryButton.layer.cornerRadius = 8
        retryButton.addTarget(self, action: #selector(retryButtonTapped), for: .touchUpInside)
        
        // Setup loading indicator
        loadingIndicator.style = .large
        loadingIndicator.color = .white
    }
    
    private func setupCollectionView() {
        // Register cell
        let cellNib = UINib(nibName: "VideoReelCell", bundle: nil)
        collectionView.register(cellNib, forCellWithReuseIdentifier: "VideoReelCell")
        
        // Setup layout
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .vertical
        layout.minimumLineSpacing = 0
        layout.minimumInteritemSpacing = 0
        layout.itemSize = CGSize(width: view.frame.width, height: view.frame.height)
        
        collectionView.collectionViewLayout = layout
        collectionView.isPagingEnabled = true
        collectionView.showsVerticalScrollIndicator = false
        collectionView.showsHorizontalScrollIndicator = false
        collectionView.backgroundColor = .black
        collectionView.delegate = self
        collectionView.dataSource = self
        
        // Add gesture recognizer for tap to play/pause
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleTap(_:)))
        collectionView.addGestureRecognizer(tapGesture)
    }
    
    // MARK: - Data Loading
    private func loadVideoReels() {
        showLoading(true)
        
        // Simulate network delay
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) { [weak self] in
            self?.videoReels = VideoReel.sampleReels
            self?.showLoading(false)
            self?.collectionView.reloadData()
            
            // Preload first few videos
            self?.preloadVideos()
            
            // Start playing first video
            if !self?.videoReels.isEmpty ?? true {
                self?.playCurrentVideo()
            }
        }
    }
    
    private func preloadVideos() {
        let preloadCount = min(3, videoReels.count)
        let urlsToPreload = Array(videoReels.prefix(preloadCount)).map { $0.videoURL }
        VideoManager.shared.preloadVideos(urls: urlsToPreload)
    }
    
    // MARK: - Video Control
    private func playCurrentVideo() {
        guard currentIndex < videoReels.count,
              let cell = collectionView.cellForItem(at: IndexPath(item: currentIndex, section: 0)) as? VideoReelCell else {
            return
        }
        
        cell.playVideo()
    }
    
    private func pauseCurrentVideo() {
        guard currentIndex < videoReels.count,
              let cell = collectionView.cellForItem(at: IndexPath(item: currentIndex, section: 0)) as? VideoReelCell else {
            return
        }
        
        cell.pauseVideo()
    }
    
    private func updateCurrentIndex() {
        let visibleIndexPaths = collectionView.indexPathsForVisibleItems
        if let indexPath = visibleIndexPaths.first {
            currentIndex = indexPath.item
        }
    }
    
    // MARK: - UI State
    private func showLoading(_ show: Bool) {
        isLoading = show
        loadingIndicator.isHidden = !show
        collectionView.isHidden = show
        errorView.isHidden = true
        
        if show {
            loadingIndicator.startAnimating()
        } else {
            loadingIndicator.stopAnimating()
        }
    }
    
    private func showError(_ message: String) {
        isLoading = false
        loadingIndicator.stopAnimating()
        loadingIndicator.isHidden = true
        collectionView.isHidden = true
        errorView.isHidden = false
        errorLabel.text = message
    }
    
    // MARK: - Actions
    @objc private func retryButtonTapped() {
        loadVideoReels()
    }
    
    @objc private func handleTap(_ gesture: UITapGestureRecognizer) {
        let location = gesture.location(in: collectionView)
        
        if let indexPath = collectionView.indexPathForItem(at: location),
           let cell = collectionView.cellForItem(at: indexPath) as? VideoReelCell {
            
            // Toggle play/pause for tapped cell
            let tapLocationInCell = gesture.location(in: cell)
            let cellCenter = CGPoint(x: cell.bounds.midX, y: cell.bounds.midY)
            let tapDistance = sqrt(pow(tapLocationInCell.x - cellCenter.x, 2) + pow(tapLocationInCell.y - cellCenter.y, 2))
            
            // Only toggle if tap is in center area (not on UI elements)
            if tapDistance < 100 {
                if indexPath.item == currentIndex {
                    // Current video - toggle play/pause
                    cell.playPauseButton.sendActions(for: .touchUpInside)
                }
            }
        }
    }
}

// MARK: - UICollectionViewDataSource
extension VideoReelsViewController: UICollectionViewDataSource {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return videoReels.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "VideoReelCell", for: indexPath) as! VideoReelCell
        
        let videoReel = videoReels[indexPath.item]
        cell.configure(with: videoReel)
        
        return cell
    }
}

// MARK: - UICollectionViewDelegate
extension VideoReelsViewController: UICollectionViewDelegate {
    func scrollViewDidEndDecelerating(_ scrollView: UIScrollView) {
        updateCurrentIndex()

        // Small delay for smooth transition
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) { [weak self] in
            self?.playCurrentVideo()
        }

        // Preload next videos
        preloadNextVideos()
    }

    func scrollViewWillBeginDragging(_ scrollView: UIScrollView) {
        // Pause current video when user starts scrolling
        VideoManager.shared.pauseAllVideos()
    }

    func scrollViewDidEndDragging(_ scrollView: UIScrollView, willDecelerate decelerate: Bool) {
        if !decelerate {
            updateCurrentIndex()
            playCurrentVideo()
            preloadNextVideos()
        }
    }
    
    private func preloadNextVideos() {
        let nextIndex = currentIndex + 1
        let preloadRange = nextIndex..<min(nextIndex + 2, videoReels.count)
        
        for index in preloadRange {
            let videoURL = videoReels[index].videoURL
            VideoManager.shared.preloadVideo(url: videoURL)
        }
    }
}

// MARK: - UICollectionViewDelegateFlowLayout
extension VideoReelsViewController: UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        return CGSize(width: view.frame.width, height: view.frame.height)
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, minimumLineSpacingForSectionAt section: Int) -> CGFloat {
        return 0
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, minimumInteritemSpacingForSectionAt section: Int) -> CGFloat {
        return 0
    }
}
