<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="21701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21678"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="collection view cell content view" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="VideoReelsViewController" customModule="HappyFresh" customModuleProvider="target">
            <connections>
                <outlet property="collectionView" destination="QNV-O0-1ul" id="1ul-O0-QNV"/>
                <outlet property="errorLabel" destination="fnZ-8a-g6I" id="g6I-8a-fnZ"/>
                <outlet property="errorView" destination="N6d-SI-hnr" id="hnr-SI-N6d"/>
                <outlet property="loadingIndicator" destination="Ze4-5a-6hd" id="6hd-5a-Ze4"/>
                <outlet property="retryButton" destination="8bC-Xf-vdC" id="vdC-Xf-8bC"/>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <collectionView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" dataMode="none" translatesAutoresizingMaskIntoConstraints="NO" id="QNV-O0-1ul">
                    <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                    <color key="backgroundColor" systemColor="blackColor"/>
                    <collectionViewFlowLayout key="collectionViewLayout" automaticEstimatedItemSize="YES" minimumLineSpacing="0.0" minimumInteritemSpacing="0.0" id="Bh7-fA-2Jr">
                        <size key="itemSize" width="393" height="852"/>
                        <size key="headerReferenceSize" width="0.0" height="0.0"/>
                        <size key="footerReferenceSize" width="0.0" height="0.0"/>
                        <inset key="sectionInset" minX="0.0" minY="0.0" maxX="0.0" maxY="0.0"/>
                    </collectionViewFlowLayout>
                </collectionView>
                <activityIndicatorView opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="750" verticalHuggingPriority="750" style="large" translatesAutoresizingMaskIntoConstraints="NO" id="Ze4-5a-6hd">
                    <rect key="frame" x="178" y="407.66666666666669" width="37" height="37"/>
                    <color key="color" systemColor="whiteColor"/>
                </activityIndicatorView>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="N6d-SI-hnr" userLabel="Error View">
                    <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                    <subviews>
                        <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="center" spacing="20" translatesAutoresizingMaskIntoConstraints="NO" id="Xgf-gd-Yzg">
                            <rect key="frame" x="96.666666666666686" y="376" width="200" height="100.33333333333331"/>
                            <subviews>
                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Failed to load videos" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="fnZ-8a-g6I">
                                    <rect key="frame" x="0.0" y="0.0" width="200" height="48"/>
                                    <constraints>
                                        <constraint firstAttribute="width" constant="200" id="g6I-8a-fnZ"/>
                                    </constraints>
                                    <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                    <color key="textColor" systemColor="whiteColor"/>
                                    <nil key="highlightedColor"/>
                                </label>
                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="8bC-Xf-vdC">
                                    <rect key="frame" x="67" y="68" width="66" height="32.333333333333314"/>
                                    <color key="backgroundColor" systemColor="systemBlueColor"/>
                                    <constraints>
                                        <constraint firstAttribute="width" relation="greaterThanOrEqual" constant="66" id="vdC-Xf-8bC"/>
                                    </constraints>
                                    <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                    <inset key="contentEdgeInsets" minX="16" minY="8" maxX="16" maxY="8"/>
                                    <state key="normal" title="Retry">
                                        <color key="titleColor" systemColor="whiteColor"/>
                                    </state>
                                </button>
                            </subviews>
                        </stackView>
                    </subviews>
                    <color key="backgroundColor" systemColor="blackColor"/>
                    <constraints>
                        <constraint firstItem="Xgf-gd-Yzg" firstAttribute="centerX" secondItem="N6d-SI-hnr" secondAttribute="centerX" id="Yzg-gd-Xgf"/>
                        <constraint firstItem="Xgf-gd-Yzg" firstAttribute="centerY" secondItem="N6d-SI-hnr" secondAttribute="centerY" id="gd-Yzg-Xgf"/>
                    </constraints>
                </view>
            </subviews>
            <viewLayoutMarginsGuide key="safeArea" id="Q5M-cg-NOt"/>
            <color key="backgroundColor" systemColor="blackColor"/>
            <constraints>
                <constraint firstItem="QNV-O0-1ul" firstAttribute="leading" secondItem="i5M-Pr-FkT" secondAttribute="leading" id="FkT-Pr-i5M"/>
                <constraint firstAttribute="trailing" secondItem="QNV-O0-1ul" secondAttribute="trailing" id="O0-1ul-QNV"/>
                <constraint firstItem="QNV-O0-1ul" firstAttribute="top" secondItem="i5M-Pr-FkT" secondAttribute="top" id="Pr-FkT-i5M"/>
                <constraint firstAttribute="bottom" secondItem="QNV-O0-1ul" secondAttribute="bottom" id="1ul-QNV-O0"/>
                <constraint firstItem="Ze4-5a-6hd" firstAttribute="centerX" secondItem="i5M-Pr-FkT" secondAttribute="centerX" id="5a-6hd-Ze4"/>
                <constraint firstItem="Ze4-5a-6hd" firstAttribute="centerY" secondItem="i5M-Pr-FkT" secondAttribute="centerY" id="6hd-Ze4-5a"/>
                <constraint firstItem="N6d-SI-hnr" firstAttribute="leading" secondItem="i5M-Pr-FkT" secondAttribute="leading" id="SI-hnr-N6d"/>
                <constraint firstAttribute="trailing" secondItem="N6d-SI-hnr" secondAttribute="trailing" id="hnr-N6d-SI"/>
                <constraint firstItem="N6d-SI-hnr" firstAttribute="top" secondItem="i5M-Pr-FkT" secondAttribute="top" id="N6d-hnr-SI"/>
                <constraint firstAttribute="bottom" secondItem="N6d-SI-hnr" secondAttribute="bottom" id="i5M-FkT-Pr"/>
            </constraints>
            <point key="canvasLocation" x="139" y="154"/>
        </view>
    </objects>
    <resources>
        <systemColor name="blackColor">
            <color white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
        <systemColor name="systemBlueColor">
            <color red="0.0" green="0.47843137254901963" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
        <systemColor name="whiteColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
