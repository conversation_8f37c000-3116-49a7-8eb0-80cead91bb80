/* No comment provided by engineer. */
"\nPlease select a different address" = "Mohon pilih alamat yang berbeda";

/* No comment provided by engineer. */
" will be billed every " = " akan ditagihkan setiap ";

/* No comment provided by engineer. */
"-%@%@" = "-%1$@%2$@";

/* %d will be replaced with the amount of stores in the specified area. */
"(%d Stores)" = "(%d Toko)";

/* No comment provided by engineer. */
"(ApplicationName) failed to determine your current location." = "(ApplicationName) gagal menentukan lokasi Anda sekarang.";

/* No comment provided by engineer. */
"(optional)" = "(opsional)";

/* No comment provided by engineer. */
"*You can choose more than 1 answer" = "*Anda dapat memilih lebih dari 1 jawaban";

/* No comment provided by engineer. */
"%@ failed to determine your current location." = "%@ tidak berhasil menentukan lokasi Anda saat ini.";

/* %@ will be hour. */
"%@ hour" = "%@ jam";

/* %@ will be hours. */
"%@ hours" = "%@ jam";

/* No comment provided by engineer. */
"%@ in %@" = "%1$@ pada %2$@";

/* %@ is the amount of items */
"%@ items" = "%@ barang";

/* %@ will be replaced by the minute. */
"%@ minute deliveries cannot be changed or cancelled." = "%@ menit pengiriman tidak dapat diubah atau dibatalkan.";

/* %@ will be minutes. */
"%@ minutes" = "%@ menit";

/* No comment provided by engineer. */
"%@ off" = "Diskon %@";

/* No comment provided by engineer. */
"%@ off%@" = "%1$@ diskon%2$@";

/* %@ will be replaced by the delivery voucher amount. */
"%@ on delivery" = "%@ saat pengantaran";

/* No comment provided by engineer. */
"%@ products" = "%@ produk";

/* s as in second */
"%@s..." = "%@dtk...";

/* %d is amount of items copied. Second %@ is stock location name. */
"%d items copied from %@" = "%1$d item disalin dari %2$@";

/* No comment provided by engineer. */
"%ld item(s) found in" = "%ld barang ditemukan di";

/* %@ items left */
"%ld items left" = "%ld barang tersisa";

/* %@ is the amount of points. Fonts in between <b> and </b> will be bold */
"<b>%@ points</b> that you have can't be used." = "<b>%@ poin</b> yang Anda miliki tidak dapat digunakan.";

/* Text between <b> and </b> will be bold. %@ will be replaced with price. */
"<b>%@</b> delivery promo applied" = "<b>%@</b> promo pengantaran dipakai";

/* Text between <b> and </b> will be bold. %@ will be replaced with price. */
"<b>%@</b> off delivery without code by adding <b>%@</b> more" = "<b>%1$@</b> pengantaran tanpa kode jika menambahkan <b>%2$@</b>";

/* %@ is the amount of refund balance. Fonts in between <b> and </b> will be bold */
"<b>%@</b> refund balance cannot be withdrawn." = "<b>%@</b> refund saldo tidak dapat ditarik";

/* Text between <b> and </b> will be bold. %@ will be replaced with price. */
"<b>FREE DELIVERY</b> when you add <b>%@</b> more!" = "<b>GRATIS ONGKIR</b> jika tambah <b>%@</b> lainnya!";

/* Fonts in between <b> and </b> will be bold */
"<b>Ongoing subscriptions</b> will be deactivated." = "<b>Langganan yang sedang berlangsung</b> akan dinonaktifkan";

/* Fonts in between <b> and </b> will be bold */
"<b>Points</b> that you have can't be used." = "<b>Poin</b> yang Anda miliki tidak dapat digunakan.";

/* Fonts in between <b> and </b> will be bold */
"<b>Refund balance</b> that you have cannot be withdrawn." = "<b>Refund saldo</b> yang Anda miliki tidak dapat ditarik";

/* No comment provided by engineer. */
"<b>Save up to %@</b> with Bank Promos" = "<b>Hemat hingga %@</b> dengan Promo Bank";

/* No comment provided by engineer. */
"<font color='#666'>This location is outside of our delivery area.  Tap <font color='#1a1a1a'><b>Request to deliver here</b></font> and you could be shopping with HappyFresh very soon!</font>" = "<font color='#666'>Lokasi ini di luar area pengantaran kami. Ketuk <font color='#1a1a1a'><b>Minta untuk dikirimkan ke sini</b></font> dan Anda akan dapat berbelanja di HappyFresh segera!</font>";

/* No comment provided by engineer. */
"<font color=\"#3BA4B4\">Active cart</font> - %ld Product" = "<font color=\"#3BA4B4\">Troli aktif</font> - %ld Produk";

/* No comment provided by engineer. */
"<font color=\"#3BA4B4\">Active cart</font> - %ld Products" = "<font color=\"#3BA4B4\">Troli aktif</font> - %ld Produk";

/* No comment provided by engineer. */
"<font color=\"#3BA4B4\">Delivery Time: <b>%@</b></font>" = "<font color=\"#3BA4B4\">Waktu Pengantaran<b>%@</b></font>";

/* No comment provided by engineer. */
"<font color=\"#666666\"><b>\"%@\"</b> did not match any items.</font>" = "<font color=\"#666666\"><b>\"%@\"</b> tidak sesuai barang apapun.</font>";

/* No comment provided by engineer. */
"<font color=\"#666666\">Showing results for <b>%@</b>.</font>" = "<font color=\"#666666\">Tampilkan hasil untuk <b>%@</b>.</font>";

/* %@ is the promotion code */
"<small>code:</small> %@" = "<small>kode:</small> %@";

/* No comment provided by engineer. */
"0 item(s) found in" = "%ld barang ditemukan di";

/* No comment provided by engineer. */
"1 item" = "1 item";

/* No comment provided by engineer. */
"1 product" = "1 produk";

/* No comment provided by engineer. */
"1. Select Location\n2. Tap \"While Using the App\"" = "1. Pilih lokasi\n2. Klik \"Saat Menggunakan App\"";

/* No comment provided by engineer. */
"A bit unhappy" = "Kurang puas";

/* No comment provided by engineer. */
"A dedicated agent will give you swift support." = "Agen khusus akan membantumu dengan cepat.";

/* No comment provided by engineer. */
"A healthy start!" = "Awali dengan yang sehat!";

/* No comment provided by engineer. */
"A-Z" = "A-Z";

/* tab bar */
"Account" = "Akun";

/* No comment provided by engineer. */
"Activate location permission" = "Berikan izin akses lokasi";

/* No comment provided by engineer. */
"Active Orders" = "Pesanan Aktif";

/* No comment provided by engineer. */
"Add" = "Tambah";

/* No comment provided by engineer. */
"Add %@ more, <b>save up to %@</b> with Bank Promos" = "Tambah %1$@, <b>hemat hingga %2$@</b> dengan Bank Promo";

/* No comment provided by engineer. */
"Add %ld more items to create a bundle" = "Tambah %ld produk lagi untuk membuat bundel";

/* No comment provided by engineer. */
"Add <strong>%@</strong> to checkout" = "Tambah <strong>%@</strong> untuk checkout";

/* No comment provided by engineer. */
"Add All To Cart" = "Tambah Semua ke Troli";

/* No comment provided by engineer. */
"Add delivery address" = "Tambah alamat pengantaran";

/* No comment provided by engineer. */
"Add more" = "Tambah";

/* No comment provided by engineer. */
"Add new address" = "Tambah alamat baru";

/* No comment provided by engineer. */
"Add new items" = "Tambah produk baru";

/* No comment provided by engineer. */
"Add notes" = "Catatan";

/* No comment provided by engineer. */
"Add notes or replacement" = "Tambah catatan atau pengganti";

/* No comment provided by engineer. */
"Add notes or select replacement for your items to be ready for out-of-stock in the store" = "Tambah catatan atau barang pengganti untuk antisipasi ketidaktersediaan barang di toko";

/* No comment provided by engineer. */
"Add or Remove Items" = "Tambah atau Hapus Barang";

/* No comment provided by engineer. */
"Add to cart" = "Tambahkan";

/* No comment provided by engineer. */
"Add/remove items" = "Tambah/hapus produk";

/* No comment provided by engineer. */
"Add/Remove Items" = "Tambah/Hapus Produk";

/* No comment provided by engineer. */
"Address" = "Alamat";

/* No comment provided by engineer. */
"Address Detail" = "Detail alamat";

/* No comment provided by engineer. */
"Address list" = "Daftar alamat";

/* No comment provided by engineer. */
"Address not found? Pin manually." = "Alamat tidak ditemukan? Letakkan pin.";

/* No comment provided by engineer. */
"Adjust replacements" = "Sesuaikan penggantian";

/* No comment provided by engineer. */
"Age Verification" = "Verifikasi Umur";

/* No comment provided by engineer. */
"All" = "Semua";

/* No comment provided by engineer. */
"All brands" = "Semua merek";

/* No comment provided by engineer. */
"All categories" = "Semua kategori";

/* No comment provided by engineer. */
"All in (productType)" = "Semua pada (productType)";

/* No comment provided by engineer. */
"All in (taxonName)" = "Semua pada (taxonName)";

/* No comment provided by engineer. */
"All in %@" = "Semua %@";

/* No comment provided by engineer. */
"All past items" = "Barang sebelumnya";

/* No comment provided by engineer. */
"All Promos" = "Semua promo";

/* No comment provided by engineer. */
"All Riders are busy. Please check back later." = "Semua Rider sedang sibuk. Silahkan periksa kembali nanti.";

/* No comment provided by engineer. */
"All scheduled slots offered by this store are already taken." = "Semua slot terjadwal yang ditawarkan oleh toko ini sudah diambil.";

/* No comment provided by engineer. */
"All Stores" = "Semua toko";

/* No comment provided by engineer. */
"All the ingredients were added to your cart. HappyCooking!" = "Semua bahan baku telah ditambahkan ke troli Anda. Selamat masak!";

/* No comment provided by engineer. */
"All Vouchers" = "Semua Voucher";

/* No comment provided by engineer. */
"Allow us to send you Flash Sale notifications so you won’t miss it" = "Izinkan kami untuk mengirimkan notifikasi Flash Sale agar kamu tidak melewatkannya";

/* No comment provided by engineer. */
"Also rate us on the App Store" = "Beri nilai kami di App Store";

/* No comment provided by engineer. */
"Always-on discount" = "Selalu ada promo";

/* No comment provided by engineer. */
"An error has occured" = "Terjadi kesalahan";

/* No comment provided by engineer. */
"And now you can view your cart total directly on here!" = "Sekarang, kamu bisa lihat langsung trolimu di sini!";

/* No comment provided by engineer. */
"And our Rider?" = "dan Rider kami?";

/* No comment provided by engineer. */
"Announcements" = "Pengumuman";

/* No comment provided by engineer. */
"Any reduction in total amount will be refunded in several days, depending on your payment method." = "Pengurangan jumlah total akan dikembalikan dalam beberapa hari, tergantung pada metode pembayaranmu.";

/* No comment provided by engineer. */
"applied!" = "Terpakai!";

/* No comment provided by engineer. */
"Applies to orders below" = "Berlaku untuk pesanan di bawah ini";

/* Apply Button in Filter */
"Apply" = "Terapkan";

/* No comment provided by engineer. */
"Apply voucher" = "Pakai voucher";

/* No comment provided by engineer. */
"Are these what you're looking for?" = "Apakah ini yang kamu cari?";

/* No comment provided by engineer. */
"Are you sure want to delete your account?" = "Apakah Anda yakin ingin menghapus akun ini?";

/* No comment provided by engineer. */
"Are you sure you want to cancel your order?" = "Anda yakin untuk membatalkan pesanan Anda?";

/* No comment provided by engineer. */
"Are you sure you want to change your \ndelivery to this time?" = "Anda yakin untuk mengganti waktu pengantaran Anda saat ini?";

/* No comment provided by engineer. */
"Are you sure you want to delete this address?" = "Apakah Anda yakin ingin menghapus alamat ini?";

/* No comment provided by engineer. */
"Are you sure you want to log out?" = "Apakah Anda yakin ingin keluar?";

/* No comment provided by engineer. */
"Are you sure you want to shop without voucher?" = "Kamu yakin ingin belanja tanpa voucher?";

/* No comment provided by engineer. */
"Are you sure?" = "Anda yakin?";

/* No comment provided by engineer. */
"As a VIP member, extra delivery slots are made available for you!" = "Sebagai member VIP, slot pengantaran ekstra tersedia untukmu!";

/* No comment provided by engineer. */
"As this is a monthly subscription, you will not be billed for any future months once you cancel. You may cancel anytime but will continue to have the benefits of the subscription until the end date. You can always see when the next billing date or end date is by visiting the My Subscription Page under My Account." = "Kamu bisa berhenti di tengah masa langganan. Jangan khawatir, paket yang sedang aktif tetap bisa digunakan sampai masa berlangganan berakhir. Kamu juga bisa melihat tanggal tagihan selanjutnya dan/atau tanggal paket langganan berakhir pada menu 'Akun Saya' kemudian pilih 'Akun Langganan Saya'";

/* No comment provided by engineer. */
"automatically applied" = "otomatis terpasang";

/* No comment provided by engineer. */
"Availability and promos may differ per store. Please check if you've got all your items." = "Ketersedian dan promo-promo mungkin berbeda di tiap toko. Silahkan periksa barang belanjaanmu.";

/* No comment provided by engineer. */
"Available products" = "Produk-produk tersedia";

/* No comment provided by engineer. */
"Back to current store" = "Kembali ke toko saat ini";

/* No comment provided by engineer. */
"Bank/E-Wallet" = "Bank/E-Wallet";

/* No comment provided by engineer. */
"Bank/e-Wallet Promos is now on cart" = "Promo Bank/e-Wallet ada di troli";

/* No comment provided by engineer. */
"Bank/e-wallet vouchers" = "Voucher bank/e-wallet";

/* No comment provided by engineer. */
"Before continuing, can you please tell us the reason for deleting account?" = "Sebelum melanjutkan, bisakah Anda memberi tahu kami alasan hapus akun?";

/* No comment provided by engineer. */
"Best choice" = "Pilihan terbaik";

/* No comment provided by engineer. */
"Best deals" = "Penawaran";

/* No comment provided by engineer. */
"Best Deals" = "Penawaran Terbaik";

/* No comment provided by engineer. */
"Best deals are coming soon! Please come back again later or enter voucher code above if you have one." = "Penawaran terbaik segera datang! Silahkan kembali lagi nanti atau masukkan kode voucher di atas jika kamu memilikinya.";

/* No comment provided by engineer. */
"Best deals, all your favorite items" = "Penawaran terbaik untuk barang favoritmu";

/* No comment provided by engineer. */
"Best: 3 days" = "Terbaik: 3 hari";

/* No comment provided by engineer. */
"Bestsellers" = "Terlaris";

/* No comment provided by engineer. */
"Bonus Points" = "Bonus Poin";

/* No comment provided by engineer. */
"Brand" = "Merek";

/* No comment provided by engineer. */
"Brands" = "Merek";

/* No comment provided by engineer. */
"Bundles" = "Bundel";

/* No comment provided by engineer. */
"Buy X get Y" = "Beli X dapat Y";

/* No comment provided by engineer. */
"Buy X Get Y" = "Beli X Gratis Y";

/* No comment provided by engineer. */
"By logging in & signing up you agree with\nTerms and Conditions & Privacy Policies" = "Dengan masuk atau daftar, kamu setuju dengan\nSyarat dan Ketentuan & Kebijakan Privasi";

/* No comment provided by engineer. */
"Call" = "Telepon";

/* No comment provided by engineer. */
"Call Our Customer Service" = "Hubungi Customer Service Kami";

/* No comment provided by engineer. */
"Call us" = "Telepon kami";

/* No comment provided by engineer. */
"Camera" = "Kamera";

/* No comment provided by engineer. */
"Can I change my payment method in the middle of subscription?" = "Apakah saya bisa mengganti metode pembayaran saat paket sedang aktif?";

/* No comment provided by engineer. */
"Can I share my subscription plan?" = "Bolehkah membagikan paket langganan saya?";

/* No comment provided by engineer. */
"Can I terminate midway of the subscription period?" = "Apakah saya bisa berhenti berlangganan saat paket masih aktif?";

/* No comment provided by engineer. */
"Can I use additional promotion vouchers on top of Subscription discount benefit?" = "Apakah saya bisa menggunakan paket langganan bersama dengan voucher promo lainnya?";

/* No comment provided by engineer. */
"Can't find what you’re looking for?" = "Tidak menemukan yang kamu cari?";

/* No comment provided by engineer. */
"Can't receive your order?" = "Tidak dapat menerima pesananmu?";

/* No comment provided by engineer. */
"Cancel" = "Batal";

/* No comment provided by engineer. */
"Cancel editing account?" = "Batalkan edit akun?";

/* No comment provided by engineer. */
"Cannot add or remove items" = "Tidak bisa tambah atau hapus produk";

/* No comment provided by engineer. */
"Cannot apply voucher" = "Voucher tidak dapat dipakai";

/* No comment provided by engineer. */
"Cannot cancel order" = "Tidak dapat membatalkan pesanan";

/* No comment provided by engineer. */
"Cannot change delivery slot" = "Tidak dapat mengubah slot pengantaran";

/* No comment provided by engineer. */
"Cannot change payment method or address" = "Tidak dapat mengubah metode pembayaran atau alamat";

/* No comment provided by engineer. */
"Cannot remove voucher code" = "Tidak dapat menghapus kode voucher";

/* %@ is voucher vode */
"Cannot remove voucher code %@" = "Tidak bisa hapus kode voucher %@";

/* No comment provided by engineer. */
"Card Only" = "Kartu Kredit";

/* No comment provided by engineer. */
"Care to share more? It’ll take just a minute." = "Ingin berbagi lagi? Hanya semenit saja.";

/* No comment provided by engineer. */
"Cart in %@" = "Troli di %@";

/* No comment provided by engineer. */
"Cart in %d stores" = "Troli di %d toko";

/* No comment provided by engineer. */
"Cart Total" = "Total Troli";

/* No comment provided by engineer. */
"Cashless" = "Non tunai";

/* No comment provided by engineer. */
"Categories" = "Kategori";

/* No comment provided by engineer. */
"Change" = "Ganti";

/* No comment provided by engineer. */
"Change delivery slot" = "Ganti slot pengantaran";

/* No comment provided by engineer. */
"Change Delivery Slot" = "Ganti Slot Pengantaran";

/* No comment provided by engineer. */
"Change Items" = "Ganti Barang";

/* No comment provided by engineer. */
"Change location" = "Ubah lokasi";

/* No comment provided by engineer. */
"Change My Order" = "Ganti Pesanan Saya";

/* No comment provided by engineer. */
"Change payment method or address" = "Ubah alamat atau metode pembayaran";

/* No comment provided by engineer. */
"Change Phone Number" = "Ganti Nomor Telepon";

/* No comment provided by engineer. */
"Change Store" = "Ganti Toko";

/* No comment provided by engineer. */
"Change store to \"%@\"?\nTo proceed with the link, we need to redirect you to a different store. When you change store, we will keep your current basket accessible." = "Ganti toko ke \"%@\"?\nUntuk menyetujui, kami harus mengerahkan Anda menuju toko yang berbeda. Ketika Anda mengganti toko, kami akan jaga troli Anda.";

/* No comment provided by engineer. */
"Change time slot" = "Ubah slot waktu";

/* No comment provided by engineer. */
"Changes will be automatically saved to your order." = "Perubahan akan disimpan pada pesanan Anda secara otomatis.";

/* No comment provided by engineer. */
"Chat" = "Chat";

/* No comment provided by engineer. */
"Check my benefits" = "Lihat paket saya";

/* No comment provided by engineer. */
"Check now" = "Cek sekarang";

/* %@ is product price */
"Check now for %@" = "Cek sekarang untuk %@";

/* No comment provided by engineer. */
"Check out this %@ recipe on HappyFresh: %@" = "Lihat resep %1$@ ini di HappyFresh: %2$@";

/* No comment provided by engineer. */
"Check your notifications" = "Cek notifikasi kamu";

/* No comment provided by engineer. */
"Checkout" = "Checkout";

/* No comment provided by engineer. */
"Choose a nearby store to see promos or products" = "Pilih toko terdekat untuk lihat produk atau promo";

/* No comment provided by engineer. */
"Choose a store to get the ingredients you need" = "Pilih toko untuk mendapatkan bahan yang kamu butuhkan";

/* No comment provided by engineer. */
"Choose Delivery Address" = "Pilih Alamat Pengantaran";

/* No comment provided by engineer. */
"Choose delivery slot" = "Pilih slot pengantaran";

/* No comment provided by engineer. */
"Choose image source" = "Pilih sumber foto";

/* No comment provided by engineer. */
"Choose replacement" = "Pilih pengganti";

/* No comment provided by engineer. */
"Choose Replacement" = "Pilih pengganti";

/* No comment provided by engineer. */
"Choose store outlet" = "Pilih toko";

/* No comment provided by engineer. */
"Choose verification method" = "Pilih metode verifikasi";

/* No comment provided by engineer. */
"Choose your plan and pay" = "Pilih paketmu dan bayar";

/* No comment provided by engineer. */
"Choose your replacement options:" = "Pilih opsi penggantianmu:";

/* No comment provided by engineer. */
"Chosen replacements. Adjust options below." = "Pengganti yang dipilih. Sesuaikan opsi di bawah.";

/* No comment provided by engineer. */
"Clear all" = "Bersihkan";

/* No comment provided by engineer. */
"Clearance" = "Cuci Gudang";

/* No comment provided by engineer. */
"Close" = "Tutup";

/* No comment provided by engineer. */
"Code sent!" = "Kode terkirim!";

/* All letters capitalized */
"CODE:" = "KODE:";

/* Text inside <b> and </b> is bold. %@ is voucher code. */
"CODE: <b>%@</b>" = "KODE: <b>%@</b>";

/* No comment provided by engineer. */
"Complete your address detail" = "Lengkapi detail alamat Anda";

/* No comment provided by engineer. */
"Confirm" = "Menyetujui";

/* No comment provided by engineer. */
"Confirm " = "Setuju";

/* No comment provided by engineer. */
"Confirm all replacement" = "Konfirmasi semua penggantian";

/* No comment provided by engineer. */
"Confirm Cancellation" = "Menyetujui Pembatalan";

/* No comment provided by engineer. */
"Confirm Delivery Location" = "Konfirmasi alamat pengiriman";

/* No comment provided by engineer. */
"Confirm replacement option" = "Konfirmasi opsi pengganti";

/* No comment provided by engineer. */
"Confirm selection" = "Konfirmasi pilihan";

/* No comment provided by engineer. */
"Confirm Subscription" = "Konfirmasi paket langganan";

/* No comment provided by engineer. */
"Confirming your delivery" = "Konfirmasi pengantaranmu";

/* No comment provided by engineer. */
"Congratulations!" = "Selamat!";

/* No comment provided by engineer. */
"Connection Problem" = "Masalah Koneksi";

/* No comment provided by engineer. */
"Connection problem occured" = "Terjadi masalah koneksi";

/* No comment provided by engineer. */
"Contact CS via WhatsApp" = "Hubungi CS via WhatsApp";

/* No comment provided by engineer. */
"Contact me" = "Hubungi saya";

/* No comment provided by engineer. */
"Contact us" = "Hubungi Kami";

/* No comment provided by engineer. */
"Contact us for any questions" = "Hubungi kami untuk pertanyaan lain";

/* No comment provided by engineer. */
"Continue" = "Lanjutkan";

/* No comment provided by engineer. */
"Continue browsing" = "Lanjutkan pencarian";

/* No comment provided by engineer. */
"Copied to clipboard" = "Disalin ke clipboard";

/* No comment provided by engineer. */
"Create New Address" = "Buat Alamat Baru";

/* No comment provided by engineer. */
"Create your account or log in here" = "Buat akun Anda atau masuk di sini";

/* No comment provided by engineer. */
"Current location" = "Lokasi terkini";

/* No comment provided by engineer. */
"Currently shopping at" = "Berbelanja di";

/* No comment provided by engineer. */
"Customer Change of Mind" = "Pelanggan Berubah Pikiran";

/* No comment provided by engineer. */
"Dairy & Eggs" = "Olahan Susu & Telur";

/* No comment provided by engineer. */
"Damaged In Transit" = "Rusak dalam Pengantaran";

/* No comment provided by engineer. */
"Deal's gone!" = "Promo berakhir!";

/* No comment provided by engineer. */
"Deals Gone" = "Penawaran berakhir";

/* No comment provided by engineer. */
"Dedicated customer support" = "Pusat bantuan khusus";

/* No comment provided by engineer. */
"Delete account" = "Hapus akun";

/* No comment provided by engineer. */
"Delete Address" = "Hapus Alamat";

/* No comment provided by engineer. */
"Delete this cart?" = "Hapus troli?";

/* No comment provided by engineer. */
"Deliver to" = "Antar ke";

/* No comment provided by engineer. */
"Deliver your grocery here?" = "Kirim pesananmu ke sini?";

/* No comment provided by engineer. */
"Delivering" = "Mengantarkan";

/* No comment provided by engineer. */
"Delivery address" = "Alamat pengantaran";

/* No comment provided by engineer. */
"Delivery checker" = "Pemeriksa pengantaran";

/* No comment provided by engineer. */
"Delivery Checker" = "Pemeriksa Pengantaran";

/* No comment provided by engineer. */
"Delivery info" = "Info pengantaran";

/* No comment provided by engineer. */
"Delivery instructions" = "Instruksi pengantaran";

/* No comment provided by engineer. */
"Delivery time" = "Waktu pengantaran";

/* No comment provided by engineer. */
"Delivery Time" = "Waktu Pengantaran";

/* No comment provided by engineer. */
"Delivery:" = "Pengantaran:";

/* No comment provided by engineer. */
"Didn’t get any message?" = "Belum dapat pesan?";

/* No comment provided by engineer. */
"Didn't receive the OTP? " = "Tidak menerima OTP? ";

/* No comment provided by engineer. */
"Difficulty" = "Kesulitan";

/* No comment provided by engineer. */
"Discount" = "Diskon";

/* %@ will be replaced with discount amount */
"Discount %@ off" = "Diskon %@";

/* No comment provided by engineer. */
"Discount vouchers" = "Voucher diskon";

/* %@ is brand name */
"Discover more %@ products" = "Temukan produk %@ lainnya";

/* No comment provided by engineer. */
"Discover recipes" = "Temukan resep pilihan";

/* No comment provided by engineer. */
"Do not cancel" = "Jangan dibatalkan";

/* No comment provided by engineer. */
"Do not replace" = "Jangan diganti";

/* No comment provided by engineer. */
"Do you have any feedback?" = "Ada saran untuk kami?";

/* No comment provided by engineer. */
"Do you have any preference?" = "Apakah kamu punya preferensi?";

/* No comment provided by engineer. */
"Do you have any problems?" = "Kamu punya kendala?";

/* No comment provided by engineer. */
"Do you know? HappyFresh Shopper are trained to pick the best quality products for you." = "Tahukah kamu? Shopper HappyFresh sudah terlatih untuk memilih produk berkualitas untukmu.";

/* No comment provided by engineer. */
"Do you mean %@?" = "Maksud Anda %@?";

/* No comment provided by engineer. */
"Do you want to login manually?" = "Do you want to login manually?";

/* No comment provided by engineer. */
"Don't like these options?" = "Kurang puas dengan pilihan di atas?";

/* No comment provided by engineer. */
"Don't miss these huge savings" = "Jangan lewatkan kesempatan besar berhemat ini";

/* No comment provided by engineer. */
"Don’t See Your Favourite Store?" = "Tidak Temukan Toko Favorit Anda?";

/* No comment provided by engineer. */
"Don't show this again" = "Tidak perlu ditampilkan lagi";

/* No comment provided by engineer. */
"Don't worry, it happens to anyone! Contact us to get it sorted out." = "Jangan khawatir, hal ini terjadi pada siapa pun. Hubungi kami untuk mengatasinya.";

/* No comment provided by engineer. */
"Donation" = "Donasi";

/* No comment provided by engineer. */
"Drop location pin on map" = "Letakkan pin lokasi di peta";

/* No comment provided by engineer. */
"Drop your pin" = "Tentukan lokasimu";

/* No comment provided by engineer. */
"Due to our limited delivery capacity, you can only add new items up to 50% your original order." = "Karena kapasitas pengiriman kami yang terbatas, Kamu hanya dapat menambahkan produk baru hingga 50% pesanan awalmu.";

/* No comment provided by engineer. */
"E-mail" = "E-mail";

/* No comment provided by engineer. */
"e.g House number 5" = "cth Rumah nomor 5";

/* No comment provided by engineer. */
"e.g Leave it in front of the door" = "cth tinggalkan di depan pintu";

/* No comment provided by engineer. */
"e.g. bananas shouldn't be overripe" = "misal: pisang jangan terlalu matang";

/* No comment provided by engineer. */
"e.g: +628123xxx | <EMAIL>" = "cth: +628123xxx | <EMAIL>";

/* No comment provided by engineer. */
"Earliest delivery by:" = "Pengantaran tercepat:";

/* Text between <b> and </b> will be bold. %@ will be replaced by the app with the point amount. */
"Earn <b>%@ points*</b> upon delivery." = "Kumpulkan <b>%@ poin*</b> saat pengantaran";

/* No comment provided by engineer. */
"Edit Account" = "Edit Akun";

/* No comment provided by engineer. */
"Edit Address" = "Ubah Alamat";

/* No comment provided by engineer. */
"Edit items" = "Edit produk";

/* No comment provided by engineer. */
"Email address is not valid." = "Alamat email tidak valid";

/* No comment provided by engineer. */
"Email and password can't be empty." = "Email dan sandi tidak boleh kosong";

/* No comment provided by engineer. */
"Email is not valid." = "Email tidak valid.";

/* No comment provided by engineer. */
"Email verification" = "Verifikasi email";

/* No comment provided by engineer. */
"Email, password and firstname can't be empty." = "Email, sandi dan nama depan tidak boleh kosong.";

/* No comment provided by engineer. */
"Enjoy 24h delivery for all official Brand Stores. The delivery time of your order depends on your location and shipping methods" = "Nikmati pengantaran 24jam untuk semua Toko Brand resmi. Waktu pengantaran pesanan akan menyesuaikan lokasi dan metode pengantaran Anda.";

/* Text between <b> and </b> will be bold. */
"Enjoy huge discounts on these items while they are still good to consume. <b>Learn more</b>" = "Nikmati diskon besar-besaran untuk item-item ini selagi masih baik untuk dikonsumsi. <b>Pelajari lebih lanjut</b>";

/* No comment provided by engineer. */
"Enter all the stores you would like to shop and we will notify you when we open them in this area." = "Masukkan semua toko yang Anda inginkan dan kami akan memberitahukan ketika kami telah buka di area Anda.";

/* No comment provided by engineer. */
"Enter phone number or email" = "Masukkan no. telepon atau email";

/* No comment provided by engineer. */
"Enter voucher code" = "Masukkan kode voucher";

/* No comment provided by engineer. */
"Enter without account" = "Masuk tanpa akun";

/* No comment provided by engineer. */
"Enter your email" = "Masukkan email kamu";

/* No comment provided by engineer. */
"Enter your email address" = "Masukkan alamat email Anda";

/* No comment provided by engineer. */
"Enter your favourite stores" = "Masukkan toko favorit Anda";

/* No comment provided by engineer. */
"Enter your name" = "Masukkan nama kamu";

/* No comment provided by engineer. */
"Enter your password to confirm the changes" = "Masukan kata sandi untuk mengonfirmasi perubahan";

/* No comment provided by engineer. */
"Enter your valid email" = "Masukkan email valid Anda";

/* Title in alert pop up */
"Error" = "Kesalahan";

/* No comment provided by engineer. */
"Estimated Price" = "Perkiraan Harga";

/* No comment provided by engineer. */
"Estimated price - will be updated after weighing." = "Perkiraan harga - akan diperbarui setelah penimbangan.";

/* No comment provided by engineer. */
"Exclusive Promo" = "Promo Eksklusif";

/* No comment provided by engineer. */
"Exit Edit Mode" = "Keluar Mode Ubah";

/* No comment provided by engineer. */
"Explore categories" = "Lihat kategori lainnya";

/* No comment provided by engineer. */
"Explore cooking ideas with HappyFresh" = "Temukan beragam inspirasi masak bersama HappyFresh";

/* No comment provided by engineer. */
"Explore and shop ingredients for every recipe" = "Temukan resep favoritmu dan dapatkan bahannya di sini";

/* No comment provided by engineer. */
"Explore HappyFresh Supermarket" = "Telusuri HappyFresh Supermarket";

/* No comment provided by engineer. */
"Explore our deals" = "Lihat promo lainnya";

/* No comment provided by engineer. */
"Explore recipes" = "Cari resep";

/* No comment provided by engineer. */
"Express" = "Ekspres";

/* No comment provided by engineer. */
"Express delivery confirmed!" = "Pengantaran ekspres dikonfirmasi!";

/* No comment provided by engineer. */
"Express Delivery is now available!" = "Pengiriman ekspres sekarang tersedia!";

/* No comment provided by engineer. */
"Express orders are prioritized and delivered within 90 minutes after order confirmed." = "Pesanan ekspres diprioritaskan dan dikirim dalam waktu 90 menit setelah pesanan dikonfirmasi.";

/* No comment provided by engineer. */
"Facebook Error" = "Kesalahan Facebook";

/* No comment provided by engineer. */
"FAQ" = "FAQ";

/* No comment provided by engineer. */
"Favourites" = "Favorit";

/* No comment provided by engineer. */
"Fill your cart to enjoy vouchers" = "Isi troli untuk lihat voucher";

/* No comment provided by engineer. */
"Filter by brand" = "Pilih merek";

/* No comment provided by engineer. */
"Filter by brand:" = "Pilih merek:";

/* No comment provided by engineer. */
"Find out more" = "Temukan lainnya";

/* No comment provided by engineer. */
"Find Stores Near Me" = "Cari toko terdekat";

/* No comment provided by engineer. */
"Find your ideal replacement" = "Temukan pengganti idealmu";

/* No comment provided by engineer. */
"Finished Orders" = "Pesanan Selesai";

/* No comment provided by engineer. */
"Flash sale" = "Flash sale";

/* No comment provided by engineer. */
"Flash Sale ends in" = "Flash Sale berakhir pada";

/* No comment provided by engineer. */
"Flash Sale starts in" = "Flash Sale dimulai pada";

/* No comment provided by engineer. */
"Forgot my password" = "Lupa sandi saya";

/* No comment provided by engineer. */
"Forgot Password" = "Lupa Sandi";

/* No comment provided by engineer. */
"FREE" = "GRATIS";

/* No comment provided by engineer. */
"Free delivery" = "Gratis ongkir";

/* No comment provided by engineer. */
"Free Delivery" = "Gratis Ongkir";

/* No comment provided by engineer. */
"FREE Delivery" = "GRATIS Ongkir";

/* No comment provided by engineer. */
"FREE Delivery & Discount" = "GRATIS Ongkir & Diskon";

/* %@ will be replaced with price. */
"Free delivery without code by adding <b>%@</b> more" = "Gratis ongkir tanpa kode jika menambahkan <b>%@</b>";

/* No comment provided by engineer. */
"Free Item(s)" = "Barang Gratis";

/* No comment provided by engineer. */
"Frequently Asked Questions" = "Tanya Jawab";

/* No comment provided by engineer. */
"Frequently Visited" = "Sering dikunjungi";

/* No comment provided by engineer. */
"Fresh Produce" = "Produk Segar";

/* No comment provided by engineer. */
"Freshly handpicked by our skilled Shopper to ensure quality" = "Dipilih oleh Shopper handal kami untuk menjamin kualitas terbaik";

/* No comment provided by engineer. */
"From premium to value supermarket, explore them all near you!" = "Dari supermarket premium hingga toserba hemat, semua ada di sini!";

/* No comment provided by engineer. */
"Get from" = "Dapat dari";

/* No comment provided by engineer. */
"Get informed with our latest promotion & stay up to date with your order's status by allowing your notification permission." = "Dapatkan informasi terbaru mengenai promo & status pemesanan Anda dengan mengaktifkan notifikasi Anda.";

/* No comment provided by engineer. */
"Get notified!" = "Dapatkan notifikasi!";

/* No comment provided by engineer. */
"Get quality groceries handpicked by our trained personal shoppers, delivered by our riders." = "Dapatkan kebutuhan berkualitas\nyang dipilih oleh personal shopper terlatih kami, dikirim oleh rider kami.";

/* first %@ will be hours (use no space), second %@ will be store name. */
"Get ready for the%@special deal on %@!" = "Bersiaplah untuk promo khusus%1$@di %2$@!";

/* No comment provided by engineer. */
"Get the new app" = "Dapatkan aplikasi baru";

/* No comment provided by engineer. */
"Get the promotion" = "Dapatkan promosi";

/* No comment provided by engineer. */
"Give a permission" = "Berikan izin";

/* No comment provided by engineer. */
"Give specific instructions to our shopper." = "Beri detail instruksi ke shopper kami.";

/* No comment provided by engineer. */
"Go to search to find other alternatives" = "Lanjutkan ke pencarian untuk temukan alternatif lain";

/* No comment provided by engineer. */
"Gold Member" = "Gold Member";

/* No comment provided by engineer. */
"Gold Member Exclusive" = "Eksklusif Gold Member";

/* No comment provided by engineer. */
"Good" = "Baik";

/* No comment provided by engineer. */
"Got it" = "Baiklah";

/* No comment provided by engineer. */
"Got It" = "Baiklah";

/* No comment provided by engineer. */
"Got it, we will send you Flash Sale notifications" = "Dimengerti, kami akan mengirimkanmu notifikasi Flash Sale";

/* No comment provided by engineer. */
"Got it!" = "Baik!";

/* No comment provided by engineer. */
"Got it! We'll be on the lookout for this item." = "Baik! kami akan mencari item ini.";

/* No comment provided by engineer. */
"GPS Problem" = "Masalah GPS";

/* No comment provided by engineer. */
"GrabExpress" = "GrabExpress";

/* No comment provided by engineer. */
"Handpicked for you" = "Dipilih langsung untukmu";

/* No comment provided by engineer. */
"HappyFresh ⚡ FLASH SALE starts now! ⚡" = "⚡ FLASH SALE dimulai sekarang!⚡";

/* No comment provided by engineer. */
"HappyFresh Rewards" = "HappyFresh Rewards";

/* No comment provided by engineer. */
"HappyFresh Rider" = "HappyFresh Rider";

/* No comment provided by engineer. */
"HappyFresh Subscription" = "Paket Langganan HappyFresh";

/* No comment provided by engineer. */
"HappyFresh Supermarket" = "HappyFresh Supermarket";

/* No comment provided by engineer. */
"HappyReferral" = "HappyReferral";

/* No comment provided by engineer. */
"Have you prepared replacements for all items?" = "Kamu sudah menyiapkan penggantian untuk semua item?";

/* No comment provided by engineer. */
"Help" = "Bantuan";

/* No comment provided by engineer. */
"Help center" = "Pusat bantuan";

/* No comment provided by engineer. */
"Help Center" = "Pusat Bantuan";

/* No comment provided by engineer. */
"Help personal shopper get the best items for you" = "Bantu personal shopper belanjakan barang terbaik";

/* No comment provided by engineer. */
"Here are the items you’ve saved from HappyFresh Supermarket." = "Ini adalah item-item yang kamu simpan dari HappyFresh Supermarket.";

/* No comment provided by engineer. */
"Hey our slots are full today" = "Hai, semua slot kami penuh hari ini";

/* No comment provided by engineer. */
"Hi %@!" = "Hai %@!";

/* No comment provided by engineer. */
"Hi!" = "Hai!";

/* No comment provided by engineer. */
"Hide details" = "Sembunyikan";

/* No comment provided by engineer. */
"Highest Item Price" = "Harga Barang Tertinggi";

/* No comment provided by engineer. */
"Highest Unit Price" = "Harga Unit Tertinggi";

/* No comment provided by engineer. */
"Home" = "Beranda";

/* No comment provided by engineer. */
"How are my points calculated?" = "Bagaimana poin saya dihitung?";

/* No comment provided by engineer. */
"How are your products replacements?" = "Bagaimana dengan produk penggantimu?";

/* No comment provided by engineer. */
"How did it go?" = "Bagaimana hasilnya?";

/* No comment provided by engineer. */
"How did they do?" = "Bagaimana kinerja mereka?";

/* No comment provided by engineer. */
"How do I cancel the subscription plan?" = "Bagaimana cara membatalkan paket langganan saya?";

/* No comment provided by engineer. */
"How do I contact customer service for Subscription?" = "Bagaimana cara menghubungi layanan pelanggan HappyFresh?";

/* No comment provided by engineer. */
"How do I earn points?" = "Bagaimana cara mendapatkan poin?";

/* No comment provided by engineer. */
"How do I know when my subscription start and renewal date?" = "Bagaimana cara mengetahui kapan paket saya aktif dan diperpanjang?";

/* No comment provided by engineer. */
"How do I use my subscription?" = "Bagaimana cara menggunakan paket langganan saya?";

/* No comment provided by engineer. */
"How many points can I redeem within a transaction?" = "Berapa poin yang bisa saya tukarkan dalam satu transaksi?\n";

/* No comment provided by engineer. */
"How subscription works" = "Ketentuan berlangganan";

/* No comment provided by engineer. */
"How was our Shopper and Rider?" = "How was our Shopper and Rider?";

/* No comment provided by engineer. */
"How was our Shopper?" = "Bagaimana Shopper kami?";

/* No comment provided by engineer. */
"How was your order?" = "Bagaimana pesananmu?";

/* No comment provided by engineer. */
"How were your product replacements?" = "Bagaimana dengan produk penggantimu?";

/* No comment provided by engineer. */
"I am experiencing problems with my order:" = "Saya mengalami masalah dengan pesanan:";

/* No comment provided by engineer. */
"I am facing a technical issue" = "Saya mengalami masalah teknis";

/* No comment provided by engineer. */
"I have an emergency and I need to cancel my order" = "Saya ada masalah darurat dan perlu membatalkan pesanan";

/* No comment provided by engineer. */
"I have double accounts" = "Saya memiliki akun ganda";

/* No comment provided by engineer. */
"I rarely use account" = "Saya jarang menggunakan akun";

/* No comment provided by engineer. */
"I understand and accept all of the above risks regarding account deletion." = "Saya memahami dan menerima semua risiko di atas terkait penghapusan akun.";

/* No comment provided by engineer. */
"I will not be available to receive my order" = "Saya tidak berada di tempat untuk menerima pesanan";

/* No comment provided by engineer. */
"I’ve moved to another country" = "Saya sudah pindah ke negara lain";

/* No comment provided by engineer. */
"If I am a gold customer and subscribed to Happyfresh subscription, do the points multiplier stack for more bonus points?" = "Saya sudah menjadi Gold Member dan juga berlangganan HappyFresh. Apakah poin dari Gold Member dan paket langganan dapat digabung agar saya bisa dapat lebih banyak bonus poin?";

/* No comment provided by engineer. */
"If out of stock, select how to replace:" = "Kalau tidak ada, ganti dengan cara:";

/* No comment provided by engineer. */
"If you go back, you will lose any changes you’ve made" = "Jika kembali, kamu akan kehilangan perubahan yang dibuat";

/* No comment provided by engineer. */
"If you'd like, specify replacements below!" = "Jika Kamu mau, tentukan penggantian di bawah ini!";

/* No comment provided by engineer. */
"in" = "pada";

/* %d is the estimated delivery time in minutes */
"In %d minutes" = "Dalam %d menit";

/* No comment provided by engineer. */
"In case out of stock, replace with:" = "Kalau stok habis, diganti dengan:";

/* No comment provided by engineer. */
"In this store, we process your order faster. But don't worry, we will pick the items with the best and freshest quality." = "Di toko ini, kami memproses pesanan lebih cepat. Permintaan tambahan terbatas. Tenang, kami tetap memilih yang terbaik untukmu";

/* No comment provided by engineer. */
"Information required" = "Informasi yang diperlukan";

/* No comment provided by engineer. */
"Ingredients:" = "Bahan baku:";

/* No comment provided by engineer. */
"Input password here" = "Masukkan sandi di sini";

/* No comment provided by engineer. */
"Input recipient name" = "Masukkan nama penerima";

/* No comment provided by engineer. */
"Input recipient phone number" = "Masukkan nomor telepon penerima";

/* No comment provided by engineer. */
"Inspirations" = "Inspirasi";

/* No comment provided by engineer. */
"Inspirations this week" = "Inspirasi minggu ini";

/* No comment provided by engineer. */
"Invalid Email" = "Email Tidak Valid";

/* No comment provided by engineer. */
"Invalid phone number or email" = "Nomor telepon atau email tidak valid";

/* No comment provided by engineer. */
"It was Okay" = "Okay";

/* No comment provided by engineer. */
"It's easy peasy! To cancel the subscription plan, you may go to 'Manage Subscription' in the My Subscription Page in the App.You can unsubscribe anytime and still enjoy the benefits until the next billing cycle." = "Kamu tinggal pilih menu 'Atur Paket Langganan' pada halaman 'Langganan Saya' di aplikasi. Kamu boleh berhenti berlangganan kapan saja. Jangan khawatir, semua layanan tetap bisa dinikmati sampai masa langganan yang sedang aktif berakhir.";

/* No comment provided by engineer. */
"Item cannot be modified" = "Item tidak bisa diubah";

/* No comment provided by engineer. */
"Item cannot be modified. In this store, we process orders faster. Extra requests are limited." = "Item tidak bisa diubah. Di toko ini, kami memproses pesanan lebih cepat. Permintaan tambahan terbatas. Tenang, kami tetap memilih yang terbaik untukmu";

/* No comment provided by engineer. */
"Item Not Available" = "Barang Tidak Tersedia";

/* it means, continued with list of what happens if the account is deleted */
"Just a quick reminder, if your account is deleted it means:" = "Sekedar mengingatkan, jika akun Anda dihapus artinya:";

/* No comment provided by engineer. */
"Just for you" = "Hanya untukmu";

/* No comment provided by engineer. */
"Just in case of low stock, we’re here to help!" = "Jika stok kurang, kami siap membantu!";

/* No comment provided by engineer. */
"Keep editing" = "Lanjutkan edit";

/* No comment provided by engineer. */
"Keep My Original Order" = "Pakai Pesanan Sebelumnya";

/* No comment provided by engineer. */
"Keep Original Order" = "Pakai Pesanan Sebelumnya";

/* No comment provided by engineer. */
"Keep up to date with our promotion!" = "Dapatkan informasi promo terbaru kami!";

/* No comment provided by engineer. */
"Keep updated on your order's status by allowing your notification permission." = "Selalu ketahui status pesanan Anda dengan mengaktifkan notifikasi Anda.";

/* No comment provided by engineer. */
"Knock knock! Your order has arrived!" = "Tok tok! Pesanan Anda telah sampai!";

/* No comment provided by engineer. */
"Lalamove" = "Lalamove";

/* No comment provided by engineer. */
"Language" = "Bahasa";

/* Shown on the last visited supplier */
"Last visited" = "Terakhir dikunjungi";

/* No comment provided by engineer. */
"Leave a note on your ideal replacement" = "Beri catatan untuk penggantian yang diinginkan";

/* No comment provided by engineer. */
"Leave order at my door." = "Tinggalkan di depan pintu.";

/* No comment provided by engineer. */
"Let our shopper pick" = "Biar shopper memilih";

/* No comment provided by engineer. */
"Let our shopper pick the rest." = "Biar shopper memilih sisanya";

/* No comment provided by engineer. */
"Let our team know" = "Beritahu tim kami";

/* No comment provided by engineer. */
"Let shopper assist you" = "Biar shopper yang membantumu";

/* No comment provided by engineer. */
"Let shopper pick" = "Biar shopper pilih";

/* No comment provided by engineer. */
"Let your personal shopper assist you?" = "Biar shopper yang membantumu?";

/* No comment provided by engineer. */
"Let’s do it" = "Yuk, ikutan";

/* No comment provided by engineer. */
"Let's go" = "Yuk!";

/* No comment provided by engineer. */
"Let's Go" = "Lanjut!";

/* No comment provided by engineer. */
"Limited" = "Terbatas";

/* No comment provided by engineer. */
"Limited Offer" = "Penawaran Terbatas";

/* No comment provided by engineer. */
"Limited slots, get yours now!" = "Slot terbatas, pilih sekarang!";

/* %@ will be replaced with the number of items */
"Limited to first %@ items per customers." = "Terbatas untuk  %@ item pertama per pelanggan";

/* No comment provided by engineer. */
"Loading your points..." = "Memuat poinmu...";

/* No comment provided by engineer. */
"Log In" = "Masuk";

/* No comment provided by engineer. */
"Log in to use best vouchers" = "Masuk untuk pakai voucher terbaik";

/* No comment provided by engineer. */
"Log Out" = "Keluar";

/* No comment provided by engineer. */
"Login" = "Masuk";

/* No comment provided by engineer. */
"Login or register" = "Masuk atau daftar";

/* No comment provided by engineer. */
"Login or signup" = "Masuk atau daftar";

/* No comment provided by engineer. */
"Login here" = "Masuk di sini";

/* No comment provided by engineer. */
"Login to continue" = "Masuk untuk lanjut";

/* No comment provided by engineer. */
"Login to see your previous orders" = "Login untuk lihat pesanan sebelumnya";

/* No comment provided by engineer. */
"Logout" = "Keluar";

/* No comment provided by engineer. */
"Looking for something?" = "Mencari sesuatu?";

/* No comment provided by engineer. */
"Low stock" = "Stok sedikit";

/* No comment provided by engineer. */
"Lowest Item Price" = "Harga Barang Terendah";

/* No comment provided by engineer. */
"Lowest Unit Price" = "Harga Unit Terendah";

/* No comment provided by engineer. */
"Make sure you’re logged in, or check the promos in your email." = "Pastikan kamu sudah login, atau cek promo di email.";

/* tab bar */
"Manage Account" = "Atur Akun";

/* No comment provided by engineer. */
"Manage Payment" = "Atur Pembayaran";

/* No comment provided by engineer. */
"Manage subscription" = "Atur paket langganan";

/* No comment provided by engineer. */
"Meanwhile, start shopping to get further updates" = "Sementara, mulai belanja untuk mendapatkan info terkini";

/* No comment provided by engineer. */
"month" = "bulan";

/* No comment provided by engineer. */
"More free items!" = "Produk gratis lainnya!";

/* %@ will be supermarket name */
"More from %@" = "Lainnya dari %@";

/* No comment provided by engineer. */
"More items coming your way!" = "Nantikan produk-produk lainnya ya!";

/* No comment provided by engineer. */
"Most Purchased" = "Paling sering dibeli";

/* No comment provided by engineer. */
"Most Saving" = "Terhemat";

/* tab bar */
"My Account" = "Akun Saya";

/* No comment provided by engineer. */
"My Address" = "Alamat Saya";

/* No comment provided by engineer. */
"My Cart" = "Troli saya";

/* No comment provided by engineer. */
"My lists" = "Daftarku";

/* No comment provided by engineer. */
"My Lists" = "List Saya";

/* No comment provided by engineer. */
"My order is late" = "Pesanan saya terlambat";

/* No comment provided by engineer. */
"My Orders" = "Pesanan Saya";

/* No comment provided by engineer. */
"My saved items" = "Item yang disimpan";

/* No comment provided by engineer. */
"My Subscription" = "Langganan saya";

/* No comment provided by engineer. */
"My Vouchers" = "Voucher Saya";

/* No comment provided by engineer. */
"Name" = "Nama";

/* No comment provided by engineer. */
"Need any help? Get immediate help on your issues or questions." = "Punya kendala atau pertanyaan? Segera hubungi layanan pelanggan kami.";

/* No comment provided by engineer. */
"Need help?" = "Perlu bantuan?";

/* No comment provided by engineer. */
"Need help? Check the FAQ" = "Butuh bantuan? Cek FAQ";

/* No comment provided by engineer. */
"Network Problem" = "Masalah Jaringan";

/* No comment provided by engineer. */
"NEW" = "BARU";

/* No comment provided by engineer. */
"New arrivals" = "Produk baru";

/* No comment provided by engineer. */
"New basket added\nTap cart view to see all your baskets!" = "Troli baru ditambah\nKetuk lihat troli untuk melihat seluruh troli!";

/* No comment provided by engineer. */
"New Place for Your Carts!" = "Tempat baru untuk keranjangmu!";

/* No comment provided by engineer. */
"NEW UPDATE" = "PEMBARUAN BARU";

/* No comment provided by engineer. */
"Next" = "Lanjut";

/* Example: No available voucher for Discount/Bank (promotion type) */
"No available vouchers for %@" = "Voucher tidak tersedia untuk %@";

/* No comment provided by engineer. */
"No available vouchers for Bank/E-Wallet" = "Voucher tidak tersedia untuk Bank/E-Wallet";

/* No comment provided by engineer. */
"No available vouchers for Discount" = "Voucher tidak tersedia untuk Diskon";

/* No comment provided by engineer. */
"No call/chat unless urgent." = "Tidak telepon/chat kecuali mendesak.";

/* Context: Promotion code */
"No code required" = "Kode tidak diperlukan";

/* No comment provided by engineer. */
"No Connection" = "Tanpa Koneksi";

/* No comment provided by engineer. */
"No hidden charges. You can cancel anytime." = "Tidak ada biaya berhenti langganan. Kamu bisa berhenti langganan kapan saja";

/* %@ is the date */
"No more slots for %@" = "slot habis untuk %@";

/* No comment provided by engineer. */
"No more slots today" = "Slot hari ini habis";

/* No comment provided by engineer. */
"No name specified" = "Nama belum ditentukan";

/* No comment provided by engineer. */
"No orders yet" = "Belum ada pesanan";

/* No comment provided by engineer. */
"No points available for redemption" = "Tidak ada poin yang tersedia untuk ditukarkan";

/* No comment provided by engineer. */
"No quantity remaining" = "Tidak ada jumlah tersisa";

/* No comment provided by engineer. */
"No Recommendations Yet" = "Tidak Ada Rekomendasi";

/* No comment provided by engineer. */
"No Result" = "Tak Ada Hasil";

/* No comment provided by engineer. */
"No updates yet" = "Belum ada pembaruan";

/* No comment provided by engineer. */
"No worries, you can change your delivery time to when you'd be available! If you're facing another issue, check our FAQ for more details." = "Jangan khawatir, kamu bisa ubah waktu pengantaran yang diinginkan! Jika kamu menghadapi masalah lainnya, cek FAQ kami untuk info lebih lanjut.";

/* No comment provided by engineer. */
"No, keep the cart" = "Jangan hapus troli";

/* No comment provided by engineer. */
"No, keep voucher" = "Gunakan voucher";

/* No comment provided by engineer. */
"No, thanks" = "Tidak, terima kasih";

/* No comment provided by engineer. */
"Not available" = "Tidak tersedia";

/* No comment provided by engineer. */
"Not available for delivery on Saturday, Sunday, and Public Holidays." = "Pengantaran tidak tersedia di hari Sabtu, Minggu, dan libur nasional.";

/* No comment provided by engineer. */
"Not available in your selected store" = "Tidak tersedia di toko yang Anda pilih";

/* No comment provided by engineer. */
"Not in coverage area" = "Di luar area jangkauan";

/* No comment provided by engineer. */
"Not satisfied with the service" = "Tidak puas dengan layanannya";

/* No comment provided by engineer. */
"Not so good" = "Tidak terlalu baik";

/* No comment provided by engineer. */
"Note saved" = "Catatan";

/* No comment provided by engineer. */
"Notes to shopper:" = "Catatan untuk shopper:";

/* No comment provided by engineer. */
"Notes: %@" = "Catatan: %@";

/* No comment provided by engineer. */
"Notification" = "Notifikasi";

/* No comment provided by engineer. */
"Notify me when the sale starts" = "Beritahu saya saat sale dimulai";

/* No comment provided by engineer. */
"Now you can apply your Bank/e-Wallet promos here!" = "Kini kamu bisa pakai promo Bank/e-Wallet di sini!";

/* No comment provided by engineer. */
"Now you can easily access your pending carts here!" = "Sekarang kamu bisa mengakses daftar belanjaan yang masih tersimpan dengan mudah!";

/* No comment provided by engineer. */
"Often bought with" = "Sering dibeli bersama";

/* %@ will be replaced with stock location name. */
"Oh hey, you had some items from %@" = "Oh hi, kamu memiliki beberapa item dari %@";

/* No comment provided by engineer. */
"Ok" = "Ok";

/* No comment provided by engineer. */
"OK" = "OK";

/* No comment provided by engineer. */
"On sale" = "Diskon";

/* No comment provided by engineer. */
"On Sale" = "Diskon";

/* No comment provided by engineer. */
"One last thing..." = "Satu hal terakhir...";

/* No comment provided by engineer. */
"Only %d of these items are on promotion, the rest will be at normal price" = "Hanya %d dari produk ini dapat potongan harga, sisanya harga normal";

/* No comment provided by engineer. */
"Oops!" = "Ups!";

/* No comment provided by engineer. */
"Open Settings" = "Buka Pengaturan";

/* No comment provided by engineer. */
"Opening hours may be affected by holidays or other exceptional circumstances." = "Jam buka mungkin terpengaruh oleh hari libur atau tergantung situasi tertentu.";

/* No comment provided by engineer. */
"Optional" = "Opsional";

/* No comment provided by engineer. */
"Or" = "Atau";

/* No comment provided by engineer. */
"Or check our categories" = "Atau cek kategori kami";

/* No comment provided by engineer. */
"Or, you can find it in other location" = "Atau, kamu bisa mencarinya di lokasi lain";

/* No comment provided by engineer. */
"Or, you can find it in other stores" = "Atau, kamu bisa mencarinya di toko lain";

/* No comment provided by engineer. */
"Or login / signup with" = "Atau masuk / daftar dengan";

/* No comment provided by engineer. */
"Order canceled" = "Pesanan dibatalkan";

/* No comment provided by engineer. */
"Order cancelled" = "Pesanan dibatalkan";

/* No comment provided by engineer. */
"Order Cancelled" = "Pesanan Dibatalkan";

/* No comment provided by engineer. */
"Order confirmed" = "Pesanan dikonfirmasi";

/* No comment provided by engineer. */
"Order delivered" = "Pesanan terkirim";

/* No comment provided by engineer. */
"Order details" = "Detail pesanan";

/* No comment provided by engineer. */
"Order failed" = "Pesananan gagal";

/* No comment provided by engineer. */
"Order Failed!" = "Pemesanan Gagal!";

/* No comment provided by engineer. */
"Order Fee" = "Biaya Pesanan";

/* No comment provided by engineer. */
"Order Number" = "Nomor Pesanan";

/* No comment provided by engineer. */
"Order number copied to clipboard" = "Nomor pesanan telah disalin";

/* No comment provided by engineer. */
"Order number is missing." = "Nomor pesanan kosong.";

/* No comment provided by engineer. */
"Order received" = "Pesanan diterima";

/* No comment provided by engineer. */
"Order status" = "Status pesanan";

/* No comment provided by engineer. */
"Order Success!" = "Pemesanan Berhasil!";

/* No comment provided by engineer. */
"Order Updated" = "Pesanan Diperbarui";

/* No comment provided by engineer. */
"Order" = "Pesanan";

/* No comment provided by engineer. */
"Other" = "Lainnya";

/* No comment provided by engineer. */
"Other Options" = "Opsi Lain";

/* No comment provided by engineer. */
"Others" = "Lainnya";

/* No comment provided by engineer. */
"Our coverage area" = "Jangkauan area kami";

/* No comment provided by engineer. */
"Our Quality Promise" = "Janji Kualitas Kami";

/* No comment provided by engineer. */
"Our Riders are fully booked at the moment" = "Rider kami sedang tidak tersedia";

/* No comment provided by engineer. */
"Our shopper won't replace this item if OOS" = "Shopper kami tidak akan mengganti barang jika habis";

/* No comment provided by engineer. */
"Out of Range" = "Di Luar Jangkauan";

/* No comment provided by engineer. */
"Out of stock" = "Stok habis";

/* No comment provided by engineer. */
"Out of Stock" = "Stok Habis";

/* No comment provided by engineer. */
"Packaging" = "Kemasan";

/* No comment provided by engineer. */
"Pantry & Ingredients" = "Bahan Baku & Pantry";

/* No comment provided by engineer. */
"Password confirmation" = "Konfirmasi kata sandi";

/* No comment provided by engineer. */
"Password Reset" = "Setel Ulang Sandi";

/* No comment provided by engineer. */
"Past items" = "Barang sebelumnya";

/* No comment provided by engineer. */
"PAST MONTH" = "BULAN LALU";

/* No comment provided by engineer. */
"Past order details" = "Detil order sebelumnya";

/* No comment provided by engineer. */
"Past orders" = "Order sebelumnya";

/* No comment provided by engineer. */
"Pay securely" = "Bayar dengan aman";

/* No comment provided by engineer. */
"Payment" = "Pembayaran";

/* No comment provided by engineer. */
"Payment & benefits are renewed each month" = "Paket dan tagihan akan diperpanjang setiap bulan";

/* No comment provided by engineer. */
"Payment Deals" = "Promo Pembayaran";

/* No comment provided by engineer. */
"per" = "per";

/* No comment provided by engineer. */
"per month" = "per bulan";

/* No comment provided by engineer. */
"Per Month" = "Per Bulan";

/* No comment provided by engineer. */
"Per Year" = "Per Tahun";

/* No comment provided by engineer. */
"Personal shopper" = "Personal shopper";

/* No comment provided by engineer. */
"Phone number" = "Nomor telepon";

/* No comment provided by engineer. */
"Phone number or email is invalid. Please try another one" = "Nomor telepon atau email tidak valid. Silakan coba yang lain";

/* No comment provided by engineer. */
"Phone Number Verification" = "Verifikasi Telepon";

/* No comment provided by engineer. */
"Photo gallery" = "Galeri foto";

/* No comment provided by engineer. */
"Pick a delivery slot" = "Pilih slot pengantaran";

/* No comment provided by engineer. */
"Please add your free item(s) to cart." = "Silakan tambahkan barang gratis Anda ke troli.";

/* No comment provided by engineer. */
"Please allow your device's location, to detect your delivery address" = "Berikan izin akses lokasi perangkat untuk mengetahui alamat pengirimanmu";

/* No comment provided by engineer. */
"Please check back later." = "Silahkan periksa kembali nanti";

/* No comment provided by engineer. */
"Please check other deals." = "Cek Penawaran Lain";

/* No comment provided by engineer. */
"Please check your connection and try again" = "Mohon periksa jaringan dan coba lagi.";

/* No comment provided by engineer. */
"Please check your connection and try again." = "Mohon periksa jaringan dan coba lagi.";

/* No comment provided by engineer. */
"Please choose your replacement options:" = "Silahkan pilih opsi penggantianmu:";

/* No comment provided by engineer. */
"Please complete your address detail" = "Mohon lengkapi detail alamat";

/* No comment provided by engineer. */
"Please enter a valid phone number, such as +%@123456789" = "Silakan masukkan nomor telepon yang valid, contoh +%@123456789";

/* No comment provided by engineer. */
"Please enter the 4 digits code that we sent to" = "Mohon masukkan 4 digit kode terkirim";

/* No comment provided by engineer. */
"Please find other keywords or check other recommendations." = "Gunakan kata kunci lain atau cek rekomendasi";

/* No comment provided by engineer. */
"Please find other keywords or check other replacement items." = "Silahkan pakai kata kunci lain atau cek item pengganti lainnya.";

/* No comment provided by engineer. */
"Please find your replacement using the search bar above." = "Temukan penggantimu menggunakan bar pencarian di atas.";

/* No comment provided by engineer. */
"Please give us a few seconds." = "Mohon tunggu sebentar.";

/* No comment provided by engineer. */
"Please input correct phone number" = "Silakan masukkan nomor telepon yang benar";

/* Text between <b> and </b> will be bold. %@ will be replaced with phone number. */
"Please input OTP code that has been sent to <b>%@</b>" = "Masukkan kode OTP yang telah dikirimkan ke <b>%@</b>";

/* No comment provided by engineer. */
"Please note that:" = "Mohon dicatat bahwa:";

/* No comment provided by engineer. */
"Please select your delivery time" = "Silakan pilih waktu pengantaran Anda";

/* No comment provided by engineer. */
"Please try again" = "Silakan coba lagi";

/* No comment provided by engineer. */
"Please try again later" = "Silakan coba kembali nanti";

/* No comment provided by engineer. */
"Please try later or choose a scheduled slot." = "Silakan coba nanti atau pilih slot terjadwal.";

/* No comment provided by engineer. */
"Point boosters" = "Point boosters";

/* %@ will be replaced by the app with the point amount. */
"Point Redemption (%@ points)" = "Penukaran Poin (%@ poin)";

/* No comment provided by engineer. */
"Points are calculated based on the final price of your order, plus any additional bonus points earned." = "Poin dihitung berdasarkan harga akhir pesananmu, ditambah poin bonus tambahan yang diperoleh.";

/* No comment provided by engineer. */
"Poor Quality" = "Kualitas Buruk";

/* %@ will be the store name. */
"Popular selections from %@" = "Produk populer dari %@";

/* No comment provided by engineer. */
"Popularity" = "Popularitas";

/* No comment provided by engineer. */
"Portion" = "Porsi";

/* No comment provided by engineer. */
"Prepare your replacement item" = "Siapkan item penggantianmu";

/* No comment provided by engineer. */
"Pretty quiet here…" = "Di sini tampak sepi...";

/* No comment provided by engineer. */
"Previously using Lineman-Mart?" = "Sebelumnya menggunakan Lineman-Mart?";

/* No comment provided by engineer. */
"Prices may change and be updated when you edit your current order." = "Harga mungkin berubah dan diperbarui ketika Anda mengubah pesanan Anda.";

/* No comment provided by engineer. */
"Prioritized slot" = "Slot prioritas";

/* No comment provided by engineer. */
"Privacy Policies" = "Kebijakan Privasi";

/* No comment provided by engineer. */
"Privacy Policy" = "Kebijakan Privasi";

/* No comment provided by engineer. */
"Proceed to checkout" = "Lanjutkan untuk checkout";

/* No comment provided by engineer. */
"Product description" = "Deskripsi produk";

/* No comment provided by engineer. */
"Product failed to add" = "Produk gagal ditambahkan";

/* No comment provided by engineer. */
"Product with similar price" = "Produk dengan harga serupa";

/* No comment provided by engineer. */
"Products by category" = "Produk sesuai kategori";

/* No comment provided by engineer. */
"Products Replacement" = "Penggantian Produk";

/* No comment provided by engineer. */
"Promo unavailable" = "Promo tidak tersedia";

/* No comment provided by engineer. */
"Promo" = "Promosi";

/* No comment provided by engineer. */
"Promotion" = "Promotion";

/* No comment provided by engineer. */
"Promotion Details" = "Detail Promosi";

/* No comment provided by engineer. */
"Promotions" = "Promosi";

/* No comment provided by engineer. */
"Promotions First" = "Promosi Dahulu";

/* No comment provided by engineer. */
"Provided by" = "Disediakan oleh";

/* No comment provided by engineer. */
"Quickly redirecting you..." = "Segera mengarahkanmu...";

/* No comment provided by engineer. */
"Quite Happy" = "Sedikit Puas";

/* No comment provided by engineer. */
"Rate replacement" = "Nilai penggantian";

/* No comment provided by engineer. */
"Rate Shopper & Rider" = "Nilai Shopper & Rider";

/* No comment provided by engineer. */
"Rate Us" = "Nilai Kami";

/* No comment provided by engineer. */
"Rate your order" = "Nilai pesananmu";

/* No comment provided by engineer. */
"Re-send" = "Kirim lagi";

/* No comment provided by engineer. */
"Reach limit promo quantity" = "Mencapai batas jumlah promo";

/* No comment provided by engineer. */
"Reactivate my subscription" = "Aktivasi lagi paket langganan saya";

/* No comment provided by engineer. */
"Read more" = "Baca lainnya";

/* No comment provided by engineer. */
"Ready for the Flash Sale?" = "Siap untuk ikut Flash Sale?";

/* No comment provided by engineer. */
"receive by" = "diterima";

/* %@ is max shipping time */
"Receive by %@" = "Diterima %@";

/* No comment provided by engineer. */
"Recent Purchased" = "Yang baru dibeli";

/* No comment provided by engineer. */
"Recipe Details" = "Detil Resep";

/* No comment provided by engineer. */
"Recipes" = "Resep";

/* No comment provided by engineer. */
"Recipient name" = "Nama penerima";

/* No comment provided by engineer. */
"Recipient phone number" = "Nomor telepon penerima";

/* No comment provided by engineer. */
"Recommendations" = "Rekomendasi";

/* No comment provided by engineer. */
"Recommended" = "Rekomendasi";

/* No comment provided by engineer. */
"Recommended replacements" = "Rekomendasi penggantian";

/* %@ will be replaced by the app with the point amount. */
"Redeem %@ points?" = "Tukar %@ poin?";

/* %@ will be store name. */
"Redirecting to %@" = "Diarahkan ke %@";

/* No comment provided by engineer. */
"Refer Friends and Earn!" = "Ajak Teman dan Dapatkan!";

/* No comment provided by engineer. */
"Referral" = "Referral";

/* No comment provided by engineer. */
"Refresh page" = "Muat ulang ";

/* No comment provided by engineer. */
"Refund Balance" = "Saldo Pengembalian Dana";

/* No comment provided by engineer. */
"Register now or log in to save and see your favorite products." = "Daftar sekarang atau masuk untuk simpan dan lihat produk favoritmu";

/* No comment provided by engineer. */
"Register or Log in" = "Daftar atau Masuk";

/* No comment provided by engineer. */
"Register or log in to share your referral code with your friends and enjoy the benefits!" = "Daftar sekarang atau masuk untuk membagikan kode referral ke temanmu dan nikmati keuntungannya!";

/* No comment provided by engineer. */
"Register to switch store without losing your other baskets." = "Daftar untuk mengganti toko tanpa kehilangan pesananmu lainnya";

/* No comment provided by engineer. */
"Rejected: %ld" = "Ditolak: %ld";

/* No comment provided by engineer. */
"Relevance" = "Relevansi";

/* No comment provided by engineer. */
"Remove" = "Hapus";

/* No comment provided by engineer. */
"Reorder" = "Pesan lagi";

/* No comment provided by engineer. */
"Reorder now" = "Pesan lagi";

/* No comment provided by engineer. */
"Reorder now!" = "Pesan lagi!";

/* No comment provided by engineer. */
"Replacement Not Accepted" = "Pengganti Tidak Diterima";

/* No comment provided by engineer. */
"Replacement rating submitted. Thank you!" = "Replacement rating submitted. Thank you!";

/* No comment provided by engineer. */
"Request" = "Minta";

/* No comment provided by engineer. */
"Request for restock" = "Minta stok ulang";

/* No comment provided by engineer. */
"Request New Store" = "Permohonan Toko Baru";

/* No comment provided by engineer. */
"Request New Stores" = "Minta Toko Baru";

/* No comment provided by engineer. */
"Request store" = "Minta Toko";

/* No comment provided by engineer. */
"Request To Deliver Here" = "Minta Untuk Dikirim ke Sini";

/* No comment provided by engineer. */
"Requested" = "Diminta";

/* No comment provided by engineer. */
"Resend code" = "Kirim ulang ";

/* No comment provided by engineer. */
"Resend Code" = "Kirim Ulang";

/* No comment provided by engineer. */
"Reset your password" = "Atur ulang kata sandi";

/* No comment provided by engineer. */
"Restock Soon" = "Akan tersedia lagi";

/* No comment provided by engineer. */
"Restore my purchase" = "Ulang kembali pembelian saya";

/* No comment provided by engineer. */
"Restock your daily essentials" = "Stok ulang kebutuhan harianmu";

/* No comment provided by engineer. */
"Retry payment" = "Ulang pembayaran";

/* No comment provided by engineer. */
"Review your replacement" = "Review penggantianmu";

/* No comment provided by engineer. */
"Sadly, the address is not in the coverage area of this store:\n" = "Mohon maaf, alamat berada di luar area jangkauan toko ini:";

/* No comment provided by engineer. */
"Same brand, but another flavour" = "Merek sama, tapi beda rasa";

/* No comment provided by engineer. */
"Same brand, but another size" = "Merek sama, tapi beda ukuran";

/* No comment provided by engineer. */
"Save" = "Simpan";

/* No comment provided by engineer. */
"Save address" = "Simpan alamat";

/* No comment provided by engineer. */
"Save all your baskets" = "Simpan semua isi keranjang";

/* No comment provided by engineer. */
"Save more with delivery promo" = "Lebih hemat dengan promo ongkir";

/* No comment provided by engineer. */
"Save time by adding your favorite items!" = "Hemat waktu dengan menambah barang yang pernah Anda sukai!";

/* Text between <b> and </b> will be bold. %@ will be replaced with price. */
"Save your <b>%@</b> savings before" = "Hemat <b>%@</b> sebelum";

/* No comment provided by engineer. */
"Saved cart - %ld Product" = "Troli tersimpan - %ld Produk";

/* No comment provided by engineer. */
"Saved cart - %ld Products" = "Troli tersimpan - %ld Produk";

/* No comment provided by engineer. */
"Saver" = "Hemat";

/* No comment provided by engineer. */
"Scheduled" = "Terjadwal";

/* No comment provided by engineer. */
"Search" = "Cari";

/* No comment provided by engineer. */
"Search area, city or street name" = "Cari area, kota atau nama jalan";

/* No comment provided by engineer. */
"Search brand..." = "Cari merek...";

/* No comment provided by engineer. */
"Search for" = "Mencari";

/* No comment provided by engineer. */
"Search for \"%@\"" = "Cari untuk \"%@\"";

/* No comment provided by engineer. */
"Search product or supermarket/store" = "Cari produk atau supermarket/store";

/* %@ will be replaced by store name */
"Search in %@" = "Cari di %@";

/* No comment provided by engineer. */
"Search product in" = "Cari produk di";

/* No comment provided by engineer. */
"Search for products and brands" = "Cari produk dan merk";

/* No comment provided by engineer. */
"See all" = "Lihat semua";

/* %d will be replaced by number */
"See all %d outlets" = "Lihat semua %d outlet";

/* No comment provided by engineer. */
"See details" = "Lihat detail";

/* No comment provided by engineer. */
"See more" = "Lihat lainnya";

/* No comment provided by engineer. */
"See more promo" = "Promo lainnya";

/* No comment provided by engineer. */
"See order details" = "Lihat detail pesanan";

/* No comment provided by engineer. */
"See you next time" = "Sampai ketemu lagi";

/* No comment provided by engineer. */
"Select address" = "Pilih alamat";

/* No comment provided by engineer. */
"Select another day to find a delivery slot that suits you!" = "Pilih hari lainnya untuk menemukan slot pengantaran yang cocok untukmu!";

/* No comment provided by engineer. */
"Select delivery option" = "Pilih opsi pengantaran";

/* No comment provided by engineer. */
"Select delivery option:" = "Pilih opsi pengantaran:";

/* No comment provided by engineer. */
"Select how to get your OTP code" = "Pilih opsi untuk menerima kode OTP";

/* No comment provided by engineer. */
"Select language" = "Pilih bahasa";

/* No comment provided by engineer. */
"Select one voucher" = "Pilih satu voucher";

/* No comment provided by engineer. */
"Select product replacement" = "Pilih produk penggantian";

/* No comment provided by engineer. */
"Select slot now" = "Pilih slot sekarang";

/* No comment provided by engineer. */
"Select Store" = "Pilih Toko";

/* No comment provided by engineer. */
"Select store" = "Pilih toko";

/* No comment provided by engineer. */
"Select store to start shopping" = "Pilih toko untuk mulai berbelanja";

/* No comment provided by engineer. */
"Select verification method" = "Pilih metode verifikasi";

/* No comment provided by engineer. */
"Select your service:" = "Pilih layananmu:";

/* No comment provided by engineer. */
"Send OTP" = "Kirim OTP";

/* No comment provided by engineer. */
"Service fee" = "Biaya layanan";

/* No comment provided by engineer. */
"Share Recipe" = "Bagikan Resep";

/* No comment provided by engineer. */
"Share your location" = "Bagikan lokasi Anda";

/* No comment provided by engineer. */
"Shop from one of these top categories" = "Belanja dari salah satu kategori teratas";

/* No comment provided by engineer. */
"Shop now" = "Belanja sekarang";

/* No comment provided by engineer. */
"Shop Now!" = "Belanja sekarang!";

/* No comment provided by engineer. */
"Shopper has already started selecting your items. Order cannot be edited." = "Pembelanja telah mulai memilih barang Anda. Pesanan tidak dapat diubah.";

/* No comment provided by engineer. */
"Shopper Notes Not Fulfilled" = "Catatan Pembelanja Tidak Terpenuhi";

/* No comment provided by engineer. */
"Shopper will call/chat." = "Shopper akan menelepon/chat.";

/* No comment provided by engineer. */
"Shopping your order" = "Membeli pesananmu";

/* No comment provided by engineer. */
"Show all items" = "Tampilkan semua item";

/* No comment provided by engineer. */
"Show all store locations" = "Tampilkan semua lokasi toko";

/* No comment provided by engineer. */
"Show all vouchers" = "Perlihatkan semua voucher";

/* No comment provided by engineer. */
"Show less items" = "Tampilkan lebih sedikit";

/* No comment provided by engineer. */
"Show more items" = "Tampilkan lebih banyak";

/* No comment provided by engineer. */
"Show my list" = "Lihat Daftarku";

/* No comment provided by engineer. */
"Sign Up" = "Daftar";

/* No comment provided by engineer. */
"Sign up now or log in to save and see your favourite products!" = "Daftar sekarang atau masuk untuk menyimpan dan melihat produk favorit Anda!";

/* No comment provided by engineer. */
"Sign up or log in to confirm your HappyFresh Subscription purchase" = "Masuk atau daftar untuk mengonfirmasi pembelian paket Langganan HappyFresh kamu";

/* No comment provided by engineer. */
"Silver Member" = "Silver Member";

/* No comment provided by engineer. */
"Similar Items" = "Item serupa";

/* No comment provided by engineer. */
"Similar products" = "Produk serupa";

/* No comment provided by engineer. */
"Skip" = "Lewati";

/* No comment provided by engineer. */
"Skip and clear my cart" = "Lewatkan dan hapus troli";

/* No comment provided by engineer. */
"Slots are running low now, you may want to checkout sooner" = "Slot di atas akan segera habis, kamu mungkin ingin checkout lebih awal";

/* No comment provided by engineer. */
"Smart savings with exclusive deals" = "Promo eksklusif untuk belanja lebih hemat";

/* No comment provided by engineer. */
"Snacks" = "Makanan Ringan";

/* No comment provided by engineer. */
"Some of the items you added to cart went out of stock. Please choose other products." = "Beberapa barang yang Anda pilih stoknya habis. Silakan pilih produk lain.";

/* No comment provided by engineer. */
"Something went wrong" = "Terjadi kesalahan";

/* Default error message */
"Something went wrong, please try again later." = "Sesuatu yang salah terjadi, silakan coba kembali nanti.";

/* Message in alert pop up */
"Something went wrong. Please try again later" = "Terjadi kesalahan. Silahkan nanti coba kembali.";

/* No comment provided by engineer. */
"Sorry to hear about that! You can contact us to get more info on your order." = "Maaf atas ketidaknyamanannya! Kamu bisa menghubungi kami untuk info lebih lanjut tentang pesananmu. ";

/* No comment provided by engineer. */
"Sorry to hear about that..." = "Mohon maaf ya...";

/* No comment provided by engineer. */
"Sorry we can't find good recommendations" = "Maaf kami tidak bisa mencari rekomendasi yang bagus ";

/* No comment provided by engineer. */
"Sorry we couldn’t find \"{query}\"" = "Maaf kami tidak bisa mencari \"{query}\"";

/* No comment provided by engineer. */
"Sorry we couldn't find \"%@\"" = "Maaf, kami tidak dapat menemukan \"%@\"";

/* No comment provided by engineer. */
"Sorry, all delivery slots are full" = "Maaf, semua slot pengantaran penuh";

/* No comment provided by engineer. */
"Sorry, this area is out of coverage" = "Maaf, area ini di luar jangkauan";

/* No comment provided by engineer. */
"Sorry, we can't delete your account for now." = "Maaf, untuk sekarang akun kamu belum bisa dihapus";

/* Text between <b> and </b> will be bold. %@ will be store name */
"Sorry, we can't find <b>“%@”</b> in %@." = "Maaf, kami tidak bisa menemukan <b>“%1$@”</b> di %2$@.";

/* No comment provided by engineer. */
"Sorry, we couldn’t find deals" = "Maaf, kami tidak dapat menemukan penawaran.";

/* No comment provided by engineer. */
"Sorry, We haven't served your area yet." = "Maaf, kami belum melayani areamu";

/* No comment provided by engineer. */
"Sorry, we've started processing your order by now. Have worries about your order? Contact our team to cancel. You can cancel before our shopper starts." = "Maaf, kami sudah mulai memproses pesananmu sekarang. Masih khawatir dengan pesananmu? Hubungi tim kami untuk membatalkannya. Kamu bisa membatalkan sebelum shopper kami mulai berbelanja.";

/* No comment provided by engineer. */
"Sorry! You can no longer edit this order as your shopper is about to start picking your items." = "Maaf! Anda tidak dapat mengubah pesanan ini lagi karena pembelanja sudah mulai mengambil barang.";

/* No comment provided by engineer. */
"Sorry! Your total order must be a minimum of <strong>%@</strong>." = "Maaf! total pesanan Anda minimal sejumlah <strong>%@</strong>.";

/* No comment provided by engineer. */
"Sort by" = "Sort by";

/* No comment provided by engineer. */
"Sort by:" = "Urutkan:";

/* No comment provided by engineer. */
"Special delivery slots" = "Slot pengantaran spesial";

/* No comment provided by engineer. */
"Special delivery slots, customer support, and more!" = "Slot pengantaran spesial, pusat bantuan, dan lainnya!";

/* No comment provided by engineer. */
"Special offers for you" = "Promo spesial untukmu";

/* No comment provided by engineer. */
"Specials" = "Spesial";

/* No comment provided by engineer. */
"Specialty" = "Khusus";

/* No comment provided by engineer. */
"Ssst.. you may be the first person to find this promo. Care to share with your loved ones?" = "Ssst .. Kamu mungkin orang pertama yang menemukan promo ini. Mau berbagi dengan orang tersayang?";

/* No comment provided by engineer. */
"Start new basket" = "Mulai troli baru";

/* No comment provided by engineer. */
"Start shopping" = "Mulai berbelanja";

/* No comment provided by engineer. */
"Start Shopping" = "Mulai Berbelanja";

/* No comment provided by engineer. */
"Start shopping below" = "Mulai belanja di bawah";

/* No comment provided by engineer. */
"Start shopping NOW to get your special discount 🛒" = "Mulai belanja SEKARANG untuk dapatkan diskon spesial 🛒";

/* No comment provided by engineer. */
"Start shopping to create an order!" = "Mulai berbelanja untuk membuat pesanan!";

/* No comment provided by engineer. */
"Start shopping to get order updates flowing in!" = "Mulai berbelanja untuk mendapatkan info pesanan!";

/* No comment provided by engineer. */
"Starting date - is when the payment is made and the next renewal date will be reflected in 'Manage my Subscription' section in the app. If you have canceled the subscription, the end date will also be reflected in the 'Manage my Subscription' section." = "Paket langganan akan aktif setelah kamu melakukan pembayaran. Jika memutuskan untuk berhenti berlangganan, tanggal berakhirnya paket bisa kamu lihat di menu 'Atur Paket Langganan Saya'";

/* No comment provided by engineer. */
"Stay in Current Store" = "Tetap pada Toko Saat Ini";

/* No comment provided by engineer. */
"Stay tuned for more VIP services!" = "Nantikan fasilitas VIP lainnya!";

/* No comment provided by engineer. */
"Still didn’t get the message?" = "Masih belum dapat pesan?";

/* No comment provided by engineer. */
"Still haven't received the sms? Resend Code" = "Masih belum menerima sms? Kirim Ulang Kode";

/* No comment provided by engineer. */
"Stock up essentials!" = "Stok ulang kebutuhanmu!";

/* No comment provided by engineer. */
"Stores" = "Toko";

/* No comment provided by engineer. */
"Stores Near You" = "Toko di sekitarmu";

/* No comment provided by engineer. */
"Submit" = "Kirim";

/* No comment provided by engineer. */
"Subscription plan is meant for personal use but you may use it to buy groceries for your whole family." = "Paket langganan hanya untuk 1 akun. Namun, kamu bisa berbelanja kebutuhan seluruh anggota keluarga.";

/* No comment provided by engineer. */
"Success! All the changes in this order are already confirmed and your shopper will be informed." = "Sukses! semua perubahan pada pesanan ini telah dikonfirmasi dan akan diketahui pembelanja kami.";

/* No comment provided by engineer. */
"Success! Bundle added to your cart." = "Berhasil! Bundel telah ditambahkan ke troli Anda.";

/* No comment provided by engineer. */
"Supermarket" = "Supermarket";

/* No comment provided by engineer. */
"Support" = "Bantuan";

/* No comment provided by engineer. */
"Sure, take me there" = "Tentu, antarkan saya";

/* No comment provided by engineer. */
"Switch to another account to access your subscription benefits" = "Ganti akun untuk menikmati paket langganan";

/* No comment provided by engineer. */
"T&C" = "S&K";

/* No comment provided by engineer. */
"T&Cs" = "S&K";

/* No comment provided by engineer. */
"Tap <b>\"Add More\"</b> to go back to the home screen and shop in this store!" = "Ketuk <b>\"Tambah Lainnya\"</b> untuk kembali ke halaman utama dan berbelanja di toko ini!";

/* No comment provided by engineer. */
"Tap <strong>\"Change Store\"</strong> to start a basket on this store. We will save the basket you have with other supermarkets." = "Ketuk <strong>\"Ganti Toko\"</strong> dan kami akan menyimpan troli yang Anda miliki pada supermarket lain!";

/* No comment provided by engineer. */
"Tap here to pin your location." = "Ketuk di sini untuk letakkan lokasi Anda.";

/* No comment provided by engineer. */
"Tap here to track your order and special offers." = "Klik di sini untuk informasi pesanan dan promo menarik.";

/* No comment provided by engineer. */
"Tell us" = "Beritahu kami";

/* No comment provided by engineer. */
"Tell us and be the first know when we have them." = "Jadilah yang pertama tahu saat kami mendapatkannya.";

/* No comment provided by engineer. */
"Terms & conditions" = "Syarat & ketentuan";

/* No comment provided by engineer. */
"Terms and Conditions" = "Syarat dan Ketentuan";

/* No comment provided by engineer. */
"TERMS AND CONDITIONS" = "SYARAT DAN KETENTUAN";

/* No comment provided by engineer. */
"Thank you for your order" = "Terima kasih untuk pesananmu";

/* No comment provided by engineer. */
"Thank you!" = "Terima kasih!";

/* No comment provided by engineer. */
"Thanks for shopping with us. For safety reasons, please reset your GrabFresh password here." = "Terima kasih sudah berbelanja bersama kami. Untuk alasan keamanan, mohon setel ulang password GrabFresh di sini.";

/* No comment provided by engineer. */
"Thanks for shopping with us. For safety reasons, please reset your Lineman password here." = "Terima kasih sudah berbelanja dengan kami. Demi alasan keamanan, mohon reset password Lineman di sini.";

/* No comment provided by engineer. */
"Thanks! We will email as soon as you can shop these stores." = "Terima kasih! Kami akan email segera saat Anda bisa berbelanja di toko ini.";

/* No comment provided by engineer. */
"That’s great to hear!" = "Senang mendengarnya!";

/* No comment provided by engineer. */
"The easiest and quick way for your favorite orders!" = "Praktis dan cepat untuk pesanan favorit kamu!";

/* No comment provided by engineer. */
"The operation couldn’t be completed." = "Operasi ini tidak dapat diselesaikan.";

/* 'backslash n' means new line */
"The price is an estimation.\nIt will be updated based on weight." = "Harga hanya perkiraan dan akan diperbarui berdasarkan berat.";

/* No comment provided by engineer. */
"The subscription terms and conditions are available on our website. You may access it here." = "Syarat dan ketentuan paket langganan bisa dilihat di website HappyFresh. Klik di sini.";

/* No comment provided by engineer. */
"The subscription will be on auto-renewal unless there is any issues on the payment you will receive notification from the Operation System provider (Google PlayStore and/or AppStore)" = "Paket langgananmu akan otomatis diperpanjang, kecuali terjadi kesalahan pada proses pembayaran.";

/* No comment provided by engineer. */
"There are no stores around you" = "Tidak ada toko di sekitar Anda";

/* No comment provided by engineer. */
"There are some price changes in your cart" = "Ada perubahan harga pada barang di trolimu";

/* No comment provided by engineer. */
"There is no user with that email address" = "Tidak ada pengguna dengan alamat email tersebut";

/* No comment provided by engineer. */
"This delivery location is outside of our service coverage." = "Lokasi pengantaran ini di luar jangkauan layanan kami.";

/* No comment provided by engineer. */
"This field can not be empty" = "Bagian ini tidak boleh kosong";

/* No comment provided by engineer. */
"This item has reached maximum quantity allowed" = "Barang ini mencapai batas kuantitas yang diperbolehkan";

/* No comment provided by engineer. */
"This item is currently not available in your selected store:" = "Barang ini tidak tersedia di toko yang Anda pilih saat ini:";

/* No comment provided by engineer. */
"This location is outside of our delivery area" = "Lokasi yang Anda pilih saat ini di luar area pengantaran kami";

/* No comment provided by engineer. */
"This message is related to a store that is not in your area.\nSincere apologies." = "Pesan ini berhubungan dengan toko yang tidak berada di area Anda.\nMohon maaf.";

/* No comment provided by engineer. */
"THIS MONTH" = "BULAN INI";

/* No comment provided by engineer. */
"This number is already registered. Please check and try again" = "Nomor ini telah terdaftar. Silahkan cek dan coba lagi nanti";

/* No comment provided by engineer. */
"This product is running low" = "Produk ini mulai habis";

/* No comment provided by engineer. */
"This promotion is only eligible in certain stores" = "This promotion is only eligible in certain stores";

/* No comment provided by engineer. */
"This store is based on your current location." = "Toko ini berdasarkan lokasimu saat ini.";

/* No comment provided by engineer. */
"This store is closed" = "Toko ini tutup";

/* No comment provided by engineer. */
"Time" = "Waktu";

/* No comment provided by engineer. */
"Time’s Up" = "Waktu Habis!";

/* No comment provided by engineer. */
"Time’s up! The Flash Sale is over" = "Waktu habis! Flash Sale telah berakhir";

/* No comment provided by engineer. */
"To contact our customer service regarding subscription, visit the Help Center at the bottom of this page" = "Hubungi Pusat Bantuan di bawah halaman ini apabila mengalami kendala terkait paket langganan kamu";

/* No comment provided by engineer. */
"To continue, your total needs to be more than <strong>%@</strong>." = "Untuk melanjutkan, total Anda harus lebih dari <strong>%@</strong>";

/* No comment provided by engineer. */
"To ensure speedy delivery, we will follow the replacement preferences from your previous order." = "Untuk memastikan pengantaran cepat, kami akan mengikuti preferensi penggantian dari pesanan Kamu sebelumnya.";

/* No comment provided by engineer. */
"To ensure speedy delivery, your Personal Shopper will call you to confirm any replacement" = "Untuk memastikan pengantaran cepat, Personal Shopper akan menghubungimu untuk mengonfirmasi penggantian";

/* No comment provided by engineer. */
"To ensure speedy delivery, your Personal Shopper will replace your items if they are out of stock." = "Untuk memastikan pengantaran cepat, Personal Shopper akan menggantikan item jika ada stok kosong.";

/* No comment provided by engineer. */
"To ensure speedy delivery, your Personal Shopper will send you a message to confirm any replacement." = "Untuk memastikan pengantaran cepat, Personal Shopper akan mengirimkan pesan untuk mengonfirmasi penggantian.";

/* No comment provided by engineer. */
"Today" = "Hari ini";

/* No comment provided by engineer. */
"TODAY" = "HARI INI";

/* No comment provided by engineer. */
"Tomorrow" = "Besok";

/* No comment provided by engineer. */
"Top savers this week" = "Promo hemat minggu ini";

/* No comment provided by engineer. */
"Total" = "Total";

/* No comment provided by engineer. */
"Totally not happy!" = "Tidak puas!";

/* No comment provided by engineer. */
"Track delivery" = "Lacak pengantaran";

/* No comment provided by engineer. */
"Track your order status live!" = "Cek status pemesanan secara langsung!";

/* No comment provided by engineer. */
"Try again" = "Coba lagi";

/* No comment provided by engineer. */
"Try Again" = "Coba Lagi";

/* No comment provided by engineer. */
"Try again in" = "Coba lagi di";

/* %@ will be replaced by second. */
"Try again in %@s" = "Coba lagi dalam %@";

/* No comment provided by engineer. */
"Try other stores near you" = "Coba toko lain di dekatmu";

/* No comment provided by engineer. */
"Turn on your location settings" = "Aktifkan layanan lokasi";

/* No comment provided by engineer. */
"Uh oh, it seems you passed the time limit or are bound by your credit card's T&C.\n\nStill worried about your order? Contact our Service Team who will happily assist you.\n\nFor more info on cancellations, you can Contact Us or check our FAQ." = "Uh oh, sepertinya Kamu melewati batas waktu atau terikat oleh T&C kartu kredit Kamu. \n\nMasih khawatir dengan pesanan Kamu? Hubungi Tim Layanan kami yang dengan senang hati akan membantumu. \n\nUntuk info lebih lanjut tentang pembatalan, Kamu dapat Hubungi Kami atau periksa FAQ kami.";

/* No comment provided by engineer. */
"Uh oh, it seems you passed the time limit. We've started processing your order by now.\n\nHave worries about your order? Contact our Service Team who will happily assist you, or check our FAQ." = "Uh oh, sepertinya Kamu sudah melewati batas waktu. Kami sudah mulai memproses pesananmu sekarang.  \n\nMasih khawatir dengan pesananmu? Hubungi tim customer service kami yang dengan senang hati akan membantumu, atau cek FAQ kami.";

/* No comment provided by engineer. */
"Uh oh, we may have started processing your order by now. Please contact us to reschedule your order manually." = "Uh oh, kami sudah mulai memproses pesananmu sekarang. Mohon hubungi kami untuk menjadwalkan ulang pesananmu secara manual.";

/* No comment provided by engineer. */
"Uh-oh, something went wrong" = "Uh-oh, terjadi kesalahan";

/* No comment provided by engineer. */
"Unable to proceed" = "Tidak dapat melanjutkan";

/* No comment provided by engineer. */
"Unavailable" = "Tidak tersedia";

/* No comment provided by engineer. */
"Unavailable delivery slots will become available for you." = "Slot pengantaran yang habis kami buka untukmu.";

/* No comment provided by engineer. */
"Unavailable on this store" = "Tidak tersedia di toko ini";

/* No comment provided by engineer. */
"Unfortunately, this promotion is not available at any stores around you." = "Sayangnya, promosi ini tidak tersedia di toko mana pun di sekitarmu.";

/* No comment provided by engineer. */
"Unknown status" = "Status tidak dikenal";

/* %@ will be replaced by expiry date of the promotion. */
"until %@" = "sampai %@";

/* No comment provided by engineer. */
"Upcoming VIP service" = "Fasilitas VIP selanjutnya";

/* No comment provided by engineer. */
"Update account" = "Update akun";

/* No comment provided by engineer. */
"Update Cart" = "Perbarui Troli";

/* No comment provided by engineer. */
"Update order" = "Perbarui Pesanan";

/* No comment provided by engineer. */
"Update your cart to use these vouchers" = "Perbarui trolimu untuk pakai voucher ini";

/* No comment provided by engineer. */
"Updates" = "Informasi";

/* No comment provided by engineer. */
"Use the benefits right away!" = "Gunakan paketmu sekarang juga!";

/* No comment provided by engineer. */
"Use express for 1-2 hour delivery" = "Gunakan ekspress untuk pengiriman dalam 1-2 jam";

/* No comment provided by engineer. */
"Use your phone number or email" = "Gunakan nomor telepon atau email kamu";

/* No comment provided by engineer. */
"Valid for delivery: " = "Berlaku untuk pengantaran:";

/* No comment provided by engineer. */
"Various supermarket options" = "Pilihan supermarket beragam";

/* No comment provided by engineer. */
"Vegan" = "Vegan";

/* No comment provided by engineer. */
"Verification" = "Verifikasi";

/* No comment provided by engineer. */
"Verification failed. Please try again." = "Verifikasi gagal. Coba lagi.";

/* No comment provided by engineer. */
"Verification link has been sent to %@\n\n Please verify that this email is yours before it is set as your primary email." = "Link verifikasi telah dikirim ke %@\n\n. Lakukan verifikasi untuk memastikan bahwa ini email milikmu sebelum disimpan sebagai email utama.";

/* No comment provided by engineer. */
"Verify now" = "Verifikasi sekarang";

/* No comment provided by engineer. */
"Verify Number" = "Verifikasi Nomor";

/* No comment provided by engineer. */
"Verify phone number" = "Verifikasi nomor telepon";

/* No comment provided by engineer. */
"Verify Phone Number" = "Verifikasi Nomor Telepon";

/* No comment provided by engineer. */
"Verify your email" = "Verifikasi email kamu";

/* No comment provided by engineer. */
"Verify your ID card" = "Verifikasi KTP Anda";

/* No comment provided by engineer. */
"Verify your ID card to purchase products for 18+" = "Verifikasi KTP Anda untuk membeli produk-produk 18+";

/* Version */
"Version" = "Versi";

/* No comment provided by engineer. */
"Very Happy!" = "Sangat Puas!";

/* No comment provided by engineer. */
"View cart" = "Lihat troli";

/* No comment provided by engineer. */
"View list" = "Lihat daftar";

/* No comment provided by engineer. */
"VIP Member" = "Member VIP";

/* No comment provided by engineer. */
"Visit store" = "Kunjungi toko";

/* No comment provided by engineer. */
"Voucher got removed" = "Voucher dihapus";

/* No comment provided by engineer. */
"Voucher selected" = "Voucher terpilih";

/* No comment provided by engineer. */
"Voucher selected ✔︎" = "Voucher terpilih ✔︎";

/* No comment provided by engineer. */
"Voucher will be removed" = "Voucher akan dihapus";

/* No comment provided by engineer. */
"Vouchers & Bank Promos" = "Voucher dan Promo Bank";

/* No comment provided by engineer. */
"Vouchers selected" = "Voucher terpilih";

/* No comment provided by engineer. */
"Waiting for delivery" = "Menunggu pengantaran";

/* No comment provided by engineer. */
"Waiting for Payment" = "Menunggu Pembayaran";

/* No comment provided by engineer. */
"Want to copy them here?" = "Ingin menyalinnya di sini?";

/* No comment provided by engineer. */
"Want to cook at home?" = "Ingin coba di rumah?";

/* No comment provided by engineer. */
"We are marking down these items while they are fresh. Now you can enjoy huge discounts on our inventory surplus. Don’t forget to consume the items within the suggested number of days!" = "Kami menandai produk-produk ini selagi masih segar. Kini kamu bisa menikmati diskon besar-besaran di gudang persediaan kami. Jangan lupa untuk mengonsumsi item dalam kurun waktu yang disarankan!";

/* No comment provided by engineer. */
"We are working on fixing this, please refresh the page." = "Kami sedang memperbaikinya, silahkan refresh laman ini.";

/* No comment provided by engineer. */
"We cannot carry out this request because your phone is not connected to the internet. Please connect to wifi or turn on your mobile data." = "Kami tidak dapat membawa keluar permohonan ini karena telepon Anda tidak terhubung ke internet. Silakan terhubung ke wifi atau aktifkan mobile data.";

/* No comment provided by engineer. */
"We cannot find such store nearby your delivery address." = "We cannot find such store nearby your delivery address.";

/* No comment provided by engineer. */
"We cannot reach you, no matter\nhow hard we try..." = "Kami tidak dapat menjangkau Anda, bagaimanapun caranya...";

/* No comment provided by engineer. */
"We found out you are already have subscription let's identify your benefits" = "Kamu sudah berlangganan. Lihat paket langgananmu sekarang.";

/* No comment provided by engineer. */
"We have sent email with instructions for resetting your password! Please check your email." = "Kami telah mengirim email dengan instruksi untuk mengatur ulang sandi Anda! Silakan cek email Anda.";

/* No comment provided by engineer. */
"We need to ensure if your address is within our coverage area" = "Kami harus memastikan apabila alamat Anda dapat dijangkau.";

/* No comment provided by engineer. */
"We promise to deliver your items in best quality. " = "Kami menjanjikan pengantaran barang berkualitas kepada Anda.";

/* No comment provided by engineer. */
"We recommend that you immediately buy this product before out of stock." = "Kami sarankan agar kamu segera membeli produk ini sebelum kehabisan.";

/* No comment provided by engineer. */
"We sent you an sms to %@" = "Kami mengirimkan sms ke %@";

/* No comment provided by engineer. */
"We will automatically add your promo code, but promo quota and time limit may be affected." = "Secara otomatis kami akan menambahkan kode promomu, namun kuota promo dan batas waktu akan terpengaruh.";

/* No comment provided by engineer. */
"We will do better next time" = "Kami akan berusaha lebih baik lagi";

/* No comment provided by engineer. */
"We will not replace the rest if turned off." = "Kami tidak akan mengganti sisanya jika dimatikan.";

/* No comment provided by engineer. */
"We will restock them for you as soon as we can." = "Kami akan stok ulang secepatnya untukmu.";

/* No comment provided by engineer. */
"We will send you an email to set your password." = "Kami akan kirimkan email untuk mengatur ulang kata sandi.";

/* No comment provided by engineer. */
"We will start immediately after payment is completed. You will receive your order before %@." = "Kami akan segera berbelanja setelah pembayaran selesai. Kamu akan menerima pesananmu sebelum %@.";

/* No comment provided by engineer. */
"We'll be available there soon. Please select another location for now." = "Kami akan segera tersedia di sana. Silahkan pilih lokasi lain saat ini.";

/* No comment provided by engineer. */
"We'll have more products uploaded in the upcoming weeks. Don't worry, you can still continue shopping!" = "Kami akan terus menambahkan produk lain untuk beberapa minggu kedepan. Tenang, kamu masih bisa belanja kok";

/* No comment provided by engineer. */
"We'll keep you notified!" = "Kami akan terus mengabarimu!";

/* No comment provided by engineer. */
"We'll replace out-of-stock items with similar ones." = "Kami akan mengganti item yang stoknya habis dengan yang serupa.";

/* No comment provided by engineer. */
"We'll send promo updates soon!" = "Kami akan mengirimkan promo terbaru! ";

/* No comment provided by engineer. */
"We’re having troubles processing your payment. Would you like to try again?" = "Pembayaranmu tidak dapat diproses. Ayo coba lagi.";

/* No comment provided by engineer. */
"We’re here to help if you run into any trouble logging in or signing up." = "Kami disini untuk membantu apabila kamu memiliki kendala saat masuk/daftar akun.";

/* No comment provided by engineer. */
"We’re sorry to hear you go" = "Kami menyesal Anda pergi";

/* No comment provided by engineer. */
"We've processed your order by now. Still urgent? We allow 1 emergency cancel a month to respect our fleet who prepare orders in advance." = "Kami telah memproses pesananmu sekarang. Masih ada urusan mendesak? Kami mengizinkan 1 kali pembatalan darurat setiap bulan untuk menghormati tim lapangan kami yang sudah menyiapkan pesanan lebih awal.";

/* No comment provided by engineer. */
"We’ve received your request" = "Kami menerima permintaanmu";

/* No comment provided by engineer. */
"We've started shopping for you" = "Kami sudah mulai berbelanja untukmu";

/* No comment provided by engineer. */
"Weight adjustment" = "Penyesuaian berat";

/* No comment provided by engineer. */
"Welcome %@!" = "Welcome %@!";

/* No comment provided by engineer. */
"Welcome back!" = "Selamat datang kembali!";

/* No comment provided by engineer. */
"Welcome to HappyFresh Subscription and enjoy your exclusive benefits right away!" = "Selamat datang di Paket Langganan HappyFresh. Nikmati paket eksklusifmu sekarang!";

/* No comment provided by engineer. */
"Welcome!" = "Selamat datang!";

/* No comment provided by engineer. */
"What do they say?" = "Apa yang mereka katakan?";

/* No comment provided by engineer. */
"What happens if my subscription about to expire or has expired?" = "Bagaimana jika paket langganan saya akan atau telah berakhir?";

/* No comment provided by engineer. */
"What is Clearance?" = "Apa itu cuci gudang?";

/* No comment provided by engineer. */
"What is the subscription terms and conditions?" = "Apa saja syarat dan ketentuan langganan HappyFresh?";

/* No comment provided by engineer. */
"What you’ll get:" = "Yang akan kamu dapat:";

/* No comment provided by engineer. */
"What's New" = "Yang Terbaru";

/* No comment provided by engineer. */
"What’s cooking today?" = "Masak apa hari ini?";

/* No comment provided by engineer. */
"Where to?" = "Antar ke mana?";

/* No comment provided by engineer. */
"Will be applied automatically on checkout" = "Akan diterapkan secara otomatis saat checkout";

/* No comment provided by engineer. */
"Will be updated after weighing." = "Akan diperbarui setelah ditimbang.";

/* No comment provided by engineer. */
"Would you like to reactivate it? Your benefits will be restored immediately." = "Apakah kamu ingin mengaktifkan paket langganan lagi? Benefit langganan bisa langsung dipakai.";

/* No comment provided by engineer. */
"Wrong Product" = "Produk Salah";

/* No comment provided by engineer. */
"Yay, your order will get <b>FREE DELIVERY</b>!" = "Yay, pesananmu akan dapat <b>GRATIS ONGKIR</b>!";

/* No comment provided by engineer. */
"Yay! Express delivery slot found!" = "Yay! Slot pengantaran instan ditemukan!";

/* Text between <green> and </green> will be green. %@ will be replaced by the app with the point amount. */
"Yay! You've got <green>%@ points</green>" = "Yay! Kamu dapat <green>%@ poin</green>";

/* No comment provided by engineer. */
"Yay! Your free item(s) is in your cart!" = "Yay! Item gratis Anda sudah di troli!";

/* No comment provided by engineer. */
"year" = "tahun";

/* No comment provided by engineer. */
"Yes" = "Ya";

/* No comment provided by engineer. */
"Yes Please!" = "Ya!";

/* No comment provided by engineer. */
"Yes, cancel" = "Ya, batalkan";

/* No comment provided by engineer. */
"Yes, copy products" = "Ya, salin produk";

/* No comment provided by engineer. */
"Yes, delete" = "Ya, hapus";

/* No comment provided by engineer. */
"Yes, deliver here" = "Ya, kirim ke sini";

/* No comment provided by engineer. */
"Yes, I understand" = "Ya, Saya mengerti";

/* No comment provided by engineer. */
"Yes, remove" = "Ya, hapus";

/* No comment provided by engineer. */
"Yes! It is possible to change your payment method for the next billing cycle through Appstore or Playstore" = "Betul! Kamu bisa mengganti metode pembayaran di AppStore dan Playstore untuk membayar tagihan selanjutnya.";

/* No comment provided by engineer. */
"Yes! That is correct." = "Betul sekali!";

/* No comment provided by engineer. */
"Yesterday" = "Kemarin";

/* No comment provided by engineer. */
"YESTERDAY" = "KEMARIN";

/* No comment provided by engineer. */
"You are already subscribed!" = "Kamu sudah berlangganan!";

/* No comment provided by engineer. */
"You can add and remove items in the current order until:" = "Anda dapat menambahkah dan mengurangi barang pada pesanan ini sampai:";

/* No comment provided by engineer. */
"You can add your complete address later during checkout" = "Kamu bisa menambahkan alamat lengkapmu saat checkout";

/* No comment provided by engineer. */
"You can add/remove items before:" = "Kamu bisa tambah/hapus produk sebelum:";

/* No comment provided by engineer. */
"You can change this by placing your first order!" = "Anda dapat mengganti ini dengan melakukan pemesanan pertama!";

/* No comment provided by engineer. */
"You can explore stores in these popular areas:" = "Kamu dapat mencari toko di area populer berikut ini:";

/* No comment provided by engineer. */
"You can find all items you’ve saved in HappyFresh Supermarket here." = "Kamu bisa menemukan semua item yang disimpan di HappyFresh Supermarket di sini.";

/* No comment provided by engineer. */
"You can redeem a maximum of 10,000 points per transaction.\n\nNote: Available points shown may be inaccurate due to delays in point updates. For more information, visit the FAQ or Help Center." = "Kamu dapat menukarkan maksimal 10.000 poin per transaksi. \n\nPerlu diingat, poin tersedia yang ditampilkan mungkin tidak akurat karena penundaan pembaruan poin. Untuk informasi lebih lanjut, kunjungi FAQ atau Pusat Bantuan.";

/* No comment provided by engineer. */
"You can select contact method at checkout" = "Kamu bisa pilih metode kontak saat checkout";

/* No comment provided by engineer. */
"You can select your own back-up product" = "Kamu bisa memilih produk cadangan sendiri";

/* No comment provided by engineer. */
"You can try another promotions." = "Kamu bisa coba promo lainnya.";

/* No comment provided by engineer. */
"You can try to start shopping" = "Kamu bisa coba untuk mulai belanja";

/* No comment provided by engineer. */
"You currently have no order history" = "Saat ini kamu belum memiliki riwayat pesanan";

/* No comment provided by engineer. */
"You don't have any items in your basket. Let's go shopping" = "Tidak ada produk di trolimu. Yuk, belanja";

/* No comment provided by engineer. */
"You have an active subscription on a different HappyFresh account" = "Kamu punya paket langganan di akun lain";

/* No comment provided by engineer. */
"You have no available voucher" = "Kamu tidak memiliki voucher yang tersedia";

/* No comment provided by engineer. */
"You have no past items" = "Anda tidak memiliki barang sebelumnya";

/* No comment provided by engineer. */
"You have no past orders" = "Anda tidak memiliki pesanan sebelumnya";

/* No comment provided by engineer. */
"You have successfully subscribed!" = "Paket langganan kamu berhasil diaktifkan!";

/* No comment provided by engineer. */
"You have VIP access to delivery slots" = "Kamu punya akses VIP untuk slot pengantaran";

/* No comment provided by engineer. */
"You may insert one voucher code per purchase and can stack any vouchers with the free delivery and point multipliers." = "Kamu bisa menggunakan 1 kode voucher untuk setiap transaksi dan dapat digabung dengan voucher lain seperti gratis ongkir dan poin akumulatif.";

/* No comment provided by engineer. */
"You may like" = "Kamu Mungkin Suka";

/* No comment provided by engineer. */
"You may lose promotions with a usage limit, time limit or minimum amount required." = "Anda mungkin kehilangan promosi berbatas waktu/penggunaan, atau minimal belanja.";

/* No comment provided by engineer. */
"You may receive a call from your Personal Shopper if any questions arise." = "Kamu mungkin akan menerima panggilan dari Personal Shopper jika ada pertanyaan.";

/* No comment provided by engineer. */
"You must be at least 21 years old to purchase tobacco and alcohol products. Please hold your ID ready for verification." = "Kamu harus berusia setidaknya 21 tahun untuk membeli produk rokok dan alkohol. Siapkan kartu identitas (KTP) untuk verifikasi.";

/* No comment provided by engineer. */
"You need to be logged in to check all the orders you placed on HappyFresh." = "Anda harus masuk untuk mengecek semua pesanan yang pernah Anda buat di HappyFresh.";

/* No comment provided by engineer. */
"You need to be logged in to update your account details." = "Anda harus masuk untuk perbarui detail akun Anda.";

/* No comment provided by engineer. */
"You still have an active subscription. Check your subscription page to use your benefits." = "Paket langganan kamu masih aktif. Cek halaman 'Langganan Saya' untuk menggunakan paketmu";

/* No comment provided by engineer. */
"You still have an ongoing order. Try again later after your order is completed." = "Kamu masih memiliki pesanan aktif. Coba lagi saat pesananmu sudah selesai, ya.";

/* No comment provided by engineer. */
"You will clear all the items in the cart. Remember, this action cannot be undone." = "Produk akan dihapus dari trolimu dan tidak bisa dibatalkan.";

/* No comment provided by engineer. */
"You will earn points from every order placed on HappyFresh.\n\n Additionally. you can earn points by participating in our Point Booster campaigns.\n Keep a look out for these bonus points on your Membership Page and in your email." = "Kamu akan mendapatkan poin dari setiap pesanan yang dilakukan di HappyFresh.\n\nSelain itu, Kamu bisa mendapatkan poin dengan berpartisipasi dalam campaignPoint Booster kami. Nantikan bonus poin ini di Halaman Keanggotaan dan di email-mu.";

/* %@ will be replaced by the minuite. */
"You will get your order within %@ minutes of completed payment" = "Kamu akan mendapatkan pesananmu dalam %@ menit setelah pembayaran selesai";

/* No comment provided by engineer. */
"You will receive notifications for HappyFresh Super Plan." = "Kamu akan menerima notifikasi untuk HappyFresh Super Plan.";

/* No comment provided by engineer. */
"You’ll be notified" = "Kamu akan diberitahu";

/* No comment provided by engineer. */
"You'll need to standby & pick replacements yourself." = "Kamu harus siaga & memilih penggantinya sendiri.";

/* No comment provided by engineer. */
"You're already logged in as %@." = "You're already logged in as %@.";

/* No comment provided by engineer. */
"You're one of the first to order from HappyFresh Supermarket. How was your shopping experience?" = "Kamu salah satu orang pertama yang memesan dari HappyFresh Supermarket. Bagaimana pengalaman belanjamu?";

/* Text in between <b> and </b> will be bold. %@ will be replaced by the app with the point amount. */
"You've successfully redeemed <b>%@ points</b>!" = "Kamu berhasil menukarkan <b>%@ poin</b>!";

/* No comment provided by engineer. */
"Your account" = "Akun kamu";

/* No comment provided by engineer. */
"Your account has been deleted successfully" = "Akun Anda telah berhasil dihapus";

/* No comment provided by engineer. */
"Your area requires you to use sustainable bag" = "Area kamu mewajibkan untuk menggunakan tas sustainable";

/* No comment provided by engineer. */
"Your authentication link is not valid" = "Your authentication link is not valid";

/* No comment provided by engineer. */
"Your benefits have also been reactivated. Enjoy them!" = "Paket langganan kamu berhasil diperpanjang. Selamat berbelanja!";

/* No comment provided by engineer. */
"Your cart is empty!" = "Troli Kamu kosong";

/* No comment provided by engineer. */
"Your feedback is noted" = "Tanggapanmu dicatat";

/* No comment provided by engineer. */
"Your free item(s) have been removed from cart." = "Barang gratis Anda telah dihilangkan dari troli.";

/* No comment provided by engineer. */
"Your Frequent Searches" = "Pencarian Favorit Anda";

/* No comment provided by engineer. */
"Your internet connection seems to be offline" = "Koneksi internet Anda sepertinya terputus";

/* No comment provided by engineer. */
"Your name / recipient" = "Nama kamu / penerima";

/* No comment provided by engineer. */
"Your number is successfully\nverified" = "Nomor telepon Anda telah sukses diverifikasi";

/* No comment provided by engineer. */
"Your order has been canceled" = "Pesanan Anda telah dibatalkan";

/* No comment provided by engineer. */
"Your order has been cancelled" = "Pesanan Anda Telah Dibatalkan";

/* No comment provided by engineer. */
"Your order has been cancelled. Your original order has been added to a new cart, ready for amendments & checkout!" = "Pesanan Anda telah dibatalkan. Seluruh barang dari pesanan sebelumnya telah ditambahkan ke troli, siap untuk perubahan & checkout!";

/* No comment provided by engineer. */
"Your order has been received" = "Pesananmu sudah diterima.";

/* No comment provided by engineer. */
"Your order has been received at %@" = "Pesanan Anda telah diterima pukul %@";

/* No comment provided by engineer. */
"Your order has been received by %@" = "Pesanan Anda telah diterima oleh %@";

/* No comment provided by engineer. */
"Your order has been received by %@ at %@" = "Pesanan Anda telah diterima oleh %1$@ pukul %2$@";

/* No comment provided by engineer. */
"Your order has been received!" = "Pesanan Anda telah diterima!";

/* No comment provided by engineer. */
"Your order history" = "Riwayat pesananmu";

/* No comment provided by engineer. */
"Your order is packed and on the way" = "Pesanan Anda dikemas dan dalam perjalanan";

/* No comment provided by engineer. */
"Your overall rating has been submitted!" = "Penilaian keseluruhanmu telah dikumpulkan!";

/* No comment provided by engineer. */
"Your password" = "Kata Sandimu";

/* No comment provided by engineer. */
"Your payment is unsuccessful" = "Pembayaranmu tidak berhasil";

/* No comment provided by engineer. */
"Your personal shopper" = "Personal Shopper kamu";

/* No comment provided by engineer. */
"Your personal shopper & rider" = "Personal shopper & rider kamu";

/* No comment provided by engineer. */
"Your personal shopper has started selecting your items" = "Pembelanja pribadi Anda sedang memilih barang-barang pesanan Anda";

/* No comment provided by engineer. */
"Your Personal Shopper has started shopping for you." = "Personal Shopper sudah mulai membelanjakan pesananmu.";

/* No comment provided by engineer. */
"Your phone number has not been verified. To keep your account secure, please enter your phone number." = "Nomor telepon Anda belum terverifikasi. Untuk menjaga keamanan akun, silakan masukkan nomor telepon Anda.";

/* No comment provided by engineer. */
"Your profile successfully changed" = "Profilmu telah berhasil diubah";

/* No comment provided by engineer. */
"Your recent searches" = "Pencarian terakhir Anda";

/* No comment provided by engineer. */
"Your refund balance:" = "Saldo refund kamu:";

/* No comment provided by engineer. */
"Your replacement preference has been removed!" = "Preferensi penggantianmu telah dihapus!";

/* No comment provided by engineer. */
"Your replacement preference has been saved!" = "Preferensi penggantianmu telah disimpan!";

/* No comment provided by engineer. */
"Your Shopper & Rider" = "Shopper & Rider Kamu";

/* No comment provided by engineer. */
"Your shopper and driver rating has been submitted!" = "Penilaian shopper dan driver telah dikumpulkan!";

/* No comment provided by engineer. */
"Your subscription has been restored!" = "Paket langganan kamu sudah aktif lagi!";

/* No comment provided by engineer. */
"Your subscription has expired" = "Paket langganan kamu sudah kedaluwarsa";

/* No comment provided by engineer. */
"Your subscription will be automatically applied when you shop on HappyFresh App. Certain vouchers that come with the subscription will be available in the My Vouchers section." = "Paket langganan akan otomatis terpakai saat kamu belanja di aplikasi HappyFresh. Voucher lainnya yang bisa kamu gunakan secara bersamaan bisa dilihat di halaman 'Voucher Saya'.";

/* No comment provided by engineer. */
"Your VIP benefits" = "Fasilitas VIP kamu";

/* No comment provided by engineer. */
"My Voucher" = "Voucher Saya";

/* No comment provided by engineer. */
"<EMAIL>" = "<EMAIL>";

/* No comment provided by engineer. */
"Z-A" = "Z-A";

