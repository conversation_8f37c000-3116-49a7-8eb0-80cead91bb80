//
//  Recipe+Logic.m
//  HappyFresh
//
//  Created by <PERSON> on 11/7/17.
//  Copyright © 2017 HappyFresh Inc. All rights reserved.
//

#import <MagicalRecord/MagicalRecord.h>

#import "Recipe+Logic.h"
#import "NSDictionary+Additions.h"

@implementation Recipe (Logic)

+ (NSArray *)arrayFromJSONArray:(NSArray *)array stockLocation:(StockLocation *)stockLocation {
    NSMutableArray *recipes = [[NSMutableArray alloc] initWithCapacity:array.count];
    for (NSDictionary *dictionary in array) {
        Recipe *recipe = [Recipe recipeFromDictionary:dictionary stockLocation:stockLocation];
        [recipes addObject:recipe];
    }
    return recipes;
}

+ (Recipe *)recipeFromDictionary:(NSDictionary *)dictionary stockLocation:(StockLocation *)stockLocation {
    dictionary = [dictionary removeNSNullValues];
    
    NSNumber *recipeID = dictionary[@"id"];
    Recipe *recipe = [Recipe MR_findFirstByAttribute:@"recipeID" withValue:recipeID];
    if (recipe == nil) {
        recipe = [Recipe MR_createEntity];
    }
    
    recipe.recipeID = recipeID;
    recipe.name = dictionary[@"name"];
    recipe.displayTimeToCook = dictionary[@"display_time_to_cook"];
    recipe.displayServingPortion = dictionary[@"display_serving_portion"];
    recipe.displayDifficulty = dictionary[@"display_difficulty"];
    recipe.displayInstructions = dictionary[@"display_instruction"];
    recipe.link = dictionary[@"video_url"];
    
    NSDictionary *photo = dictionary[@"photo"];
    if (photo) {
        recipe.photoDefaultURL = photo[@"url"];
        recipe.photoMediumURL = photo[@"medium_url"];
        recipe.photoThumbURL = photo[@"thumb_url"];
    }

    return recipe;
}

@end
