//
//  VideoReel.swift
//  HappyFresh
//
//  Created by Augment Agent on 2025-07-07.
//

import Foundation
import UIKit

struct VideoReel {
    let id: String
    let title: String
    let description: String
    let videoURL: URL
    let thumbnailURL: URL?
    let duration: TimeInterval
    let author: String
    let ingredients: [String]
    let cookingTime: String
    let difficulty: RecipeDifficulty
    let category: RecipeCategory
    let likes: Int
    let views: Int
    
    enum RecipeDifficulty: String, CaseIterable {
        case easy = "Easy"
        case medium = "Medium"
        case hard = "Hard"
        
        var color: UIColor {
            switch self {
            case .easy:
                return UIColor.systemGreen
            case .medium:
                return UIColor.systemOrange
            case .hard:
                return UIColor.systemRed
            }
        }
    }
    
    enum RecipeCategory: String, CaseIterable {
        case breakfast = "Breakfast"
        case lunch = "Lunch"
        case dinner = "Dinner"
        case dessert = "Dessert"
        case snack = "Snack"
        case beverage = "Beverage"
        
        var icon: String {
            switch self {
            case .breakfast:
                return "☀️"
            case .lunch:
                return "🍽️"
            case .dinner:
                return "🌙"
            case .dessert:
                return "🍰"
            case .snack:
                return "🥨"
            case .beverage:
                return "🥤"
            }
        }
    }
}

// MARK: - Sample Data
extension VideoReel {
    static let sampleReels: [VideoReel] = [
        VideoReel(
            id: "1",
            title: "Ebifurai Saus Keju",
            description: "Crispy fried shrimp with creamy cheese sauce - perfect for lunch!",
            videoURL: URL(string: "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4")!,
            thumbnailURL: URL(string: "https://via.placeholder.com/300x400/FF6B6B/FFFFFF?text=Ebifurai"),
            duration: 596,
            author: "Chef Maria",
            ingredients: ["Shrimp", "Cheese", "Flour", "Eggs", "Breadcrumbs"],
            cookingTime: "30 mins",
            difficulty: .medium,
            category: .lunch,
            likes: 1250,
            views: 15600
        ),
        VideoReel(
            id: "2",
            title: "Cheese Beef Paratha",
            description: "Flaky paratha stuffed with seasoned beef and melted cheese",
            videoURL: URL(string: "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4")!,
            thumbnailURL: URL(string: "https://via.placeholder.com/300x400/4ECDC4/FFFFFF?text=Paratha"),
            duration: 653,
            author: "Chef Ahmad",
            ingredients: ["Beef", "Cheese", "Flour", "Onions", "Spices"],
            cookingTime: "45 mins",
            difficulty: .medium,
            category: .dinner,
            likes: 2100,
            views: 28400
        ),
        VideoReel(
            id: "3",
            title: "Chocolate Lava Cake",
            description: "Decadent chocolate cake with molten center - dessert perfection!",
            videoURL: URL(string: "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4")!,
            thumbnailURL: URL(string: "https://via.placeholder.com/300x400/8E44AD/FFFFFF?text=Lava+Cake"),
            duration: 60,
            author: "Chef Sophie",
            ingredients: ["Dark Chocolate", "Butter", "Eggs", "Sugar", "Flour"],
            cookingTime: "25 mins",
            difficulty: .easy,
            category: .dessert,
            likes: 3200,
            views: 45600
        ),
        VideoReel(
            id: "4",
            title: "Avocado Toast Supreme",
            description: "Healthy and delicious avocado toast with perfect toppings",
            videoURL: URL(string: "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerEscapes.mp4")!,
            thumbnailURL: URL(string: "https://via.placeholder.com/300x400/27AE60/FFFFFF?text=Avocado+Toast"),
            duration: 15,
            author: "Chef Emma",
            ingredients: ["Avocado", "Sourdough Bread", "Tomatoes", "Feta", "Olive Oil"],
            cookingTime: "10 mins",
            difficulty: .easy,
            category: .breakfast,
            likes: 890,
            views: 12300
        ),
        VideoReel(
            id: "5",
            title: "Spicy Ramen Bowl",
            description: "Authentic spicy ramen with rich broth and fresh toppings",
            videoURL: URL(string: "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerFun.mp4")!,
            thumbnailURL: URL(string: "https://via.placeholder.com/300x400/E74C3C/FFFFFF?text=Spicy+Ramen"),
            duration: 60,
            author: "Chef Takeshi",
            ingredients: ["Ramen Noodles", "Pork Broth", "Eggs", "Green Onions", "Chili Oil"],
            cookingTime: "40 mins",
            difficulty: .hard,
            category: .dinner,
            likes: 1800,
            views: 22100
        ),
        VideoReel(
            id: "6",
            title: "Mango Smoothie Bowl",
            description: "Refreshing tropical smoothie bowl perfect for hot days",
            videoURL: URL(string: "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerJoyrides.mp4")!,
            thumbnailURL: URL(string: "https://via.placeholder.com/300x400/F39C12/FFFFFF?text=Mango+Bowl"),
            duration: 15,
            author: "Chef Luna",
            ingredients: ["Mango", "Banana", "Coconut Milk", "Granola", "Berries"],
            cookingTime: "5 mins",
            difficulty: .easy,
            category: .beverage,
            likes: 1450,
            views: 18700
        ),
        VideoReel(
            id: "7",
            title: "Korean BBQ Tacos",
            description: "Fusion cuisine at its best - Korean flavors in Mexican tacos",
            videoURL: URL(string: "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerMeltdowns.mp4")!,
            thumbnailURL: URL(string: "https://via.placeholder.com/300x400/9B59B6/FFFFFF?text=BBQ+Tacos"),
            duration: 60,
            author: "Chef Diego",
            ingredients: ["Beef Bulgogi", "Corn Tortillas", "Kimchi", "Cilantro", "Lime"],
            cookingTime: "35 mins",
            difficulty: .medium,
            category: .dinner,
            likes: 2750,
            views: 31200
        ),
        VideoReel(
            id: "8",
            title: "Classic Pancakes",
            description: "Fluffy, golden pancakes that melt in your mouth",
            videoURL: URL(string: "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/Sintel.mp4")!,
            thumbnailURL: URL(string: "https://via.placeholder.com/300x400/F1C40F/FFFFFF?text=Pancakes"),
            duration: 888,
            author: "Chef Betty",
            ingredients: ["Flour", "Milk", "Eggs", "Butter", "Maple Syrup"],
            cookingTime: "20 mins",
            difficulty: .easy,
            category: .breakfast,
            likes: 1650,
            views: 20800
        )
    ]
}
