//
//  User+Logic.m
//  HappyFresh
//
//  Created by <PERSON><PERSON> on 30/04/24.
//  Copyright © 2024 HappyFresh Inc. All rights reserved.
//

#import <MagicalRecord/MagicalRecord.h>

#import "User+Logic.h"
#import "NSDictionary+Additions.h"
#import "NSString+Additions.h"
#import "HFUserDefault.h"
#import "Tracker.h"
#import "HappyFresh-Swift.h"

@implementation User (Logic)

+ (User *)userFromDictionary:(NSDictionary *)dictionary {
    dictionary = [dictionary removeNSNullValues];

    // NOTE: [auth]
    User *user = nil;
    if ([dictionary[@"uuid"] length] > 0) {
        user = [User MR_findFirstByAttribute:@"uuid" withValue:dictionary[@"uuid"]];
    }
    if (user == nil) {
        user = [User MR_findFirstByAttribute:@"userID" withValue:dictionary[@"id"]];
    }
    if (user == nil) {
        user = [User MR_createEntity];
    }

    user.uuid = dictionary[@"uuid"];
    user.userID = dictionary[@"id"];
    user.firstName = dictionary[@"first_name"];
    user.lastName = dictionary[@"last_name"];
    user.email = dictionary[@"email"];
    user.token = dictionary[@"token"];
    user.code = dictionary[@"code"];
    user.phone = dictionary[@"phone"];
    user.verified = dictionary[@"verified"];
    user.emailVerified = dictionary[@"email_verified"];
    user.isCorporate = dictionary[@"is_corporate"];
    user.gender = dictionary[@"gender"];
    user.minAge = dictionary[@"min_age"];
    user.maxAge = dictionary[@"max_age"];
    user.createdAt = [dictionary[@"created_at"] dateFromISO8601];
    user.updatedAt = [dictionary[@"updated_at"] dateFromISO8601];
    user.inviterID = dictionary[@"inviter_id"];
    user.language = dictionary[@"language"];
    user.enableUpdatePhone = dictionary[@"enable_update_phone"];
    user.isSubscriber = [dictionary[@"is_subscriber"] boolValue];
    user.isThaiUserConsentAccepted = [dictionary[@"th_privacy_consent_accepted"] boolValue];
    user.segment = dictionary[@"segment"];

    // NOTE: [auth]
    // If unknown, fallback to legacy/spree user id and inviter id for stable Segment tracking identifier.
    if ([user.trackingID length] == 0 && dictionary[@"id"]) {
        user.trackingID = [dictionary[@"id"] stringValue];
    }
    if ([user.inviterTrackingID length] == 0 && dictionary[@"inviter_id"]) {
        user.inviterTrackingID = [dictionary[@"inviter_id"] stringValue];
    }
    if (dictionary[@"bill_address"]) {
        Address *billAddress = [Address MR_importFromObject:dictionary[@"bill_address"]];
        billAddress.user = user;
    }
    if (dictionary[@"ship_address"]) {
        Address *shipAddress = [Address MR_importFromObject:dictionary[@"ship_address"]];
        shipAddress.user = user;
    }
    if (dictionary[@"profile_picture_url"]) {
        user.profilePictureURL = dictionary[@"profile_picture_url"];
    }
    if (dictionary[@"is_shopper"]) {
        user.isShopper = dictionary[@"is_shopper"];
    }
    if (dictionary[@"is_driver"]) {
        user.isDriver = dictionary[@"is_driver"];
    }
    if (dictionary[@"store_info_flyout_seen"]) {
        user.storeInfoFlyoutSeen = [dictionary[@"store_info_flyout_seen"] boolValue];
    }

    if ([dictionary[@"hfs_post_purchase_survey"] isKindOfClass:[NSDictionary class]]) {
        if (dictionary[@"hfs_post_purchase_survey"][@"first_view_date"]) {
            user.hfsPostPurchaseSurveyViewDate = [NSDate dateWithBackendString:dictionary[@"hfs_post_purchase_survey"][@"first_view_date"] dateFormat:@"yyyy-MM-dd"];
        }
        if (dictionary[@"hfs_post_purchase_survey"][@"version"]) {
            user.hfsPostPurchaseSurveyVersion = dictionary[@"hfs_post_purchase_survey"][@"version"];
        }
    }

    if (dictionary[@"choose_hfs_plus"]) {
        user.chooseHfsPlus = dictionary[@"choose_hfs_plus"];
        [HFUserDefault sharedInstance].chosenHfsPlus = [dictionary[@"choose_hfs_plus"] boolValue];
    } else if ([HFUserDefault sharedInstance].haveChosenHfsType) {
        [[Tracker sharedInstance] trackViewHfsPlusFlyoutWithHfsPlusSelected:[HFUserDefault sharedInstance].chosenHfsPlus isSavePreference:YES viaPopup:NO];
    }

    user.ordersReceivedCount = dictionary[@"orders_received_count"];

    if (dictionary[@"waitlist_offer_seen"]) {
        user.waitlistOfferSeen = dictionary[@"waitlist_offer_seen"];
    }

    if (dictionary[@"flash_sale_info_flyout_seen"]) {
        user.flashSaleInfoFlyoutSeen = [dictionary[@"flash_sale_info_flyout_seen"] boolValue];
    }

    if (dictionary[@"internal_uuid"]) {
        user.internalUuid = dictionary[@"internal_uuid"];
    }

    if (dictionary[@"my_super_indo_user"]) {
        NSLog(@"jilytest123 my_super_indo_user %@", dictionary[@"my_super_indo_user"]);
        [MySuperindo MR_truncateAll];
        MySuperindo *mySuperindo = [MySuperindo MR_importFromObject:dictionary[@"my_super_indo_user"]];
        user.mySuperindo = mySuperindo;
    }

    if (dictionary[@"id_card_verified"]) {
        user.isIDCardVerified = [dictionary[@"id_card_verified"] boolValue];
    }

    return user;
}

// NOTE: [auth]
+ (User *)userFromJWTDictionary:(NSDictionary *)dictionary {
    dictionary = [dictionary removeNSNullValues];

    User *user = [User MR_findFirstByAttribute:@"uuid" withValue:dictionary[@"sub"]];
    if (user == nil) {
        user = [User MR_createEntity];
    }

    user.uuid = dictionary[@"sub"];
    user.email = dictionary[@"email"];
    user.emailVerified = dictionary[@"email_verified"];
    user.firstName = dictionary[@"given_name"];
    user.lastName = dictionary[@"family_name"];
    user.phone = dictionary[@"phone_number"];
    user.verified = dictionary[@"phone_number_verified"];
    user.gender = dictionary[@"gender"];
    user.trackingID = dictionary[@"tracking_id"];
    user.enableUpdatePhone = dictionary[@"enable_update_phone"];

    if (dictionary[@"id"]) {
        user.userID = dictionary[@"id"];
    } else if ([user.trackingID isNumber]){
        user.userID = @([user.trackingID integerValue]);
    }

    user.inviterTrackingID = dictionary[@"inviter_tracking_id"];

    if (dictionary[@"min_age"]) {
        user.minAge = @([dictionary[@"min_age"] intValue]);
    }
    if (dictionary[@"max_age"]) {
        user.maxAge = @([dictionary[@"max_age"] intValue]);
    }

    return user;
}

- (NSArray *)userAddresses {
    NSPredicate *predicate = [NSPredicate predicateWithFormat:@"user = %@", self];
    return [Address MR_findAllWithPredicate:predicate];
}

- (id)trackingInviterID {
    id inviterID = self.inviterTrackingID;
    if (inviterID == nil) {
        inviterID = self.inviterID;
    }
    return inviterID;
}

@end
