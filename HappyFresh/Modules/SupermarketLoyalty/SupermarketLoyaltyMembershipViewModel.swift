//
//  SupermarketLoyaltyViewModel.swift
//  HappyFresh
//
//  Created by <PERSON><PERSON>@HappyFresh on 12/02/23.
//  Copyright © 2023 HappyFresh Inc. All rights reserved.
//

import Foundation

final class SupermarketLoyaltyMembershipViewModel {
    private let service: SupermarketLoyaltyMembershipService
    private let tracking: SupermarketLoyaltyMembershipTrackingService
    private let screenSourceName: String
    private let delegate: SupermarketLoyaltyMembershipDelegate
    
    let title = Observable<String>("")
    let widgets = Observable<[SupermarketLoyaltyMembershipWidgetViewModel]>([])
    let alert = Observable<HFAlert?>(nil)
    let successAlert = Observable<SuccessAlert?>(nil)
    
    struct SuccessAlert {
        let imageData: Data?
        let title: String?
        let description: String?
        let button: String?
    }
    
    private var _successAlert: SuccessAlert?
    
    init(service: SupermarketLoyaltyMembershipService, tracking: SupermarketLoyaltyMembershipTrackingService, screenSourceName: String, delegate: SupermarketLoyaltyMembershipDelegate) {
        self.service = service
        self.tracking = tracking
        self.screenSourceName = screenSourceName
        self.delegate = delegate
    }
    
    func load() async {
        do {
            let config = try await service.fetchWidgetConfiguration()
            switch config {
            case .form(let value):
                title.value =  value.title ?? ""
                widgets.value = [
                    SupermarketLoyaltyMembershipLogoWidgetViewModel(logoURL: value.imageURL),
                    SupermarketLoyaltyMembershipHeadingWidgetViewModel(heading: value.heading),
                    SupermarketLoyaltyMembershipBodyWidgetViewModel(body: value.body),
                    SupermarketLoyaltyMembershipInputWidgetViewModel(
                        title: value.phoneFieldName,
                        description: value.phoneFieldDescription,
                        placeholder: value.phoneFieldPlaceholder,
                        popupImageData: preloadImage(url: value.phoneFieldPopupImageURL),
                        popupTitle: value.phoneFieldPopupTitle,
                        popupDescription: value.phoneFieldPopupDescription,
                        popupButton: value.phoneFieldPopupButtonText,
                        key: "phone",
                        trackingSection: "phone"
                    ),
                    SupermarketLoyaltyMembershipSubmissionWidgetViewModel(
                        button: value.buttonText,
                        tnc: value.buttonDescription),
                ]
                _successAlert = SuccessAlert(
                    imageData: preloadImage(url: value.successPopupImageURL),
                    title: value.successPopupTitle,
                    description: value.successPopupDescription,
                    button: value.successPopupButtonText
                )
                tracking.msiScreenViewed(triggerSource: screenSourceName, msiLinked: false)
            case .linked(let value):
                title.value =  value.title ?? ""
                widgets.value = [
                    SupermarketLoyaltyMembershipBannerWidgetViewModel(imageURL: value.imageURL),
                    SupermarketLoyaltyMembershipValueWidgetViewModel(
                        title: value.msiIDFieldName,
                        value: value.msiIDFieldValue),
                    SupermarketLoyaltyMembershipValueWidgetViewModel(
                        title: value.digitalStampFieldName,
                        value: value.digitalStampFieldValue),
                    SupermarketLoyaltyMembershipMessageWidgetViewModel(
                        message: value.messageFieldValue,
                        state: value.messageFieldState),
                    SupermarketLoyaltyMembershipTncWidgetViewModel(tnc: value.tncFieldValue),
                    SupermarketLoyaltyMembershipShopNowWidgetViewModel()
                ]
                tracking.msiScreenViewed(triggerSource: screenSourceName, msiLinked: true)
            }
        } catch let error as HFAlert {
            alert.value = error
        } catch {
            
        }
    }
    
    private func preloadImage(url: URL?) -> Data? {
        guard let url = url else { return nil }
        
        do {
            return try Data(contentsOf: url)
        } catch {
            return Data()
        }
    }
    
    func submit(inputs: [SupermarketLoyaltyMembershipInputWidgetViewModel]) async {
        do {
            let parameters = inputs.reduce(into: [String: String]()) { result, element in
                if let key = element.key {
                    result[key] = element.value
                }
            }
            let user = try await service.link(parameters: parameters)
            await updateGlobalUser(user: user)
            successAlert.value = _successAlert
            tracking.msiIdSubmitted(success: true, errorMessage: nil)
        } catch let error as HFAlert {
            alert.value = error
            tracking.msiIdSubmitted(success: false, errorMessage: error.errorTypeForTracking)
        } catch {
            
        }
    }
    
    @MainActor
    private func updateGlobalUser(user: User) async {
        service.updateGlobalUser(user: user)
    }
    
    func finishLink() {
        delegate.didFinishLink()
    }
    
    func close() {
        tracking.msiScreenClosed()
    }
    
    func showPopUp(_ widget: SupermarketLoyaltyMembershipInputWidgetViewModel) {
        tracking.msiInfoClicked(section: widget.trackingSection ?? "")
    }
}
