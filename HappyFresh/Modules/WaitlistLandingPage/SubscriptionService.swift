//
//  SubscriptionService.swift
//  HappyFresh
//
//  Created by <PERSON><PERSON> on 21/03/2022.
//  Copyright © 2022 HappyFresh Inc. All rights reserved.
//

import Foundation
import ReactiveSwift
import Result
import SVProgressHUD

public enum SubscriptionConfigType: String {
    case bottomSheet = "bottom_sheet"
    case page = "page"
    case benefits = "benefits"
    case steps = "steps"
    case faqs = "faqs"
}

class SubscriptionService {
    static let replacePrice = "@price"
    let variant = ApptimizeService.subscriptionWaitlistLandingPageVariant
    
    func getBottomSheetConfig() -> SignalProducer<(String, String, String), NoError> {
        let bottomSheetConfig = getBottomSheet()
        let bottomSheetPrice = getPriceBottomSheet()
        
        return SignalProducer.combineLatest(bottomSheetConfig, bottomSheetPrice).map { config, prices -> (String, String, String) in
            let imageString = config[safe: 0] ?? ""
            
            let titleConfig = config[safe: 1]
            let titlePrices = prices[safe: 0]
            var titleString = titleConfig ?? ""
            if let titleConfig = titleConfig, let titlePrices = titlePrices?.components(separatedBy: ";") {
                titleString = ""
                for (index, item) in titleConfig.components(separatedBy: SubscriptionService.replacePrice).enumerated() {
                    titleString += item
                    titleString += titlePrices[safe: index] ?? ""
                }
            }
            
            let subtitleConfig = config[safe: 2]
            let subtitlePrices = prices[safe: 1]
            var subtitleString = subtitleConfig ?? ""
            if let subtitleConfig = subtitleConfig, let subtitlePrices = subtitlePrices?.components(separatedBy: ";") {
                subtitleString = ""
                for (index, item) in subtitleConfig.components(separatedBy: SubscriptionService.replacePrice).enumerated() {
                    subtitleString += item
                    subtitleString += subtitlePrices[safe: index] ?? ""
                }
            }
            
            return (imageString, titleString, subtitleString)
        }
    }
    
    private func getPriceBottomSheet() -> SignalProducer<[String], NoError> {
        API.sharedInstance().getSheetsRemotePrice(type: .bottomSheet, variation: variant)
    }
    
    private func getBottomSheet() -> SignalProducer<[String], NoError> {
        API.sharedInstance().getSheetsRemoteConfig(type: .bottomSheet, variation: variant)
    }
    
    func getBenefitsLanding() -> SignalProducer<(SubscriptionLandingResponse?, String?), NoError> {
        API.sharedInstance().getSubscriptionBenefits()
    }
    
    func getMySubscription() -> SignalProducer<(MySubscriptionModel?, String?), NoError> {
        API.sharedInstance().getMySubscription()
    }
    
    static func goToMySubscription() {
        SVProgressHUD.show()
        API.sharedInstance().getUserData(completion: { _ in
            SVProgressHUD.dismiss()
            App.sharedInstance().goToAccountMenuTab() {
                let viewModel = MySubscriptionViewModel()
                let viewController = MySubscriptionViewController(viewModel)
                App.topViewController()?.navigationController?.pushViewController(viewController, animated: true)
            }
        }, failure: { _,_  in
            SVProgressHUD.dismiss()
        })
    }
        
    static func goToSubscriptionSetting() {
        if let url = URL(string: "itms-apps://apps.apple.com/account/subscriptions") {
            if UIApplication.shared.canOpenURL(url) {
                UIApplication.shared.open(url, options: [:])
            }
        }
    }
    
    static func showPurchaseSuccess(hideCloseButton: Bool = true) {
        Tracker.sharedInstance().trackSubscriptionPlanPurchased(subscriptionStatus: "newly subscribed")
        let successPopup = HFPopup(imageString: "hf_img_apple_boy_ok_120px",
                                   title: NSLocalizedString("You have successfully subscribed!", comment: ""),
                                   subtitle: NSLocalizedString("Welcome to HappyFresh Subscription and enjoy your exclusive benefits right away!", comment: ""),
                                   buttonTitle: NSLocalizedString("Check my benefits", comment: ""),
                                   isHideCloseButton: hideCloseButton)
        successPopup.primaryButtonTappedBlock = {
            Tracker.sharedInstance().trackSubscriptionBenefitViewed()
            SubscriptionService.goToMySubscription()
        }
        App.topViewController()?.showPopup(successPopup)
    }
    
    static func showRestoreSuccess() {
        Tracker.sharedInstance().trackSubscriptionPlanPurchased(subscriptionStatus: "restored")
        let successPopup = HFPopup(imageString: "hf_img_apple_boy_ok_120px",
                                   title: NSLocalizedString("Your subscription has been restored!", comment: ""),
                                   subtitle: NSLocalizedString("Your benefits have also been reactivated. Enjoy them!", comment: ""),
                                   buttonTitle: NSLocalizedString("Check my benefits", comment: ""),
                                   isHideCloseButton: true)
        successPopup.primaryButtonTappedBlock = {
            SubscriptionService.goToMySubscription()
        }
        App.topViewController()?.showPopup(successPopup)
    }
    
    static func showPurchaseError(tapped: @escaping (() -> Void)) {
        Tracker.sharedInstance().trackSubscriptionPlanPurchased(subscriptionStatus: "payment failed")
        let errorPopup = HFPopup(imageString: "hf_img_outofrange_104px",
                                 title: NSLocalizedString("Your payment is unsuccessful", comment: ""),
                                 subtitle: NSLocalizedString("We’re having troubles processing your payment. Would you like to try again?", comment: ""),
                                 buttonTitle: NSLocalizedString("Retry payment", comment: ""))
        errorPopup.primaryButtonTappedBlock = {
            tapped()
        }
        DispatchQueue.main.async {
            App.topViewController()?.showPopup(errorPopup)
        }
    }
    
    static func showSubscriptionExpired(primaryButtonTappedBlock: (() -> Void)? = nil, dismissTappedBlock: (() -> Void)? = nil) {
        let expiredPopup = HFPopup(imageString: "hf_img_outofrange_104px",
                                   title: NSLocalizedString("Your subscription has expired", comment: ""),
                                   subtitle: NSLocalizedString("Would you like to reactivate it? Your benefits will be restored immediately.", comment: ""),
                                   buttonTitle: NSLocalizedString("Reactivate my subscription", comment: ""),
                                   isHideCloseButton: false)
        expiredPopup.primaryButtonTappedBlock = {
            DeeplinkHandler.sharedInstance().handleSubscriptionLandingPage(isFromExpired: true)
            primaryButtonTappedBlock?()
        }
        expiredPopup.dismissTappedBlock = {
            dismissTappedBlock?()
        }
        App.topViewController()?.showPopup(expiredPopup)
    }
    
    static func showSubscribedToDifferentAccount(primaryButtonTappedBlock: (() -> Void)? = nil) {
        let successPopup = HFPopup(imageString: "hf_img_outofrange_104px",
                                   title: NSLocalizedString("You have an active subscription on a different HappyFresh account", comment: ""),
                                   subtitle: NSLocalizedString("Switch to another account to access your subscription benefits", comment: ""),
                                   buttonTitle: NSLocalizedString("Got it", comment: ""),
                                   isHideCloseButton: true)
        successPopup.primaryButtonTappedBlock = {
            primaryButtonTappedBlock?()
        }
        
        App.topViewController()?.showPopup(successPopup)
    }
}
