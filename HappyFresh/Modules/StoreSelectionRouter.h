
#import <UIKit/UIKit.h>

#import "BaseRouter.h"

@class StockLocation, Campaign, CLLocation, Address;

@interface StoreSelectionRouter : BaseRouter

@property (nonatomic, nullable) Campaign *campaignFromDeeplink;
@property (weak, nonatomic) UIViewController *parentViewController;
@property (nonatomic) NSString *screenName;
@property (nonatomic, copy, nullable) void (^dismissalCompletionHandler)(void);

/// Whether to show store home after switch stock location.
@property (nonatomic, assign) BOOL showStoreHome;

- (void)presentFromViewController:(UIViewController *)viewController
                  showCloseButton:(BOOL)showCloseButton
            popToRootAfterDismiss:(BOOL)popToRootAfterDismiss
                  deliveryAddress:(Address *)deliveryAddress
                           source:(NSString *)source;

- (void)presentFromViewController:(UIViewController *)viewController
             campaignFromDeeplink:(Campaign *)campaign
                  showCloseButton:(BOOL)showCloseButton
            popToRootAfterDismiss:(BOOL)popToRootAfterDismiss
                    fromFindStore:(BOOL)fromFindStore
                  deliveryAddress:(Address *)deliveryAddress
                           source:(NSString *)source;

- (void)presentFromViewController:(UIViewController *)viewController
             campaignFromDeeplink:(Campaign *)campaign
                  showCloseButton:(BOOL)showCloseButton
            popToRootAfterDismiss:(BOOL)popToRootAfterDismiss
                    fromFindStore:(BOOL)fromFindStore
                    showStoreHome:(BOOL)showStoreHome
                  deliveryAddress:(Address *)deliveryAddress
                           source:(NSString *)source;

- (void)goToMap;
- (void)goToStoreAuthenticationWithStockLocation:(StockLocation *)stockLocation finishBlok:(void(^)(void))finishBlock;
- (void)dismiss;

@end
