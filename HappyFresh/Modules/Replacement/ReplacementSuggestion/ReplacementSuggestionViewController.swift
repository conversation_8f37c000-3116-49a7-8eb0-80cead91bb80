//
//  ReplacementSuggestionViewController.swift
//  HappyFresh
//
//  Created by <PERSON><PERSON> on 09/09/20.
//  Copyright © 2020 HappyFresh Inc. All rights reserved.
//

import Foundation

class ReplacementSuggestionViewController: GeneralReplacementViewController {
    
    private lazy var productCollectionView: ReplacementProductCollectionView = {
        return ReplacementProductCollectionView()
    }()
    
    private var loadingIndictor: UIActivityIndicatorView = {
        return UIActivityIndicatorView(style: .medium)
    }()
    
    init(viewModel: ReplacementSuggestionViewModel, placement: String, screen: String, source: String) {
        super.init(viewModel: viewModel, placement: placement, screen: screen, source: source)
        screenName = ScreenNameReplacementLowStockPage
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
    }
    
    override func setupBody() {
        bodyTitleLabel.text = NSLocalizedString("In case out of stock, replace with:", comment: "")
        loadingIndictor.heightAnchor.constraint(equalToConstant: 270).isActive = true
        loadingIndictor.startAnimating()
        bodyStackView.addArrangedSubview(loadingIndictor)
        productCollectionView.heightAnchor.constraint(equalToConstant: 270).isActive = true
        bodyStackView.addArrangedSubview(productCollectionView)
        productCollectionView.isHidden = true
        
        primaryButton.addTarget(self, action: #selector(onConfirmAction), for: .touchUpInside)
        primaryButton.isEnabled = false

        let secondaryButton = HFPopupButton(title: NSLocalizedString("Other Options", comment: ""),
                                                type: .ghost)
        secondaryButton.addTarget(self, action: #selector(onOtherOptionsAction), for: .touchUpInside)
        
        buttonStackView.addArrangedSubview(secondaryButton)
        buttonStackView.addArrangedSubview(primaryButton)
        
        bodyStackView.addArrangedSubview(buttonStackView)
        updatePanModalHeight()
        setupObserver()
        
    }
    
    private func setupObserver() {
        guard let viewModel = viewModel as? ReplacementSuggestionViewModel else {return}
        viewModel.stockItems.signal.observeValues({ [weak self] items in
            guard let self = self else { return }
            self.productCollectionView.isHidden = false
            self.loadingIndictor.stopAnimating()
            self.productCollectionView.setup(viewModel: ReplacementProductCollectionViewModel(stockItems: items))
            self.setupButtonObserver()
            self.trackViewLowStockPopup()
        })
        viewModel.isLoading.signal.observeValues { [weak self] isLoading in
            guard let self = self else { return }
            if isLoading {
                self.productCollectionView.showProgressHUD()
            } else {
                self.productCollectionView.hideProgressHUD()
            }
            self.productCollectionView.alpha = isLoading ? 0.8 : 1.0
        }
        viewModel.isSuggestionEmpty.signal.observeValues { [weak self] isSuggestionEmpty in
            guard let self = self else { return }
            if isSuggestionEmpty {
                self.source = self.screen
                self.openReplacementOption()
            }
        }
        viewModel.loadSubstituteRecommendationProducts()
    }
    
    func trackViewLowStockPopup() {
        guard let viewModel = viewModel,
              let variant = viewModel.replacementProductViewModel.variant,
              let stockLocationID = viewModel.stockLocation?.stockLocationID,
              let supplierID = viewModel.stockLocation?.supplier?.supplierID else { return }
        
        Tracker.sharedInstance().trackViewLowStockPopup(variant: variant,
                                                        type: viewModel.recommendationProductType,
                                                        suggestionCount: 3,
                                                        stockLocationID: stockLocationID,
                                                        supplierID: supplierID,
                                                        orderNumber: viewModel.order.number ?? "",
                                                        placement: placement,
                                                        screen: screen)
    }
    
    private func setupButtonObserver() {
        productCollectionView.viewModel?.selectedItem.signal.observeValues({ [weak self] selectedCellViewModel in
            self?.primaryButton.isEnabled = true
        })
    }
    
    @objc func onConfirmAction(sender: UIButton!) {
        guard let viewModel = viewModel,
            let selectedVariant = productCollectionView.viewModel?.selectedItem.value?.variant,
            let selectedPosition = productCollectionView.viewModel?.selectedIndexPosition else { return }
        viewModel.replacementType = .selectProductReplacement
        viewModel.replacementVariant = selectedVariant
        viewModel.replacementPosition = selectedPosition
        addToCart()
        trackReplacement()
    }
    
    @objc func onOtherOptionsAction(sender: UIButton!) {
        self.source = ScreenNameReplacementLowStockPage
        openReplacementOption()
    }
    
    private func openReplacementOption() {
        guard let viewModel = viewModel else { return }
        let replacementOptionViewModel = ReplacementOptionsViewModel(replacementProductViewModel: viewModel.replacementProductViewModel,
                                                                     order: viewModel.order,
                                                                     trackingProperties: viewModel.addToCartTrackingProperties,
                                                                     lineItemPayload: viewModel.lineItemPayload)
        let replacementOptionsViewController = ReplacementOptionsViewController(viewModel: replacementOptionViewModel,
                                                                                placement: placement,
                                                                                screen: screen,
                                                                                source: source)
        self.navigationController?.pushViewController(replacementOptionsViewController, animated: false)
    }
    
}
