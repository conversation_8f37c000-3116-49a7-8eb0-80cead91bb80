//
//  StoreSelectionRouter.swift
//  HappyFresh
//
//  Created by <PERSON><PERSON> on 02/03/21.
//  Copyright © 2021 HappyFresh Inc. All rights reserved.
//

import Foundation

extension StoreSelectionRouter {
    
    @objc
    func goToCampaign() {
        guard let campaign = campaignFromDeeplink else { return }
        let vc = CampaignViewController(viewModel: CampaignViewModel(productId: campaign.productID,
                                         promotionId: campaign.promotionID))
        vc.leftBarButtonItemTypes = .close
        let navigationController = UINavigationController(rootViewController: vc)
        if #available(iOS 13.0, *) {
            navigationController.modalPresentationStyle = .fullScreen
        }
        
        if let view = view as? UIViewController {
            view.present(navigationController, animated: true, completion: nil)
        }
    }
    
    @objc
    func goToStoreHome(with stockLocation: StockLocation) {
        guard showStoreHome,
              let navigationController = (parentViewController as? UINavigationController) ?? parentViewController.navigationController else {
            return
        }
        
        App.sharedInstance().goToStoreHome(
            stockLocation: stockLocation,
            navigationController: navigationController,
            source: screenName)
    }
}

extension StoreSelectionRouter: UIAdaptivePresentationControllerDelegate {
    public func presentationControllerWillDismiss(_ presentationController: UIPresentationController) {
        DeeplinkHandler.sharedInstance().deferredDeeplink = nil
    }
    
    public func presentationControllerDidDismiss(_ presentationController: UIPresentationController) {
        // Handle swipe-to-dismiss or other dismissal methods
        if let dismissalHandler = dismissalCompletionHandler {
            dismissalHandler()
            dismissalCompletionHandler = nil
        }
    }
}
