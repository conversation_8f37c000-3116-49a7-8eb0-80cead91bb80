//
//  HomeViewController.swift
//  HappyFresh
//
//  Created by <PERSON> on 3/27/22.
//  Copyright © 2022 HappyFresh Inc. All rights reserved.
//

import Foundation
import IGListKit
import SVProgressHUD
import AppTrackingTransparency
import ReactiveSwift
import Instructions
import UIKit
import Swift<PERSON>

@objc
class HomeViewController: BaseViewController, HomeUserConsentPopupProtocol, FloatingCartViewDisplayable {
    // FloatingCartViewDisplayable
    var isOnGlobalHome: Bool {
        return true
    }
    
    let bottomInset: CGFloat = 16
    
    enum CoachMarkType {
        case addressBar
        case floatingCart
        case notification
        case recipe
    }
    
    lazy var floatingCartView: FloatingCartView = {
        let floatingCartView = FloatingCartView()
        floatingCartView.gestureRecognizers?.removeAll()
        let tapGestureRecognizer = UITapGestureRecognizer(target: self, action: #selector(didTapOnFloatingCart))
        floatingCartView.addGestureRecognizer(tapGestureRecognizer)
        
        floatingCartView.didShow = { [weak self] isShown in
            self?.floatingCartDidShowHide(isShown: isShown)
        }
        return floatingCartView
    }()

    var userConsentPopupHandler: HomeUserConsentPopupViewModelProtocol {
        viewModel
    }
    
    private lazy var addressView: AddressView = {
        let addressView = AddressView()
        addressView.delegate = self
        return addressView
    }()
    
    private lazy var pendingCartBarButtonItem: HFBarButtonItem = {
        return HFBarButtonItem(type: .pendingCart, target: self, action: #selector(pendingCartButtonTapped))
    }()
    
    private lazy var searchView: UIView = {
        let appName = Bundle.main.object(forInfoDictionaryKey: kCFBundleNameKey as String) as? String ?? ""
        let viewModel = SearchBarViewModel(placeholderText: NSLocalizedString("Search product or supermarket/store", comment: ""))
        let searchView = SearchBarView(viewModel: viewModel)
        searchView.disabledTextFieldInteraction { [weak self] in
            guard let self = self else { return }
            if App.sharedInstance().address == nil {
                self.showLocationPrompt()
                return
            }
            let viewModel = GlobalSearchWebViewModel(suppliers: Array(self.viewModel.suppliers.value))
            let viewController = GlobalSearchWebViewController(viewModel: viewModel)
            viewController.hidesBottomBarWhenPushed = true
            self.navigationController?.pushViewController(viewController, animated: true)
        }
        
        let view = UIView()
        view.backgroundColor = .hf_white()
        view.addSubview(searchView)
        searchView.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.height.equalTo(SearchBarView.Height)
            make.leading.trailing.equalToSuperview().inset(16)
        }
        return view
    }()
    
    lazy var collectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        
        let collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.backgroundColor = .hf_white()
        collectionView.contentInset = UIEdgeInsets(top: SearchBarView.Height + 12, left: 0, bottom: 0, right: 0)
        collectionView.alwaysBounceVertical = true
        return collectionView
    }()
    
    lazy var listAdapter: ListAdapter = {
        let updater = ListAdapterUpdater()
        let adapter = ListAdapter(updater: updater,
                                  viewController: self,
                                  workingRangeSize: 1)
        adapter.collectionView = collectionView
        adapter.scrollViewDelegate = self
        return adapter
    }()
    
    private lazy var coachMarksController: CoachMarksController = {
        let coachMarks = CoachMarksController()
        coachMarks.overlay.backgroundColor = UIColor(red: 0.2, green: 0.2, blue: 0.2, alpha: 0.5)
        coachMarks.overlay.isUserInteractionEnabled = true
        coachMarks.dataSource = self
        coachMarks.delegate = self
        return coachMarks
    }()
    
    private lazy var refreshControl: UIRefreshControl = {
        let refreshControl = UIRefreshControl()
        refreshControl.addTarget(self, action: #selector(refresh(_:)), for: .valueChanged)
        return refreshControl
    }()
    
    let viewModel = HomeViewModel(
        location: App.sharedInstance().address?.location,
        stockLocationID: App.sharedInstance().stockLocation?.stockLocationID)
    
    private lazy var newsfeedDetailRouter = NewsfeedDetailRouter()
    
    private var pendingOrderIndexPath: IndexPath? {
        guard let index = viewModel.widgetViewModels.value.firstIndex(where: { (viewModel) -> Bool in
            viewModel.type == .pendingOrders
        }) else { return nil }
        
        return IndexPath(item: 0, section: index)
    }
    
    private var isShowErrorPopUp = false
    private var coachMarkType: CoachMarkType?
    
    var didSelectStore:((_ stockLocation: StockLocation,
                         _ section: String,
                         _ sectionPosition: Int?) -> Void)?
    
    private var selectedSupplier: Supplier? = nil
    
    private var lastVisibleSectionIndex: Int? {
        collectionView.indexPathsForVisibleItems.map({$0.section}).max()
    }
    
    private var reorderService: ReorderService?
    
    init(selectedSupplier: Supplier? = nil) {
        super.init(nibName: nil, bundle: nil)
        self.selectedSupplier = selectedSupplier
        reorderService = ReorderService(presenter: self)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupView()
        observe()
        setupFloatingCartView()
        
        AppTracking.shared.requestTrackingAndSendEventIfNeeded()
        
        HFPerformance.shared
            .appStartToFirstScreenTrace?
            .setValue(HFPerformanceAttributeFirstScreen.homeScreen, forAttribute: HFPerformanceAttribute.firstScreen)
        HFPerformance.shared.appStartToFirstScreenTrace?.stop()
        HFPerformance.shared.homeStartTrace?.start()
        HFPerformance.shared.homeStartToNewsfeedReadyTrace?.start()
        
        if let onboardingSupplier = selectedSupplier {
            self.gotoStore(supplier: onboardingSupplier)
        }

        viewModel.loadInitialData()
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        viewModel.isScreenActive.value = true
        navigationController?.navigationBar.topItem?.leftBarButtonItem = nil
        
        let topController = App.topViewController()
        if topController is HomeViewController && !PushNotificationUtils.sharedInstance().dismissedOnboardingNotificationPermissionPopup {
            PushNotificationUtils.sharedInstance().askPushNotificationPermissionIfNecessary(in: PrePermissionLocation.home)
        }
        
        DeeplinkUtils.sharedInstance().processDeferredDeeplinkIfExists()
        
        if App.sharedInstance().shouldOpenCartAutomatically {
            showPendingOrder()
            App.sharedInstance().shouldOpenCartAutomatically = false
            App.sharedInstance().shouldShowItemAddedToBasketInCartBanner = true
        }

        OrderService.shared.visibileCarts.removeAll()
        showOrderCancelledConfirmation()
        
        showNotificationCoachMark()
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        viewModel.isScreenActive.value = false
        stopCoachMark()
    }
    
    func setupView() {
        screenName = ScreenNameHomeScreen
        setupNavigationBar()
        statusBarStyle = .lightContent
        tabBarController?.tabBar.isHidden = false
        
        view.addSubview(collectionView)
        collectionView.addSubview(refreshControl)
        collectionView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        listAdapter.dataSource = self
        
        view.addSubview(searchView)
        searchView.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide.snp.top)
            make.leading.trailing.equalToSuperview()
            make.height.equalTo(SearchBarView.Height + 12) // Add extra 12 space under when scrolling
        }
    }
    
    func observe() {
        setupObserve()
        viewModel.widgetViewModels.producer.skip(first: 1)
            .debounce(0.1, on: QueueScheduler.main)
            .startWithValues
        { [weak self] widgetViewModels in
            self?.listAdapter.reloadData()
            let numberOfSection = widgetViewModels.filter({$0.type.isSupplier}).count
            HFPerformance.shared.homeStartTrace?
                .setValue(HFPerformanceAttributeNumberOfSections.from(value: numberOfSection),
                          forAttribute: HFPerformanceAttribute.numberOfSections)
            HFPerformance.shared.homeStartTrace?.stop()
        }
        
        viewModel.isProcessingDataRequest.signal.observeValues { isLoading in
            if isLoading {
                SVProgressHUD.show()
            } else {
                SVProgressHUD.dismiss()
            }
        }
        
        didSelectStore = { [weak self] stockLocation, section, widgetPosition in
            guard let self = self else { return }
            var widgetName: String?
            if let position = widgetPosition  {
                widgetName = self.viewModel.widgetViewModels.value.first(where: {$0.position == position})?.widgetInstanceIdentifier
            }
            let viewModel = StoreHomeViewModel(stockLocation: stockLocation, section: section, sourceScreen: self.screenName, sectionPosition: self.viewModel.selectedWidgetPosition, storeCategory: widgetName, quickActionType: self.viewModel.selectedQuickAction)
            let viewController = StoreHomeBuilder.getStoreHomeViewController(viewModel: viewModel)
            self.navigationController?.pushViewController(viewController, animated: true)
        }

        viewModel.isLoading.producer.startWithValues { [weak self] isLoading in
            guard let self = self else { return }
            if isLoading {
                self.refreshControl.beginRefreshing()
            } else {
                self.refreshControl.endRefreshing()
            }
        }
        
        viewModel.alert.producer.startWithValues { [weak self] alert in
            guard let alert = alert,
                  let self = self,
                  !self.isShowErrorPopUp else { return }
            Tracker.sharedInstance().trackLoadingPageErrorFlyout()
            self.isShowErrorPopUp = true
            AlertController.show(alert: alert,
                                 actionTitle: NSLocalizedString("Refresh page", comment: ""),
                                 buttonAction: { [weak self] in
                guard let self = self else { return }
                self.isShowErrorPopUp = false
                self.viewModel.getWidgets(showCache: false)
            }, showCloseButton: true, completion: { [weak self] in
                self?.isShowErrorPopUp = false
            })
        }
        
        viewModel.isShowPendingCartBarButton.startWithValues { [weak self] (isShowPendingCartBarButton) in
            guard let self = self, self.viewModel.isScreenActive.value else { return }
            self.pendingCartBarButtonItem.badgeShown = isShowPendingCartBarButton
            self.navigationController?.navigationBar.topItem?.setRightBarButton(
                (isShowPendingCartBarButton) ? self.pendingCartBarButtonItem : nil,
                animated: true)
            
            // Completely remove right bar button item
            if !isShowPendingCartBarButton {
                self.navigationController?.navigationBar.topItem?.rightBarButtonItem = nil
            }
        }
        
        viewModel.isScreenActive.producer.skip(first: 1).filter({!$0}).startWithValues { [weak self] isScreenActive in
            self?.viewModel.trackSectionViewed(sectionIndex: self?.viewModel.deepestScrolledIndex ?? self?.lastVisibleSectionIndex)
        }
        
        viewModel.isResignActive.producer.filter({$0}).startWithValues { [weak self] _ in
            self?.viewModel.trackSectionViewed(sectionIndex: self?.viewModel.deepestScrolledIndex ?? self?.lastVisibleSectionIndex)
        }

        NotificationCenter.default.addObserver(
            forName: .highlightedStoreBannerImageLoaded,
            object: nil,
            queue: nil) { [weak self] _ in
                DispatchQueue.main.async {
                    self?.listAdapter.reloadData()
                }
            }
    }
    
    func gotoStore(supplier: Supplier) {
        guard let supplierID = supplier.supplierID else { return }
        self.selectSupplier(supplierID: supplierID, position: 0, source: StoreHomeSourceSectionWarehouse, sectionPosition: 0, didSelectStore: self.didSelectStore)
    }
    
    func observeOrderUpdate() {
        let pendingOrderObserver = viewModel.pendingOrderViewModel.producer.skipNil().flatMap(.latest) { viewModel in
            viewModel.cellViewModels.producer.skip(first: 1)
        }
        SignalProducer.combineLatest(pendingOrderObserver,
                                     viewModel.isScreenActive.producer.filter({$0}))
        .startWithValues({ [weak self] orderViewModels, _ in
            self?.setup(with: orderViewModels.map({$0.order}))
        })
    }
    
    func setupNavigationBar() {
        navigationBarStyle = NavigationBarStyleNoShadow
        
        let navigationItem = self.navigationController?.navigationBar.topItem
        navigationItem?.titleView = addressView
    }
    
    func addressBarTapped() {
        let addressPickerViewController = AddressPickerViewController(
            stockLocation: nil,
            sourceType: .homeScreen)
        addressPickerViewController.hidesBottomBarWhenPushed = true
        navigationController?.pushViewController(addressPickerViewController,
                                                 animated: true)
    }
    
    @objc func refresh(_ sender: AnyObject) {
        if viewModel.isLoading.value {
            return
        }
        viewModel.isPullToRefresh.value = true
        viewModel.getWidgets(showCache: false)
    }
    
    @objc func pendingCartButtonTapped(_ sender: Any) {
        Tracker.sharedInstance().trackGlobalCartClicked()
        scrollToPendingOrder()
    }
     
    private func scrollToPendingOrder() {
        guard let pendingOrderIndexPath = pendingOrderIndexPath else { return }
        collectionView.scrollToItem(at: pendingOrderIndexPath, at: .top, animated: true)
    }
    
    private func showPendingOrder() {
        guard let order = App.sharedInstance().order else { return }
        
        App.topViewController()?.showCart(
            screenName: screenName,
            order: order,
            type: .pending)
    }
    
    private func showOrderCancelledConfirmation() {
        if let dict = App.sharedInstance().editOrderConfirmation,
            let title = dict["title"] as? String,
            let message = dict["message"] as? String {
            let alert = AlertController.alert(withTitle: title, message: message, action: AlertAction.okAction(with: .negative, handler: {
                App.sharedInstance().editOrderConfirmation = nil
            }))
            alert?.show(from: self)
        }
    }
    
    @objc func handleOpenLoyaltyWebView() {
        guard UserService.shared.isLoggedIn else {
            openAuthPage()
            return
        }
        
        App.sharedInstance().goToAccountMenuTab {
            if let currentVC = App.topViewController() as? AccountMenuViewController {
                currentVC.viewModel.myVoucherLoyaltyWidgetViewModel.loyalty.producer
                    .skipNil()
                    .take(first: 1)
                    .startWithValues { loyalty in
                        DispatchQueue.main.async {
                            currentVC.showLoyalty(loyalty)
                        }
                    }
            }
        }
    }
    
    private func openAuthPage() {
        if FirebaseRemoteConfigService.sharedInstance.isLoginSignupWithIdentity {
            self.goToIdentityViewController(previousVC: String(describing: HomeViewController.self),
                                            source: SourceLoyalty)
        } else {
            openLoginSignup(source: SourceLoyalty, completion: nil)
        }
    }
    
    private func goToIdentityViewController(previousVC: String, source: String) {
        if let rootViewController = App.sharedInstance().windowRootViewController() as? UIViewController {
            AuthService.sharedInstance().loginOAuth(withPresenting: rootViewController, source: source) { [weak self] user, error in
                if error != nil { return }
                if previousVC == String(describing: AccountViewController.self) {
                    self?.goToMyAddress()
                }
            }
        }
    }
    
    private func goToAccountNotLoggedIn(previousClass: String, title: String, source: String) {
        if let vc = AccountNotLoggedInViewController.createInstanceWith() {
            vc.contentText = NSLocalizedString("You need to be logged in to update your account details.", comment: "")
            vc.previousVC = previousClass
            vc.source = source
            vc.title = title
            
            open(viewController: vc, isFullScreen: true)
        }
    }
    
    private func goToMyAddress() {
        if !UserService.shared.isLoggedIn {
            if FirebaseRemoteConfigService.sharedInstance.isLoginSignupWithIdentity {
                goToIdentityViewController(previousVC: String(describing: AccountViewController.self),
                                           source: ScreenNameMyAddress)
            } else {
                openLoginSignup(source: ScreenNameMyAddress, completion: nil)
            }
        } else {
            guard let accountVC = AccountViewController.createViewControllerInstance() else { return }
            accountVC.source = screenName
            open(viewController: accountVC, isFullScreen: true)
        }
    }
}

extension HomeViewController: AddressViewDelegate {
    func viewTapped() {
        addressBarTapped()
    }
    
    func notificationTapped() {
        let viewController = NotificationViewController(viewModel: NotificationViewModel())
        self.navigationController?.pushViewController(viewController, animated: true)
    }
}

extension HomeViewController: ListAdapterDataSource {
    func objects(for listAdapter: ListAdapter) -> [ListDiffable] {
        return viewModel.widgetViewModels.value
    }
    
    func emptyView(for listAdapter: ListAdapter) -> UIView? {
        return nil
    }
    
    func listAdapter(_ listAdapter: ListAdapter, sectionControllerFor object: Any) -> ListSectionController {
        guard let widgetViewModel = object as? HomeWidgetViewModel else { return HomeWidget() }
        switch widgetViewModel.type {
        case .categories:
            let widget = CategoriesWidget()
            widget.didSelectItem = { [weak self] cellViewModel, index in
                self?.openCategoriesWebView(categoriesCellViewModel: cellViewModel, index: index, widgetPosition: widgetViewModel.position)
            }
            return widget
        case .suppliers, .storeSections:
            guard let supplierWidgetViewModel = widgetViewModel as? SupplierWidgetViewModel else {
                return SupplierWidget()
            }
            let source = widgetViewModel.type.isStoreCategory ? StoreHomeSourceSectionStoreCategory : StoreHomeSourceSectionSupermarkets
            if supplierWidgetViewModel.isSupermarketSection {
                return createSupermarketTilesWidget(with: widgetViewModel.differenceIdentifier, source: source)
            }
            if supplierWidgetViewModel.widget.displayType == SupplierDisplayType.cardWithLogo.rawValue
                && !supplierWidgetViewModel.isSpecialitiesSection {
                return createSuppliersWidget(with: widgetViewModel.differenceIdentifier, source: source) as SupplierWidget<SupplierWithLogoWidgetItemCell>
            }
            return createSuppliersWidget(with: widgetViewModel.differenceIdentifier, source: source) as SupermarketWidget
        case .pendingOrders:
            let widget = PendingOrderWidget()
            widget.didSelectItem = { [weak self] orderWrapper, _ in
                self?.openPendingCart(with: orderWrapper.order)
            }
            return widget
        case .recipes:
            let widget = RecipesWidget()
            widget.didSelectItem = { [weak self] recipeItem, _ in
                self?.openRecipeDetail(with: recipeItem)
            }
            
            widget.didTapToSeeMore = { [weak self] _ in
                self?.seeAllRecipes()
            }
            return widget
        case .promotions:
            let widget = PromotionBannerWidget()
            widget.didSelectItem = { [weak self] promotionWrapper, index in
                self?.openPromotionDetail(card: promotionWrapper.card, index: index, section: HomeSectionType.promotions.rawValue)
            }
            widget.didTapToSeeMore = { [weak self] _ in
                self?.seeAllPromotions(section: EventPropertyNewsfeedTypeNewsfeed)
            }
            HFPerformance.shared.homeStartToNewsfeedReadyTrace?.stop()
            return widget
        case .skinnyBanner:
            let widget = SkinnyBannerWidget()
            widget.didSelectItem = { [weak self] promotionWrapper, index in
                if let deeplinkUrl = promotionWrapper.card.deeplinkUrl {
                    let deeplinkUrlWithSource = "\(deeplinkUrl)&source=\(ScreenNameHomeScreen)"
                    if let encodedUrl = deeplinkUrlWithSource.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed), let url = URL(string: encodedUrl) {
                        if let displayItem = NewsfeedDisplayItem(newsfeed: promotionWrapper.card) {
                            Tracker.sharedInstance().trackNewsfeedClicked(
                                withId: displayItem.messageId,
                                newsfeedId: nil,
                                type: displayItem.promoType ?? "",
                                newsfeedPosition: index + 1,
                                newsfeedType: displayItem.newsfeedType,
                                triggerSource: self?.screenName ?? "",
                                tab: displayItem.tab,
                                bannerRow: displayItem.bannerRow,
                                newsfeedSection: "skinny banner",
                                voucherDiscountType: nil
                            )
                        }
            
                        App.sharedInstance().goToGlobalHomeTab {
                            DeeplinkUtils.sharedInstance().open(url)
                        }
                    }
                } else {
                    self?.openPromotionDetail(card: promotionWrapper.card, index: index, section: HomeSectionType.promotions.rawValue)
                }
            }
            return widget
        case .firstOrderIncentive:
            let widget = FirstOrderIncentiveWidget()
            widget.didSelectItem = { [weak self] itemVieModel, index in
                self?.openPromotionDetail(card: itemVieModel.card, index: index, section: HomeSectionType.firstOrderIncentive.rawValue)
            }
            return widget
        case .myOrders:
            let widget = MyOrdersWidget()
            widget.didSelectItem = { [weak self] myOrdersWrapper, _ in
                self?.openOderDetail(order: myOrdersWrapper.order)
            }
            
            guard let myOrdersWidgetViewModel = widgetViewModel as? MyOrdersWidgetViewModel else { return widget }
            myOrdersWidgetViewModel.goToRate = { [weak self] order in
                self?.gotoRate(order: order)
            }
            myOrdersWidgetViewModel.goToPastItems = { [weak self] order in
                self?.goToPastItems(order: order)
            }
            myOrdersWidgetViewModel.goToTrackDeliveryURL = { [weak self] (url, orderNumber) in
                self?.goToTrackDeliveryURL(url: url, orderNumber: orderNumber)
            }
            myOrdersWidgetViewModel.openOrderLiveStatus = { [weak self] liveStatus in
                self?.openOrderLiveStatus(liveStatus)
            }
            return widget
        case .hfs:
            return createHFSWidget(with: widgetViewModel)
        case .quickAction:
            let quickActionWidget = QuickActionWidget()
            quickActionWidget.didSelectItem = { [weak self] model in
                self?.handleQuickAction(model: model)
            }
            return quickActionWidget
        case .categoryTiles:
            let widget = CategoryTilesWidget()
            widget.didSelectItem = { cellViewModel, index in
                if let deeplink = cellViewModel.deeplink,
                   let encodedUrl = deeplink.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed),
                   let url = URL(string: encodedUrl) {
                        App.sharedInstance().goToGlobalHomeTab {
                            DeeplinkUtils.sharedInstance().open(url)
                        }
                    }
                }
            return widget
        default:
            return HomeWidget()
        }
    }
    
    //MARK: suppliers actions handling
    
    private func createSuppliersWidget<Cell: BaseSupplierWidgetItemCell, Widget: SupplierWidget<Cell>>(with widgetID: String, source: String) -> Widget {
        let widget = Widget()
        widget.didTapToSeeMore = { [weak self] isSeeAllCard in
            if App.sharedInstance().address == nil {
                self?.showLocationPrompt()
                return
            }
            guard let widgetViewModel = self?.viewModel.widgetViewModels.value.first(where: {$0.differenceIdentifier == widgetID}) as? SupplierWidgetViewModel else {
                return
            }
            let sectionName = widgetViewModel.type.isStoreCategory ? StoreHomeSourceSectionStoreCategory : StoreHomeSourceSectionSupermarkets
            let storeCategory = widgetViewModel.isSupermarketSection || widgetViewModel.isSpecialitiesSection ? StoreHomeSourceSectionSupermarkets : widgetViewModel.widgetInstanceIdentifier
            let storeListVM = StoreListViewModel(
                title: widgetViewModel.sectionTitle,
                sectionName: sectionName,
                storeCategory: storeCategory,
                triggerSource: isSeeAllCard ? "see all card" : "arrow button",
                selectedFilter: widgetViewModel.widget.filterName,
                supplierWidgetViewModels: self?.viewModel.supplierWidgetViewModels ?? []
            )
            self?.navigationController?.pushViewController(StoreListViewController(viewModel: storeListVM), animated: true)
        }
        
        widget.didSelectItem = { [weak self] supplierViewModel, position in
            guard let supplierID = supplierViewModel.supplierID else {
                return
            }
            self?.selectSupplier(supplierID: supplierID,
                                 position: position,
                                 source: source, sectionPosition: widget.viewModel?.position, didSelectStore: self?.didSelectStore)
        }
        return widget
    }

    private func createSupermarketTilesWidget(with widgetID: String, source: String) -> SupermarketTilesWidget {
        let widget = SupermarketTilesWidget()

        widget.didSelectItem = { [weak self] supplierViewModel, position, widgetSource in
            guard let supplierID = supplierViewModel.supplierID else {
                return
            }
            let source = widgetSource ?? source
            self?.selectSupplier(supplierID: supplierID,
                                 position: position,
                                 source: source,
                                 sectionPosition: widget.viewModel?.position,
                                 didSelectStore: self?.didSelectStore)
        }

        widget.didTapToSeeMore = { [weak self] isSeeAllCard in
            if App.sharedInstance().address == nil {
                self?.showLocationPrompt()
                return
            }
            guard let widgetViewModel = self?.viewModel.widgetViewModels.value.first(where: {$0.differenceIdentifier == widgetID}) as? SupplierWidgetViewModel else {
                return
            }
            let sectionName = widgetViewModel.type.isStoreCategory ? StoreHomeSourceSectionStoreCategory : StoreHomeSourceSectionSupermarkets
            let storeCategory = widgetViewModel.isSupermarketSection || widgetViewModel.isSpecialitiesSection ? StoreHomeSourceSectionSupermarkets : widgetViewModel.widgetInstanceIdentifier
            let storeListVM = StoreListViewModel(
                title: widgetViewModel.sectionTitle,
                sectionName: sectionName,
                storeCategory: storeCategory,
                triggerSource: isSeeAllCard ? "see all card" : "arrow button",
                selectedFilter: widgetViewModel.widget.filterName,
                supplierWidgetViewModels: self?.viewModel.supplierWidgetViewModels ?? []
            )
            self?.navigationController?.pushViewController(StoreListViewController(viewModel: storeListVM), animated: true)
        }

        return widget
    }

    private func selectSupplier(supplierID: NSNumber,
                                position: Int,
                                source: String,
                                sectionPosition: Int?,
                                quickActionType: String? = nil,
                                didSelectStore:((_ stockLocation: StockLocation, _ section: String, _ sectionPosition: Int?) -> Void)?) {
        if App.sharedInstance().address == nil {
            showLocationPrompt()
        } else {
            HFPerformance.shared.storeHomeStartTrace?.start()
            self.viewModel.didSelectSupplier(supplierID: supplierID,
                                             supplierPosition: position,
                                             source: source,
                                             sectionPosition: sectionPosition,
                                             quickActionType: quickActionType,
                                             didSelectStore: didSelectStore)
        }
    }
    
    @objc
    func showLocationPrompt() {
        coachMarkType = .addressBar
        coachMarksController.start(in: .currentWindow(of: self))
        Tracker.sharedInstance().trackShowLocationFlyout(withSource: source)
    }
    
    func stopCoachMark() {
        coachMarksController.stop(immediately: true)
    }

    @objc
    func showNotificationCoachMark() {
//        if HFUserDefault.sharedInstance().isNotificationCoachmarkShown {
//            return
//        }
        coachMarkType = .notification
        coachMarksController.start(in: .currentWindow(of: self))
    }

    @objc
    func showRecipeCoachMark() {
//        if HFUserDefault.sharedInstance().isRecipeCoachmarkShown {
//            return
//        }
        coachMarkType = .recipe
        coachMarksController.start(in: .currentWindow(of: self))
    }

    private func getRecipesTabBarItem() -> UIView? {
        guard let tabBarController = App.sharedInstance().mainTabBarController(),
              let tabBar = tabBarController.tabBar as UITabBar?,
              tabBarController.viewControllers?.count ?? 0 > 1 else {
            return nil
        }

        // Recipes tab is at index 1 (Home=0, Recipes=1, Promo=2, Order=3, Account=4)
        let recipesTabIndex = 1
        if recipesTabIndex < tabBar.items?.count ?? 0 {
            // Get the tab bar item view
            let tabBarItemViews = tabBar.subviews.filter { $0 is UIControl }
            if recipesTabIndex < tabBarItemViews.count {
                return tabBarItemViews[recipesTabIndex]
            }
        }

        return nil
    }
    
    //MARK: Recipe actions
    
    private func openRecipeDetail(with recipeCellViewModel: RecipeCellViewModel) {
        guard let inspirationDetailRouter = InspirationDetailRouter(shoppingListID: recipeCellViewModel.recipeID,
                                                                    shoppingListType: .inspiration,
                                                                    position:UInt(recipeCellViewModel.position),
                                                                    stockLocation: nil,
                                                                    sourceScreen: nil), // No add to cart if recipe accessed from Global Home.
              let view = inspirationDetailRouter.view as? InspirationDetailViewController else { return }
        inspirationDetailRouter.source = self.screenName
        view.shouldHideCartAndSearchBarButtonItem = true
        view.hidesBottomBarWhenPushed = true
        navigationController?.pushViewController(view, animated: true)
    }
    
    private func seeAllRecipes() {
        let inspirationList = InspirationListRouter()
        inspirationList.source = self.screenName
        if let vc = inspirationList.view as? UIViewController {
            vc.hidesBottomBarWhenPushed = true
            navigationController?.pushViewController(vc, animated: true)
        }
    }
    
    //MARK: Pending Order actions
    
    private func openPendingCart(with order: Order) {
        showCart(
            screenName: screenName,
            order: order,
            type: .pending)
    }
    
    private func openMyCarts() {
        let viewModel = MyCartsViewModel(pendingOrderObserver: self.viewModel.pendingOrderViewModel)
        let myCartsVC = MyCartsViewController(viewModel: viewModel)
        navigationController?.pushViewController(myCartsVC, animated: true)
    }
    
    //MARK: Categories actions
    
    private func openCategoriesWebView(categoriesCellViewModel: CategoriesWidgetCellViewModel, index: Int, widgetPosition: Int?) {
        let viewModel = GlobalCategoryWebViewModel(
            category: categoriesCellViewModel.category,
            suppliers: Array(viewModel.suppliers.value),
            source: screenName,
            categoryPosition: index,
            sectionPosition: viewModel.getSectionPosition(from: widgetPosition))
        let viewController = GlobalCategoryWebViewController(viewModel: viewModel)
        viewController.hidesBottomBarWhenPushed = true
        navigationController?.pushViewController(viewController, animated: true)
        
        Tracker.sharedInstance().trackSelectCategory(categoriesCellViewModel.trackingName)
    }
    
    //MARK: Promotions actions
    
    private func seeAllPromotions(section: String) {
        App.sharedInstance().goToNewsfeedTab(source: screenName,
                                              section: section,
                                              selectedPromoType: .allDeals,
                                              completion: nil)
    }
    
    private func openPromotionDetail(card: Newsfeed, index: Int, section: String) {
        self.newsfeedDetailRouter.setup(
            with: card,
            trackingSource: self.screenName,
            bannerPosition: index + 1,
            tab: nil,
            section: section,
            newsfeedType: nil)
        self.newsfeedDetailRouter.present(from: self, embedInNavigationController: true, animated: true)
    }
    
    //MARK: Voucher Loyalty actions
    
    private func seeAllVouchers() {
        App.sharedInstance().goToNewsfeedTab(source: ScreenNameHomeScreen,
                                              section: EventPropertyNewsfeedTypeVoucher,
                                              selectedPromoType: .myVouchers,
                                              completion: nil)
    }
    
    private func showLoyalty(_ loyalty: Loyalty?) {
        let viewModel = LoyaltyWebViewModel(loyalty: loyalty)
        let viewController = LoyaltyWebViewController(viewModel: viewModel)
        viewController.hidesBottomBarWhenPushed = true
        
        navigationController?.pushViewController(viewController, animated: true)
    }
    
    //MARK: My Orders actions
    
    private func openOderDetail(order: Order) {
        guard let viewModel = OrderViewModel(order: order, source: self.screenName) else { return }
        let vc = OrderViewController(viewModel: viewModel)
        vc.source = self.screenName
        vc.hidesBottomBarWhenPushed = true
        self.navigationController?.pushViewController(vc, animated: true)
    }
    
    private func gotoRate(order: Order) {
        let viewModel = RatingGeneralViewModel(order: order, ratingCategory: .order)
        let viewController = RatingGeneralViewController(viewModel: viewModel)
        viewController.source = self.screenName
        viewController.leftBarButtonItemTypes = .close
        let navController = UINavigationController(rootViewController: viewController)
        self.present(navController, animated: true, completion: nil)
    }
    
    private func goToPastItems(order: Order) {
        guard let orderNumber = order.number else { return }
        reorderService?.reorder(orderNumber: orderNumber, screenName: ScreenNameHomeScreen)
    }
    
    private func goToTrackDeliveryURL(url: NSURL, orderNumber: String) {
        let vc = WebViewController(url: url,
                                   title: NSLocalizedString("Track delivery", comment: ""),
                                   screenName: ScreenNameDriverTracker,
                                   source: self.screenName,
                                   showRefreshButton: true)
        vc.orderNumber = orderNumber
        vc.leftBarButtonItemTypes = .close
        
        let navController = UINavigationController(rootViewController: vc)
        self.present(navController, animated: true, completion: nil)
    }
    
    private func openOrderLiveStatus(_ liveStatus: OrderLiveStatus) {
        if liveStatus.ctaType == .payDebt {
            SVProgressHUD.show()

            UserService.shared.getUserToken { [weak self] userToken in
                SVProgressHUD.dismiss()

                guard let userToken,
                      let outstandingPaymentURL = liveStatus.url?.add(
                        userToken: userToken
                      )?.withQueries([URLQueryItem(name: "source", value: "home screen")]) else {
                    return
                }

                let viewModel = OutstandingPaymentWebViewModel(
                    url: outstandingPaymentURL,
                    title: ""
                )
                let viewController = OutstandingPaymentWebViewController(viewModel: viewModel)
                viewController.hidesBottomBarWhenPushed = true
                self?.navigationController?.pushViewController(viewController, animated: true)
            }
        } else {
            guard let url = liveStatus.url else { return }
            UIApplication.shared.open(url)
        }
    }

    //MARK: HFS Widget
    
    private func createHFSWidget(with widgetViewModel: HomeWidgetViewModel) -> HFSWidget {
        let widget = HFSWidget()
        widget.didSelect = { [weak self] widgetViewModel in
            guard let hfsWidgetViewModel = widgetViewModel as? HFSWidgetViewModel,
                  let supplerID = hfsWidgetViewModel.supplier.supplierID else {
                return
            }
            self?.selectSupplier(supplierID: supplerID, position: 0, source: StoreHomeSourceSectionWarehouse, sectionPosition: hfsWidgetViewModel.position, didSelectStore: self?.didSelectStore)
        }
        
        widget.didSelectItem = { [weak self] categoryViewModel, index in
            guard let self = self,
                    let hfsWidgetViewModel = widgetViewModel as? HFSWidgetViewModel,
                  let supplerID = hfsWidgetViewModel.supplier.supplierID else {
                return
            }
            self.selectSupplier(supplierID: supplerID, position: 0, source: StoreHomeSourceSectionWarehouse, sectionPosition: hfsWidgetViewModel.position) { [weak self] stockLocation, section, sectionPosition in
                guard let self = self else {
                    return
                }
                self.didSelectStore?(stockLocation, section, sectionPosition)
                let categoryRouter = CategoryRouter()
                categoryRouter.present(from: self,
                                       embedInNavigationController: true,
                                       animated: true,
                                       taxon: categoryViewModel.taxon,
                                       productType: nil, rank: index,
                                       source: self.screenName,
                                       triggerSectionSource: section,
                                       isSpecial: false,
                                       fromProductShowcase: nil,
                                       promotionID: nil,
                                       isFromGlobalSearch: false,
                                       isFromGlobalCategory: false)
            }
        }
        return widget
    }
    
    //MARK: - Quick Actions
    
    private func handleQuickAction(model: QuickActionModel) {
        switch model.type {
        case .promos:
            seeAllPromotions(section: EventPropertyQuickActionSourceSection)
        case .allStores:
            let storeListVM = StoreListViewModel(title: "",
                                                 sectionName: EventPropertyQuickActionSourceSection,
                                                 storeCategory: EventPropertyQuickActionSourceSection,
                                                 triggerSource: EventPropertyQuickActionSourceSection,
                                                 selectedFilter: "",
                                                 supplierWidgetViewModels: viewModel.supplierWidgetViewModels,
                                                 quickActionType: model.identifier)
            navigationController?.pushViewController(StoreListViewController(viewModel: storeListVM), animated: true)
            return
        case .mostPurchased(let supplier):
            guard let supplierID = supplier.supplierID else {
                return
            }
            selectSupplier(supplierID: supplierID,
                           position: 1,
                           source: EventPropertyQuickActionSourceSection,
                           sectionPosition: 1,
                           quickActionType: model.identifier,
                           didSelectStore: didSelectStore)
        case .hfs:
            guard let hfsWidgetViewModel = viewModel.widgetViewModels.value.first(where: {$0.type == .hfs}) as? HFSWidgetViewModel,
                  let supplerID = hfsWidgetViewModel.supplier.supplierID else {
                return
            }
            selectSupplier(supplierID: supplerID,
                           position: 1,
                           source: EventPropertyQuickActionSourceSection,
                           sectionPosition: 1,
                           quickActionType: model.identifier,
                           didSelectStore: didSelectStore)
        case .vipMember:
            if let vipMemberConfig = FirebaseRemoteConfigService.sharedInstance.vipMemberConfig {
                let vipMemberViewModel = VIPMemberViewModel(config: vipMemberConfig)
                let vipMemberViewController = VIPMemberViewController(viewModel: vipMemberViewModel)
                vipMemberViewController.source = "home screen"
                vipMemberViewController.hidesBottomBarWhenPushed = true

                navigationController?.pushViewController(vipMemberViewController, animated: true)
            }
        }
    }
}

extension HomeViewController: CoachMarksControllerDataSource, CoachMarksControllerDelegate {
    private var coachMarkBodyView: HFCoachMarkBodyView {
        switch(coachMarkType) {
        case .addressBar:
            return HFCoachMarkBodyView(title: NSLocalizedString("Where to?", comment: ""),
                                       description: NSLocalizedString("We need to ensure if your address is within our coverage area",
                                                                      comment: ""),
                                       buttonTitle: NSLocalizedString("Change", comment: "")) { [weak self] in
                self?.addressBarTapped()
            }
        case .floatingCart:
            let style = HFCoachMarkBodyStyle(descriptionColor: .hf_grey_80(),
                                             descriptionFont: .hf_tiny(),
                                             buttonTitleColor: .hf_white(),
                                             buttonTitleFont: .hf_title_9(),
                                             buttonBackgroundColor: .hf_happy_100(),
                                             buttonContentInsets: UIEdgeInsets(top: 8, left: 30, bottom: 8, right: 30))
            return HFCoachMarkBodyView(title: NSLocalizedString("New Place for Your Carts!",
                                                                comment: ""),
                                       description: NSLocalizedString("Now you can easily access your pending carts here!",
                                                                      comment: ""),
                                       buttonTitle: NSLocalizedString("OK", comment: ""),
                                       style: style)
        case .notification:
            let style = HFCoachMarkBodyStyle(descriptionColor: .hf_grey_80(),
                                             descriptionFont: .hf_tiny(),
                                             buttonTitleColor: .hf_white(),
                                             buttonTitleFont: .hf_title_9(),
                                             buttonBackgroundColor: .hf_happy_100(),
                                             buttonContentInsets: UIEdgeInsets(top: 8, left: 30, bottom: 8, right: 30))
            return HFCoachMarkBodyView(title: NSLocalizedString("Check your notifications", comment: ""),
                                       description: NSLocalizedString("Tap here to track your order and special offers.",
                                                                      comment: ""),
                                       buttonTitle: NSLocalizedString("OK", comment: ""),
                                       style: style)
        case .recipe:
            let style = HFCoachMarkBodyStyle(descriptionColor: .hf_grey_80(),
                                             descriptionFont: .hf_tiny(),
                                             buttonTitleColor: .hf_white(),
                                             buttonTitleFont: .hf_title_9(),
                                             buttonBackgroundColor: .hf_happy_100(),
                                             buttonContentInsets: UIEdgeInsets(top: 8, left: 30, bottom: 8, right: 30))
            return HFCoachMarkBodyView(title: NSLocalizedString("Discover recipes", comment: ""),
                                       description: NSLocalizedString("Explore and shop ingredients for every recipe",
                                                                      comment: ""),
                                       buttonTitle: NSLocalizedString("OK", comment: ""),
                                       style: style)
        default:
            return HFCoachMarkBodyView(title: nil,
                                            description: nil)
        }
    }
    
    private var coachMarkTargetView: UIView? {
        switch coachMarkType {
        case .addressBar:
            return addressView
        case .floatingCart:
            return floatingCartView
        case .notification:
            return addressView.notificationButtonView
        case .recipe:
            return getRecipesTabBarItem()
        default: return nil
        }
    }

    public func coachMarksController(_ coachMarksController: CoachMarksController,
                                     coachMarkAt index: Int) -> CoachMark {
        var coachMark = coachMarksController.helper.makeCoachMark(
            for: coachMarkTargetView) { (frame) -> UIBezierPath in
            return UIBezierPath(roundedRect: frame, cornerRadius: 8)
        }
        switch(coachMarkType) {
        case .addressBar:
            coachMark.arrowOrientation = .top
            coachMark.gapBetweenCoachMarkAndCutoutPath = 8
        case .floatingCart:
            coachMark.arrowOrientation = .bottom
            coachMark.gapBetweenCoachMarkAndCutoutPath = 8
            if let coachMarkTargetView = coachMarkTargetView {
                coachMark.pointOfInterest = CGPoint(x: 48, y: coachMarkTargetView.center.y)
            }
        case .notification:
            coachMark.arrowOrientation = .top
            coachMark.gapBetweenCoachMarkAndCutoutPath = 8
        case .recipe:
            coachMark.arrowOrientation = .bottom
            coachMark.gapBetweenCoachMarkAndCutoutPath = 8
        default: break
        }
        
        return coachMark
    }

    public func numberOfCoachMarks(for coachMarksController: CoachMarksController) -> Int {
        return 1
    }

    public func coachMarksController(
        _ coachMarksController: CoachMarksController,
        coachMarkViewsAt index: Int,
        madeFrom coachMark: CoachMark) -> (bodyView: (UIView & CoachMarkBodyView), arrowView: (UIView & CoachMarkArrowView)?) {
        let coachMarkArrowView = CoachMarkArrowDefaultView(orientation: coachMark.arrowOrientation ?? .top)
        
        return (bodyView: coachMarkBodyView, arrowView: coachMarkArrowView)
    }
    
    func coachMarksController(_ coachMarksController: CoachMarksController, didEndShowingBySkipping skipped: Bool) {
        guard let coachMarkType = coachMarkType else { return }
        self.coachMarkType = nil
        switch coachMarkType {
        case .floatingCart:
            HFUserDefault.sharedInstance().isGlobalHomeFloatingCartCoachmarkShown = true
        case .notification:
            HFUserDefault.sharedInstance().isNotificationCoachmarkShown = true
            showRecipeCoachMark()
        case .recipe:
            HFUserDefault.sharedInstance().isRecipeCoachmarkShown = true
        default:
            break
        }
    }
    
}

//MARK: Floating cart appearance handling

extension HomeViewController: UIScrollViewDelegate {
    @objc func didTapOnFloatingCart() {
        Tracker.sharedInstance().trackGlobalCartClicked()
        guard let orderViewModels = viewModel.pendingOrderViewModel.value?.cellViewModels.value else {
            return
        }
        guard orderViewModels.count > 1 else {
            if let order = orderViewModels.first?.order {
                openPendingCart(with: order)
            }
            return
        }
        openMyCarts()
    }
    
    private func floatingCartDidShowHide(isShown: Bool) {
        if isShown {
            startFloatingCartCoachMarkIfNeeded()
        }
        updateCollectionViewLayout(floatingCartIsShowing: isShown)
    }
    
    private func startFloatingCartCoachMarkIfNeeded() {
        if viewModel.isScreenActive.value,
            !HFUserDefault.sharedInstance().isGlobalHomeFloatingCartCoachmarkShown,
            UserService.shared.isLoggedIn,
            App.topViewController() is HomeViewController {
            UIView.performWithoutAnimation { [unowned self] in
                coachMarkType = .floatingCart
                coachMarksController.start(in: .currentWindow(of: self))
            }
        }
    }
    
    private func updateCollectionViewLayout(floatingCartIsShowing: Bool = false) {
        if floatingCartIsShowing {
            collectionView.contentInset.bottom = floatingCartView.bounds.height + bottomInset
        }
    }
    
    func scrollViewWillBeginDragging(_ scrollView: UIScrollView) {
        shouldShowFloatingCartView = false
    }
    
    func scrollViewDidEndDragging(_ scrollView: UIScrollView, willDecelerate decelerate: Bool) {
        guard !decelerate else { return }
        shouldShowFloatingCartView = true
        updateDeepestScrolledIndexIfNeed()
    }
    
    func scrollViewDidEndDecelerating(_ scrollView: UIScrollView) {
        shouldShowFloatingCartView = true
        updateDeepestScrolledIndexIfNeed()
    }
    
    private func updateDeepestScrolledIndexIfNeed() {
        guard !viewModel.isAlreadyReachLastSection else {
            return
        }
        viewModel.updateDeepestScrolledIndex(with: lastVisibleSectionIndex)
    }
}

//private func openCategoryDetail(with taxon: Taxon, index: Int, widgetPosition: Int?) {
//    let viewModel = GlobalCategoryWebViewModel(
//        category: taxon,
//        suppliers: Array(viewModel.suppliers.value),
//        source: screenName,
//        categoryPosition: index,
//        sectionPosition: viewModel.getSectionPosition(from: widgetPosition))
//    let viewController = GlobalCategoryWebViewController(viewModel: viewModel)
//    viewController.hidesBottomBarWhenPushed = true
//    navigationController?.pushViewController(viewController, animated: true)
//    
//    Tracker.sharedInstance().trackSelectCategory(taxon.permalink ?? "")
//}

