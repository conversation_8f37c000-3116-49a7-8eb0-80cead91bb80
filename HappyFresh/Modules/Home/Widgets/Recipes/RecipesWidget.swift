//
//  RecipesWidget.swift
//  HappyFresh
//
//  Created by <PERSON><PERSON>u on 12/04/2022.
//  Copyright © 2022 HappyFresh Inc. All rights reserved.
//

import Foundation

class RecipesWidget: HorizontalWidget<RecipeCellViewModel, OldRecipeCell> {
    static let itemSpacing: CGFloat = 16
    static let bottomSpacing: CGFloat = 2
    
    override var widgetCellHeight: CGFloat {
        let numberOfItems = viewModel?.widgetItems.count ?? 0
        return OldRecipeCell.maxHeight * (numberOfItems > 2 ? 2 : 1) + RecipesWidget.itemSpacing + RecipesWidget.bottomSpacing
    }
    
    override func createMainCell(index: Int) -> UICollectionViewCell {
        guard let cellId = viewModel?.differenceIdentifier else {
            return UICollectionViewCell()
        }
        
        let cell = collectionContext?.dequeueReusableCell(of: RecipesCollectionViewCell.self, withReuseIdentifier: cellId, for: self, at: index) as? RecipesCollectionViewCell ?? RecipesCollectionViewCell()
        cell.viewModels = viewModel?.widgetItems
        cell.recipeDidSelect = { [weak self] recipeItem in
            self?.didSelectItem?(recipeItem, index)
        }
        return cell
    }
    
}
