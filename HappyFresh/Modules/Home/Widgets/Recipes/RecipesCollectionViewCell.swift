//
//  RecipesCollectionViewCell.swift
//  HappyFresh
//
//  Created by <PERSON><PERSON> on 12/04/2022.
//  Copyright © 2022 HappyFresh Inc. All rights reserved.
//

import UIKit

class RecipesCollectionViewCell: UICollectionViewCell {
    lazy var collectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .horizontal
        let itemSpacing: CGFloat = RecipesWidget.itemSpacing
        layout.minimumInteritemSpacing = itemSpacing
        layout.minimumLineSpacing = itemSpacing
        layout.scrollDirection = .horizontal
        layout.sectionInset = UIEdgeInsets(top: 0, left: itemSpacing, bottom: RecipesWidget.bottomSpacing, right: itemSpacing)
        let view = UICollectionView(frame: .zero, collectionViewLayout: layout)
        view.backgroundColor = .clear
        view.alwaysBounceVertical = false
        view.alwaysBounceHorizontal = false
        view.showsHorizontalScrollIndicator = false
        self.contentView.addSubview(view)
        view.snp.makeConstraints { make in
            make.top.leading.trailing.bottom.equalToSuperview()
        }
        return view
    }()
    
    var recipeDidSelect:((RecipeCellViewModel) -> Void)?
    
    var viewModels: [RecipeCellViewModel]? {
        didSet {
            collectionView.reloadData()
        }
    }
    
    required init?(coder aDecoder: NSCoder) {
      super.init(coder:aDecoder)
        commonInit()
    }
    
    override init(frame: CGRect) {
      super.init(frame:frame)
        commonInit()
    }
    
    func commonInit() {
        collectionView.register(OldRecipeCell.self, forCellWithReuseIdentifier: OldRecipeCell.reuseIdentifier())
        collectionView.delegate = self
        collectionView.dataSource = self
    }
}

extension RecipesCollectionViewCell: UICollectionViewDataSource {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return viewModels?.count ?? 0
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        if let cell = collectionView.dequeueReusableCell(withReuseIdentifier: OldRecipeCell.reuseIdentifier(), for: indexPath) as? OldRecipeCell {
            if let viewModels = viewModels {
                cell.configure(with: viewModels[indexPath.row])
            }
            return cell
        } else {
            return OldRecipeCell()
        }
    }
}

extension RecipesCollectionViewCell: UICollectionViewDelegate {
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        if let recipeDidSelect = recipeDidSelect, let viewModels = viewModels {
            recipeDidSelect(viewModels[indexPath.row])
        }
    }
}

extension RecipesCollectionViewCell: UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        return CGSize(width: OldRecipeCell.width, height: OldRecipeCell.maxHeight)
    }
}

