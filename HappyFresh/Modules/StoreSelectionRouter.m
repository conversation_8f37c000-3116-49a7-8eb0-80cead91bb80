
#import "StoreSelectionRouter.h"
#import "StoreSelectionViewController.h"
#import "StoreSelectionPresenter.h"
#import "StoreSelectionInteractor.h"

#import "StoreAuthenticationViewController.h"
#import "MapRouter.h"
#import "TrackerConstants.h"
#import "App.h"
#import "HappyFresh-Swift.h"

@interface StoreSelectionRouter ()

@property (nonatomic, assign) BOOL popToRootAfterDismiss;
@property (nonatomic) MapRouter *mapRouter;
@property (nonatomic) Address *deliveryAddress;

@end

@implementation StoreSelectionRouter

- (id<ViewBehavior>)createView {
    return [StoreSelectionViewController new];
}

- (BasePresenter *)createPresenter {
    return [StoreSelectionPresenter new];
}

- (BaseInteractor *)createInteractor {
    return [StoreSelectionInteractor new];
}

- (MapRouter *)mapRouter {
    if (_mapRouter == nil) {
        _mapRouter = [MapRouter new];
    }
    return _mapRouter;
}

- (void)presentFromViewController:(UIViewController *)viewController
                  showCloseButton:(BOOL)showCloseButton
            popToRootAfterDismiss:(BOOL)popToRootAfterDismiss
                  deliveryAddress:(Address *)deliveryAddress
                           source:(NSString *)source {
    [self presentFromViewController:viewController
               campaignFromDeeplink:nil
                    showCloseButton:showCloseButton
              popToRootAfterDismiss:popToRootAfterDismiss
                      fromFindStore:NO
                    deliveryAddress:deliveryAddress
                             source:source];
}

- (void)presentFromViewController:(UIViewController *)viewController
             campaignFromDeeplink:(Campaign *)campaign
                  showCloseButton:(BOOL)showCloseButton
            popToRootAfterDismiss:(BOOL)popToRootAfterDismiss
                    fromFindStore:(BOOL)fromFindStore
                  deliveryAddress:(Address *)deliveryAddress
                           source:(NSString *)source {
    [self presentFromViewController:viewController
               campaignFromDeeplink:campaign
                    showCloseButton:showCloseButton
              popToRootAfterDismiss:popToRootAfterDismiss
                      fromFindStore:fromFindStore
                      showStoreHome:NO
                    deliveryAddress:deliveryAddress
                             source:source];
}

- (void)presentFromViewController:(UIViewController *)viewController
             campaignFromDeeplink:(Campaign *)campaign
                  showCloseButton:(BOOL)showCloseButton
            popToRootAfterDismiss:(BOOL)popToRootAfterDismiss
                    fromFindStore:(BOOL)fromFindStore
                    showStoreHome:(BOOL)showStoreHome
                  deliveryAddress:(Address *)deliveryAddress
                           source:(NSString *)source {
    [self setupModule];
    
    if (showCloseButton) {
        [self.view setLeftBarButtonItemTypes:BarButtonItemTypeClose];
    }
    
    if (fromFindStore) {
        self.screenName = ScreenNameChooseStore;
    } else {
        self.screenName = ScreenNameChooseStore;
    }
    [(StoreSelectionViewController *)self.view setScreenName:self.screenName];
    [(StoreSelectionViewController *)self.view setSource:source];
    [(StoreSelectionInteractor *)self.interactor setAddress:[App sharedInstance].address];
    [(StoreSelectionInteractor *)self.interactor setShowStoreHome:showStoreHome];

    self.deliveryAddress = deliveryAddress;
    self.campaignFromDeeplink = campaign;
    self.popToRootAfterDismiss = popToRootAfterDismiss;
    self.showStoreHome = showStoreHome;
    self.parentViewController = viewController;
    
    UINavigationController *navigationController = [[UINavigationController alloc] initWithRootViewController:self.view];
    
    [navigationController.presentationController setDelegate:self];

    [viewController presentViewController:navigationController
                                 animated:YES
                               completion:nil];
}

- (void)dismiss {
    if (self.popToRootAfterDismiss) {
        UIViewController *rootVC = [App sharedInstance].windowRootViewController;
        [rootVC dismissViewControllerAnimated:YES completion:^{
            [[App sharedInstance] popToRootViewController];
            // Call dismissal completion handler
            if (self.dismissalCompletionHandler) {
                self.dismissalCompletionHandler();
                self.dismissalCompletionHandler = nil; // Clear after calling
            }
        }];
    } else {
        [[self.view navigationController] dismissViewControllerAnimated:YES
                                                             completion:^{
            // Call dismissal completion handler
            if (self.dismissalCompletionHandler) {
                self.dismissalCompletionHandler();
                self.dismissalCompletionHandler = nil; // Clear after calling
            }
        }];
    }
}

- (void)goToMap {
    [self.mapRouter pushFromNavigationController:[self.view navigationController]
                                        fromMenu:YES
                                    fromCheckout:NO
                                     fromAccount:NO
                                    fromDeeplink:NO
                                        animated:YES];
}

- (void)goToStoreAuthenticationWithStockLocation:(StockLocation *)stockLocation finishBlok:(void(^)(void))finishBlock {
    StoreAuthenticationViewController *vc = [StoreAuthenticationViewController createInstance];
    vc.targetStockLocation = stockLocation;
    vc.finishBlock = finishBlock;
    vc.source = self.screenName;
    UINavigationController *navController = [[UINavigationController alloc] initWithRootViewController:vc];
    [[App topViewController] presentViewController:navController animated:YES completion:nil];
}

@end
