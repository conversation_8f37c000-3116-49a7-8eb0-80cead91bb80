import UIKit
import SnapKit

class RecipeListViewController: BaseViewController {
    private let collectionView: UICollectionView
    private var recipes: [Recipe] = []
    private let inspirationsService = InspirationsServices()
    private let loadingIndicator = UIActivityIndicatorView(style: .large)
    
    init() {
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .vertical
        layout.sectionInset = UIEdgeInsets(top: 16, left: 16, bottom: 16, right: 16)
        layout.minimumLineSpacing = 16
        layout.minimumInteritemSpacing = 12
        layout.itemSize = CGSize(width: RecipeCell.width, height: RecipeCell.height)
        layout.headerReferenceSize = CGSize(width: UIScreen.main.bounds.width, height: 200)
        self.collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupCollectionView()
        setupLoadingIndicator()
        loadRecipes()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        navigationController?.setNavigationBarHidden(true, animated: animated)
    }
    
    private func setupCollectionView() {
        collectionView.translatesAutoresizingMaskIntoConstraints = false
        collectionView.backgroundColor = .hf_white()
        collectionView.dataSource = self
        collectionView.delegate = self
        collectionView.contentInsetAdjustmentBehavior = .never
        collectionView.bounces = false
        collectionView.contentInset.bottom = tabBarController?.tabBar.frame.height ?? 0
        collectionView.register(RecipeCell.self, forCellWithReuseIdentifier: RecipeCell.reuseIdentifier)
        collectionView.register(RecipeListHeaderView.self, forSupplementaryViewOfKind: UICollectionView.elementKindSectionHeader, withReuseIdentifier: RecipeListHeaderView.reuseIdentifier)
        
        view.addSubview(collectionView)
        collectionView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    private func setupLoadingIndicator() {
        loadingIndicator.color = .gray
        loadingIndicator.hidesWhenStopped = true
        view.addSubview(loadingIndicator)

        loadingIndicator.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }
    }

    private func loadRecipes() {
        loadingIndicator.startAnimating()

        inspirationsService.getRecipePreview(success: { [weak self] recipes in
            guard let self = self else { return }
            self.loadingIndicator.stopAnimating()
            self.recipes = recipes
            self.collectionView.reloadData()
        }, failure: { operation, error in
            Popup.showErrorMessage(fromResponse: operation)
        })
    }
    
    private func openRecipeDetail(with recipe: Recipe, position: Int) {
        guard let recipeID = recipe.recipeID else { return }

        let recipeDetailVC = RecipeDetailViewController(recipeID: recipeID)
        recipeDetailVC.hidesBottomBarWhenPushed = true
        navigationController?.pushViewController(recipeDetailVC, animated: true)

//        print("jilytest123 🎬 Opening TikTok Reels from recipe tap")
//        let vc = VideoReelsViewController()
//        vc.hidesBottomBarWhenPushed = true
//        navigationController?.pushViewController(vc, animated: true)
    }
}

extension RecipeListViewController: UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return recipes.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        guard let cell = collectionView.dequeueReusableCell(withReuseIdentifier: RecipeCell.reuseIdentifier, for: indexPath) as? RecipeCell else { return UICollectionViewCell() }
        let recipe = recipes[indexPath.item]
        cell.configure(with: recipe)
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        let recipe = recipes[indexPath.item]
        openRecipeDetail(with: recipe, position: indexPath.item)
    }
    
    func collectionView(_ collectionView: UICollectionView, viewForSupplementaryElementOfKind kind: String, at indexPath: IndexPath) -> UICollectionReusableView {
        if kind == UICollectionView.elementKindSectionHeader {
            guard let header = collectionView.dequeueReusableSupplementaryView(ofKind: kind, withReuseIdentifier: RecipeListHeaderView.reuseIdentifier, for: indexPath) as? RecipeListHeaderView else { return UICollectionReusableView() }
            return header
        }
        return UICollectionReusableView()
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, referenceSizeForHeaderInSection section: Int) -> CGSize {
        return CGSize(width: collectionView.bounds.width, height: 200)
    }
}

class RecipeCell: UICollectionViewCell {
    static let reuseIdentifier = "RecipeCell"
    static let width = (UIScreen.main.bounds.width - 48) / 2
    static let height = width * 1.7
    
    private let imageView = UIImageView()
    private let titleLabel = UILabel()
    private let subtitleLabel = UILabel()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.layer.cornerRadius = 8
        contentView.addSubview(imageView)
        
        titleLabel.font = .hf_tiny_bold()
        titleLabel.textColor = .hf_grey_100()
        titleLabel.numberOfLines = 1
        contentView.addSubview(titleLabel)
        
        subtitleLabel.font = .hf_micro()
        subtitleLabel.textColor = .hf_grey_80()
        contentView.addSubview(subtitleLabel)

        imageView.image = UIImage.add_imageNamed("hf_img_product_placeholder_600px")
        imageView.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.left.equalToSuperview().inset(8)
            make.right.equalToSuperview().inset(8)
            make.height.equalTo(RecipeCell.height - 40) // 40 is title label height + subtitle label height + padding 8
        }
        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(imageView.snp.bottom).offset(8)
            make.left.equalToSuperview().inset(8)
            make.right.equalToSuperview().inset(8)
        }
        subtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom)
            make.left.equalToSuperview().inset(8)
            make.right.equalToSuperview().inset(8)
            make.bottom.equalToSuperview()
        }
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func configure(with recipe: Recipe) {
        if let thumbnailStringUrl = recipe.photoThumbURL,
           let url = URL(string: thumbnailStringUrl) {
            let placeHolder = UIImage.add_imageNamed("hf_img_product_placeholder_600px")
            imageView.add_setImage(with: url, placeholderImage: placeHolder)
        }
        titleLabel.text = recipe.name
        if let displayTimeToCook = recipe.displayTimeToCook,
           let displayDifficulty = recipe.displayDifficulty {
            subtitleLabel.text = "\(displayTimeToCook) • \(displayDifficulty)"
        }
    }
}
