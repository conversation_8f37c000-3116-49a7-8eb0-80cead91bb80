//
//  VideoManager.swift
//  HappyFresh
//
//  Created by Augment Agent on 2025-07-07.
//

import Foundation
import AVFoundation
import UIKit

enum VideoState {
    case loading
    case ready
    case failed
}

class VideoManager {
    static let shared = VideoManager()
    
    private var playerCache: NSCache<NSString, AVPlayer> = NSCache<NSString, AVPlayer>()
    private var playerKeys = Set<NSString>()
    private var currentlyPlayingPlayer: AVPlayer?
    private var preloadQueue: DispatchQueue
    
    private init() {
        // Configure caches for aggressive preloading
        playerCache.countLimit = 10 // Increase cache limit

        // Create high-priority queue for preloading
        preloadQueue = DispatchQueue(label: "com.happyfresh.video.preload", qos: .userInitiated)

        // Setup audio session for video playback
        setupAudioSession()

        // Listen for app lifecycle events
        setupNotifications()

        // Start preloading videos immediately
        startGlobalVideoPreloading()
    }

    private func startGlobalVideoPreloading() {
        // Preload first 3 videos immediately when VideoManager is initialized
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) { [weak self] in
            self?.preloadInitialVideosGlobally()
        }
    }

    private func preloadInitialVideosGlobally() {
        // Get sample video URLs and preload them
        let sampleReels = VideoReel.sampleReels
        let firstThreeURLs = Array(sampleReels.prefix(3)).map { $0.videoURL }

        print("🌍 Global preloading started for \(firstThreeURLs.count) videos")
        preloadVideos(urls: firstThreeURLs)
    }
    
    // MARK: - Public Methods
    
    func getPlayer(for videoURL: URL) -> AVPlayer {
        let cacheKey = videoURL.absoluteString as NSString

        if let cachedPlayer = playerCache.object(forKey: cacheKey) {
            // Reset to beginning for consistent playback
            cachedPlayer.seek(to: .zero, toleranceBefore: .zero, toleranceAfter: .zero)
            return cachedPlayer
        }

        // Create optimized player item for instant playback
        let playerItem = createOptimizedPlayerItem(for: videoURL)
        let player = AVPlayer(playerItem: playerItem)

        // Configure player for instant playback
        configurePlayerForInstantPlayback(player)

        // Cache the player
        playerCache.setObject(player, forKey: cacheKey)
        playerKeys.insert(cacheKey)

        return player
    }

    private func createOptimizedPlayerItem(for url: URL) -> AVPlayerItem {
        let playerItem = AVPlayerItem(url: url)

        // Optimize for instant playback
        playerItem.preferredForwardBufferDuration = 0.5 // Minimal buffer
        playerItem.canUseNetworkResourcesForLiveStreamingWhilePaused = false

        // Preload media data
        playerItem.preferredPeakBitRate = 1000000 // 1 Mbps for faster loading

        return playerItem
    }

    private func configurePlayerForInstantPlayback(_ player: AVPlayer) {
        // Disable automatic waiting to minimize stalling
        player.automaticallyWaitsToMinimizeStalling = false

        // Set volume
        player.volume = 0.8

        // Enable faster seeking
        player.actionAtItemEnd = .pause

        // Optimize for low latency
        if #available(iOS 15.0, *) {
            player.audiovisualBackgroundPlaybackPolicy = .pauses
        }
    }
    
    func preloadVideo(url: URL) {
        preloadQueue.async { [weak self] in
            guard let self = self else { return }

            // Create and prepare player for instant playback
            let player = self.getPlayer(for: url)

            // Force immediate preparation
            self.preparePlayerForInstantPlayback(player)
        }
    }

    func preloadVideos(urls: [URL]) {
        // Increase cache limit for better preloading
        playerCache.countLimit = max(10, urls.count + 2)

        // Preload synchronously on main queue for first video, async for others
        if let firstURL = urls.first {
            let firstPlayer = getPlayer(for: firstURL)
            preparePlayerForInstantPlayback(firstPlayer)
        }

        // Preload remaining videos asynchronously
        let remainingURLs = Array(urls.dropFirst())
        preloadQueue.async { [weak self] in
            guard let self = self else { return }

            for url in remainingURLs {
                let player = self.getPlayer(for: url)
                self.preparePlayerForInstantPlayback(player)
            }
        }
    }

    private func preparePlayerForInstantPlayback(_ player: AVPlayer) {
        guard let playerItem = player.currentItem else { return }

        // Force loading of media data
        playerItem.asset.loadValuesAsynchronously(forKeys: ["status", "duration", "tracks"]) {
            DispatchQueue.main.async {
                // Seek to beginning to trigger buffering
                player.seek(to: .zero, toleranceBefore: .zero, toleranceAfter: .zero)

                // Preroll for instant playback
                player.preroll(atRate: 1.0) { success in
                    if success {
                        print("✅ Video prerolled successfully")
                    }
                }
            }
        }
    }
    
    func playVideo(player: AVPlayer) {
        // Pause any currently playing video
        pauseCurrentVideo()

        // Set as current player
        currentlyPlayingPlayer = player

        // Ensure player is ready and play immediately
        if player.status == .readyToPlay {
            // Player is ready, play immediately
            player.play()
            print("🎬 Playing video immediately (ready)")
        } else {
            // Player not ready, force preparation and play
            print("🎬 Preparing player for immediate playback")

            // Seek to beginning with zero tolerance for instant response
            player.seek(to: .zero, toleranceBefore: .zero, toleranceAfter: .zero) { [weak self] completed in
                if completed {
                    // Preroll and play
                    player.preroll(atRate: 1.0) { success in
                        DispatchQueue.main.async {
                            player.play()
                            print("🎬 Playing video after preroll: \(success)")
                        }
                    }
                }
            }
        }
    }
    
    func pauseCurrentVideo() {
        currentlyPlayingPlayer?.pause()
    }
    
    func pauseAllVideos() {
        // Pause all cached players
        for key in playerKeys {
            if let player = playerCache.object(forKey: key) {
                player.pause()
            }
        }
        currentlyPlayingPlayer = nil
    }
    
    func seekToBeginning(player: AVPlayer) {
        player.seek(to: .zero)
    }
    
    func clearCache() {
        pauseAllVideos()
        playerCache.removeAllObjects()
        playerKeys.removeAll()
    }

    func getVideoState(for player: AVPlayer) -> VideoState {
        guard let playerItem = player.currentItem else { return .loading }

        switch playerItem.status {
        case .readyToPlay:
            return .ready
        case .failed:
            return .failed
        case .unknown:
            return .loading
        @unknown default:
            return .loading
        }
    }
    
    // MARK: - Private Methods
    

    
    private func setupAudioSession() {
        do {
            try AVAudioSession.sharedInstance().setCategory(.playback, mode: .default, options: [.mixWithOthers])
            try AVAudioSession.sharedInstance().setActive(true)
        } catch {
            print("Failed to setup audio session: \(error)")
        }
    }
    
    private func setupNotifications() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appDidEnterBackground),
            name: UIApplication.didEnterBackgroundNotification,
            object: nil
        )
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appWillEnterForeground),
            name: UIApplication.willEnterForegroundNotification,
            object: nil
        )
    }
    
    @objc private func appDidEnterBackground() {
        pauseAllVideos()
    }
    
    @objc private func appWillEnterForeground() {
        // Videos will be resumed when cells become visible again
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
}

// MARK: - Video State Management
extension VideoManager {
    enum VideoState {
        case loading
        case ready
        case playing
        case paused
        case failed
    }
    
//    func getVideoState(for player: AVPlayer) -> VideoState {
//        guard let playerItem = player.currentItem else {
//            return .loading
//        }
//        
//        switch playerItem.status {
//        case .readyToPlay:
//            return player.rate > 0 ? .playing : .ready
//        case .failed:
//            return .failed
//        case .unknown:
//            return .loading
//        @unknown default:
//            return .loading
//        }
//    }
}
