//
//  VideoManager.swift
//  HappyFresh
//
//  Created by Augment Agent on 2025-07-07.
//

import Foundation
import AVFoundation
import UIKit

enum VideoState {
    case loading
    case ready
    case failed
}

class VideoManager: NSObject {
    static let shared = VideoManager()
    
    public var currentlyPlayingPlayer: AVPlayer?
    
    private var playerCache: NSCache<NSString, AVPlayer> = NSCache<NSString, AVPlayer>()
    private var playerKeys = Set<NSString>()
    private var preloadQueue: DispatchQueue
    private var lastPlayedURL: URL?
    
    override init() {
        // Configure caches
        playerCache.countLimit = 5 // Limit to 5 players in memory

        // Create background queue for preloading
        preloadQueue = DispatchQueue(label: "com.happyfresh.video.preload", qos: .userInitiated)
        
        super.init()

        // Setup audio session for video playback
        setupAudioSession()

        // Listen for app lifecycle events
        setupNotifications()
    }
    
    // MARK: - Public Methods
    
    func getPlayer(for videoURL: URL) -> AVPlayer {
        let cacheKey = videoURL.absoluteString as NSString

        // Check if we have a cached player
        if let cachedPlayer = playerCache.object(forKey: cacheKey) {
            // Reset the player to beginning to ensure fresh playback
            cachedPlayer.seek(to: .zero)
            return cachedPlayer
        }

        print("jilytest123 Creating new player for: \(videoURL.lastPathComponent)")

        // Always create a new AVPlayerItem to avoid reuse issues
        let playerItem = AVPlayerItem(url: videoURL)

        // Configure player item for better performance
        playerItem.preferredForwardBufferDuration = 3.0

        let player = AVPlayer(playerItem: playerItem)

        // Configure player for better performance
        player.automaticallyWaitsToMinimizeStalling = false
        player.volume = 0.8

        // Cache the player
        playerCache.setObject(player, forKey: cacheKey)
        playerKeys.insert(cacheKey)

        return player
    }
    
    func preloadVideo(url: URL) {
        preloadQueue.async { [weak self] in
            // Create player to trigger preloading
            guard let self = self else { return }
            let player = self.getPlayer(for: url)
            
            // Force buffering by setting a time observer
            if let playerItem = player.currentItem {
                // Set preferred forward buffer duration
                playerItem.preferredForwardBufferDuration = 10.0
                
                // Force preloading by seeking to beginning
                player.seek(to: .zero)
                
                // Add a buffer observer to track loading
                self.monitorBuffering(for: playerItem, url: url)
            }
        }
    }
    
    private func monitorBuffering(for playerItem: AVPlayerItem, url: URL) {
        // Monitor buffering progress
        let timeRangesKey = "loadedTimeRanges"
        playerItem.addObserver(self, forKeyPath: timeRangesKey, options: [.new], context: nil)
    }

    override func observeValue(forKeyPath keyPath: String?, of object: Any?, change: [NSKeyValueChangeKey : Any]?, context: UnsafeMutableRawPointer?) {
        if keyPath == "loadedTimeRanges",
           let playerItem = object as? AVPlayerItem,
           let timeRanges = playerItem.loadedTimeRanges as? [CMTimeRange],
           !timeRanges.isEmpty {
           
            let timeRange = timeRanges[0]
            let bufferedSeconds = CMTimeGetSeconds(timeRange.duration)
            
            // Consider preloaded when we have at least 5 seconds buffered
            if bufferedSeconds > 5.0 {
                // Remove observer to avoid memory leaks
                playerItem.removeObserver(self, forKeyPath: "loadedTimeRanges")
                print("jilytest123 Video preloaded: \(bufferedSeconds) seconds buffered")
            }
        }
    }

    func preloadVideos(urls: [URL]) {
        preloadQueue.async { [weak self] in
            for url in urls {
                // Create player to trigger preloading
                _ = self?.getPlayer(for: url)
            }
        }
    }
    
    func playVideo(player: AVPlayer) {
        // Only skip if it's the same player AND already playing
        if currentlyPlayingPlayer === player && player.rate > 0 {
            print("jilytest123 Video already playing, skipping")
            return
        }

        // Pause any currently playing video
        pauseCurrentVideo()

        // Set as current player and play
        currentlyPlayingPlayer = player

        // Ensure player is at beginning for consistent playback
        player.seek(to: .zero) { [weak self] _ in
            player.play()
            print("jilytest123 Started playing video")
        }
    }
    
    func pauseCurrentVideo() {
        currentlyPlayingPlayer?.pause()
    }
    
    func pauseAllVideos() {
        print("jilytest123 Pausing all videos")
        // Pause all cached players
        for key in playerKeys {
            if let player = playerCache.object(forKey: key) {
                player.pause()
            }
        }
        currentlyPlayingPlayer = nil
    }
    
    func seekToBeginning(player: AVPlayer) {
        player.seek(to: .zero)
    }
    
    func clearCache() {
        pauseAllVideos()
        playerCache.removeAllObjects()
        playerKeys.removeAll()
    }

    func getVideoState(for player: AVPlayer) -> VideoState {
        guard let playerItem = player.currentItem else { return .loading }

        switch playerItem.status {
        case .readyToPlay:
            return .ready
        case .failed:
            return .failed
        case .unknown:
            return .loading
        @unknown default:
            return .loading
        }
    }
    
    // MARK: - Private Methods
    

    
    private func setupAudioSession() {
        do {
            try AVAudioSession.sharedInstance().setCategory(.playback, mode: .default, options: [.mixWithOthers])
            try AVAudioSession.sharedInstance().setActive(true)
        } catch {
            print("Failed to setup audio session: \(error)")
        }
    }
    
    private func setupNotifications() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appDidEnterBackground),
            name: UIApplication.didEnterBackgroundNotification,
            object: nil
        )
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appWillEnterForeground),
            name: UIApplication.willEnterForegroundNotification,
            object: nil
        )
    }
    
    @objc private func appDidEnterBackground() {
        pauseAllVideos()
    }
    
    @objc private func appWillEnterForeground() {
        // Videos will be resumed when cells become visible again
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
}

// MARK: - Video State Management
extension VideoManager {
    enum VideoState {
        case loading
        case ready
        case playing
        case paused
        case failed
    }
}
