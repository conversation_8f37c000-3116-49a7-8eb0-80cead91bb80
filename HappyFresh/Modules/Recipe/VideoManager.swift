//
//  VideoManager.swift
//  HappyFresh
//
//  Created by Augment Agent on 2025-07-07.
//

import Foundation
import AVFoundation
import UIKit

// MARK: - Player Item Status Observer
class PlayerItemStatusObserver: NSObject {
    private let completion: (AVPlayerItem.Status) -> Void
    private var isCompleted = false

    init(completion: @escaping (AVPlayerItem.Status) -> Void) {
        self.completion = completion
        super.init()
    }

    func startObserving(playerItem: AVPlayerItem) {
        playerItem.addObserver(self, forKeyPath: "status", options: [.new], context: nil)

        // Set timeout to avoid infinite waiting
        DispatchQueue.main.asyncAfter(deadline: .now() + 5.0) { [weak self] in
            if let self = self, !self.isCompleted {
                self.isCompleted = true
                playerItem.removeObserver(self, forKeyPath: "status")
                print("⏰ Player item status observation timeout")
            }
        }
    }

    override func observeValue(forKeyPath keyPath: String?, of object: Any?, change: [NSKeyValueChangeKey : Any]?, context: UnsafeMutableRawPointer?) {
        if keyPath == "status",
           let playerItem = object as? AVPlayerItem,
           !isCompleted {

            if playerItem.status == .readyToPlay || playerItem.status == .failed {
                isCompleted = true
                playerItem.removeObserver(self, forKeyPath: "status")
                completion(playerItem.status)
            }
        }
    }
}

enum VideoState {
    case loading
    case ready
    case failed
}

class VideoManager {
    static let shared = VideoManager()
    
    private var playerCache: NSCache<NSString, AVPlayer> = NSCache<NSString, AVPlayer>()
    private var playerKeys = Set<NSString>()
    private var currentlyPlayingPlayer: AVPlayer?
    private var preloadQueue: DispatchQueue
    
    private init() {
        // Configure caches for aggressive preloading
        playerCache.countLimit = 10 // Increase cache limit

        // Create high-priority queue for preloading
        preloadQueue = DispatchQueue(label: "com.happyfresh.video.preload", qos: .userInitiated)

        // Setup audio session for video playback
        setupAudioSession()

        // Listen for app lifecycle events
        setupNotifications()

        // Start preloading videos immediately
        startGlobalVideoPreloading()
    }

    private func startGlobalVideoPreloading() {
        // Preload first 3 videos immediately when VideoManager is initialized
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) { [weak self] in
            self?.preloadInitialVideosGlobally()
        }
    }

    private func preloadInitialVideosGlobally() {
        // Get sample video URLs and preload them
        let sampleReels = VideoReel.sampleReels
        let firstThreeURLs = Array(sampleReels.prefix(3)).map { $0.videoURL }

        print("🌍 Global preloading started for \(firstThreeURLs.count) videos")
        preloadVideos(urls: firstThreeURLs)
    }
    
    // MARK: - Public Methods
    
    func getPlayer(for videoURL: URL) -> AVPlayer {
        let cacheKey = videoURL.absoluteString as NSString

        if let cachedPlayer = playerCache.object(forKey: cacheKey) {
            // Reset to beginning for consistent playback
            cachedPlayer.seek(to: .zero, toleranceBefore: .zero, toleranceAfter: .zero)
            return cachedPlayer
        }

        // Create optimized player item for instant playback
        let playerItem = createOptimizedPlayerItem(for: videoURL)
        let player = AVPlayer(playerItem: playerItem)

        // Configure player for instant playback
        configurePlayerForInstantPlayback(player)

        // Cache the player
        playerCache.setObject(player, forKey: cacheKey)
        playerKeys.insert(cacheKey)

        return player
    }

    private func createOptimizedPlayerItem(for url: URL) -> AVPlayerItem {
        let playerItem = AVPlayerItem(url: url)

        // Optimize for faster loading and playback
        playerItem.preferredForwardBufferDuration = 1.0 // Small buffer for faster start
        playerItem.canUseNetworkResourcesForLiveStreamingWhilePaused = false

        return playerItem
    }

    private func configurePlayerForInstantPlayback(_ player: AVPlayer) {
        // Configure for responsive playback
        player.automaticallyWaitsToMinimizeStalling = false
        player.volume = 0.8
        player.actionAtItemEnd = .pause

        // Optimize for low latency on supported iOS versions
        if #available(iOS 15.0, *) {
            player.audiovisualBackgroundPlaybackPolicy = .pauses
        }
    }
    
    func preloadVideo(url: URL) {
        preloadQueue.async { [weak self] in
            guard let self = self else { return }

            // Create player to trigger preloading
            let player = self.getPlayer(for: url)
            print("📱 Preloaded single video: \(url.lastPathComponent)")
        }
    }

    func preloadVideos(urls: [URL]) {
        // Increase cache limit for better preloading
        playerCache.countLimit = max(10, urls.count + 2)

        print("🚀 Preloading \(urls.count) videos")

        // Preload all videos - create players to trigger loading
        for (index, url) in urls.enumerated() {
            if index == 0 {
                // Preload first video on main queue for immediate availability
                let firstPlayer = getPlayer(for: url)
                print("📱 First video player created: \(url.lastPathComponent)")
            } else {
                // Preload remaining videos asynchronously
                preloadQueue.async { [weak self] in
                    let player = self?.getPlayer(for: url)
                    print("📱 Video player created: \(url.lastPathComponent)")
                }
            }
        }
    }

    private func preparePlayerForInstantPlayback(_ player: AVPlayer) {
        guard let playerItem = player.currentItem else { return }

        // Wait for player item to be ready before attempting preroll
        if playerItem.status == .readyToPlay {
            // Player item is already ready, prepare immediately
            performPrerollIfReady(player: player)
        } else {
            // Wait for player item to become ready
            let observer = PlayerItemStatusObserver { [weak self] status in
                if status == .readyToPlay {
                    self?.performPrerollIfReady(player: player)
                }
            }
            observer.startObserving(playerItem: playerItem)
        }
    }

    private func performPrerollIfReady(player: AVPlayer) {
        // Double-check player status before preroll
        guard player.status == .readyToPlay else {
            print("⚠️ Player not ready for preroll, status: \(player.status.rawValue)")
            return
        }

        // Seek to beginning to trigger buffering
        player.seek(to: .zero, toleranceBefore: .zero, toleranceAfter: .zero) { completed in
            if completed && player.status == .readyToPlay {
                // Only preroll if player is still ready
                player.preroll(atRate: 1.0) { success in
                    print("✅ Video prerolled successfully: \(success)")
                }
            }
        }
    }
    
    func playVideo(player: AVPlayer) {
        // Pause any currently playing video
        pauseCurrentVideo()

        // Set as current player
        currentlyPlayingPlayer = player

        // Check player item status first
        guard let playerItem = player.currentItem else {
            print("❌ No player item available")
            return
        }

        // Play based on current status
        switch playerItem.status {
        case .readyToPlay:
            // Player is ready, play immediately
            player.play()
            print("🎬 Playing video immediately (ready)")

        case .failed:
            print("❌ Player item failed: \(playerItem.error?.localizedDescription ?? "Unknown error")")

        case .unknown:
            print("🎬 Player item not ready, waiting for ready state")
            // Wait for player to become ready
            let observer = PlayerItemStatusObserver { status in
                DispatchQueue.main.async {
                    if status == .readyToPlay {
                        player.play()
                        print("🎬 Playing video after becoming ready")
                    } else {
                        print("❌ Player failed to become ready: \(status)")
                    }
                }
            }
            observer.startObserving(playerItem: playerItem)

        @unknown default:
            print("⚠️ Unknown player item status")
        }
    }
    
    func pauseCurrentVideo() {
        currentlyPlayingPlayer?.pause()
    }
    
    func pauseAllVideos() {
        // Pause all cached players
        for key in playerKeys {
            if let player = playerCache.object(forKey: key) {
                player.pause()
            }
        }
        currentlyPlayingPlayer = nil
    }
    
    func seekToBeginning(player: AVPlayer) {
        player.seek(to: .zero)
    }
    
    func clearCache() {
        pauseAllVideos()
        playerCache.removeAllObjects()
        playerKeys.removeAll()
    }

    func getVideoState(for player: AVPlayer) -> VideoState {
        guard let playerItem = player.currentItem else { return .loading }

        switch playerItem.status {
        case .readyToPlay:
            return .ready
        case .failed:
            return .failed
        case .unknown:
            return .loading
        @unknown default:
            return .loading
        }
    }
    
    // MARK: - Private Methods
    

    
    private func setupAudioSession() {
        do {
            try AVAudioSession.sharedInstance().setCategory(.playback, mode: .default, options: [.mixWithOthers])
            try AVAudioSession.sharedInstance().setActive(true)
        } catch {
            print("Failed to setup audio session: \(error)")
        }
    }
    
    private func setupNotifications() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appDidEnterBackground),
            name: UIApplication.didEnterBackgroundNotification,
            object: nil
        )
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appWillEnterForeground),
            name: UIApplication.willEnterForegroundNotification,
            object: nil
        )
    }
    
    @objc private func appDidEnterBackground() {
        pauseAllVideos()
    }
    
    @objc private func appWillEnterForeground() {
        // Videos will be resumed when cells become visible again
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
}

// MARK: - Video State Management
extension VideoManager {
    enum VideoState {
        case loading
        case ready
        case playing
        case paused
        case failed
    }
    
//    func getVideoState(for player: AVPlayer) -> VideoState {
//        guard let playerItem = player.currentItem else {
//            return .loading
//        }
//        
//        switch playerItem.status {
//        case .readyToPlay:
//            return player.rate > 0 ? .playing : .ready
//        case .failed:
//            return .failed
//        case .unknown:
//            return .loading
//        @unknown default:
//            return .loading
//        }
//    }
}
