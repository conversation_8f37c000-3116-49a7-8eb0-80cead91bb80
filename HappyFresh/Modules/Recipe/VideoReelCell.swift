//
//  VideoReelCell.swift
//  HappyFresh
//
//  Created by Augment Agent on 2025-07-07.
//

import UIKit
import AVFoundation
import SnapKit

class VideoReelCell: UICollectionViewCell {

    // MARK: - UI Components
    private let playerContainerView = UIView()
    private let overlayView = UIView()
    private let titleLabel = UILabel()
    private let descriptionLabel = UILabel()
    private let authorLabel = UILabel()
    private let cookingTimeLabel = UILabel()
    private let difficultyLabel = UILabel()
    private let categoryLabel = UILabel()
    private let likesLabel = UILabel()
    private let viewsLabel = UILabel()
    // Remove these two lines
    // public let playPauseButton = UIButton()
//    private let loadingIndicator = UIActivityIndicatorView()
    private let ingredientsStackView = UIStackView()
    private let contentStackView = UIStackView()
    private let statsStackView = UIStackView()
    private let infoStackView = UIStackView()
    private var gradientLayer: CAGradientLayer?
    
    // MARK: - Properties
    private var player: AVPlayer?
    private var playerLayer: AVPlayerLayer?
    private var videoReel: VideoReel?
    private var playerStatusObserver: NSKeyValueObservation?
    private var playerItemStatusObserver: NSKeyValueObservation?
    private var isConfigured = false
    
    // MARK: - Lifecycle
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        setupConstraints()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
        setupConstraints()
    }

    override func prepareForReuse() {
        super.prepareForReuse()
        cleanup()
    }

    deinit {
        cleanup()
    }

    override func layoutSubviews() {
        super.layoutSubviews()
        playerLayer?.frame = playerContainerView.bounds
        gradientLayer?.frame = overlayView.bounds
    }
    
    // MARK: - Configuration
    func configure(with videoReel: VideoReel) {
        self.videoReel = videoReel
        
        // Update UI with video reel data
        titleLabel.text = videoReel.title
        descriptionLabel.text = videoReel.description
        authorLabel.text = "by \(videoReel.author)"
        cookingTimeLabel.text = "⏱ \(videoReel.cookingTime)"
        difficultyLabel.text = videoReel.difficulty.rawValue
        difficultyLabel.textColor = videoReel.difficulty.color
        categoryLabel.text = "\(videoReel.category.icon) \(videoReel.category.rawValue)"
        likesLabel.text = "❤️ \(formatNumber(videoReel.likes))"
        viewsLabel.text = "👁 \(formatNumber(videoReel.views))"
        
        // Setup ingredients
        setupIngredients(videoReel.ingredients)
        
        // Setup video player
        setupVideoPlayer(with: videoReel.videoURL)
        
        isConfigured = true
    }
    
    // MARK: - Video Player Setup
    private func setupVideoPlayer(with url: URL) {
        // Clean up previous player
        cleanup()

        // Show loading indicator
//        loadingIndicator.startAnimating()
//        playPauseButton.isHidden = true

        // Get player from video manager
        player = VideoManager.shared.getPlayer(for: url)

        // Setup player layer
        setupPlayerLayer()

        // Add observers
        addPlayerObservers()
    }
    
    private func setupPlayerLayer() {
        guard let player = player else { return }
        
        // Remove existing layer
        playerLayer?.removeFromSuperlayer()
        
        // Create new player layer
        playerLayer = AVPlayerLayer(player: player)
        playerLayer?.videoGravity = .resizeAspectFill
        playerLayer?.frame = playerContainerView.bounds
        
        // Add to container
        playerContainerView.layer.insertSublayer(playerLayer!, at: 0)
    }
    
    private func addPlayerObservers() {
        guard let player = player, let playerItem = player.currentItem else { return }

        // Remove any existing observers
        removePlayerObservers()

        // Observe player item status using modern observation
        playerItemStatusObserver = playerItem.observe(\.status, options: [.new]) { [weak self] item, _ in
            DispatchQueue.main.async {
                self?.handlePlayerItemStatusChange(item.status)
            }
        }

        // Observe playback end
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(playerDidFinishPlaying),
            name: .AVPlayerItemDidPlayToEndTime,
            object: playerItem
        )
    }

    private func removePlayerObservers() {
        playerItemStatusObserver?.invalidate()
        playerItemStatusObserver = nil

        NotificationCenter.default.removeObserver(self, name: .AVPlayerItemDidPlayToEndTime, object: player?.currentItem)
    }

    private func handlePlayerItemStatusChange(_ status: AVPlayerItem.Status) {
//        switch status {
//        case .readyToPlay:
//            // Stop loading indicator
//            loadingIndicator.stopAnimating()
//            loadingIndicator.isHidden = true
//            // Auto-play when ready
//            playVideo()
//        case .failed:
//            // Could show error state here
//            loadingIndicator.stopAnimating()
//            loadingIndicator.isHidden = true
//            break
//        case .unknown:
//            loadingIndicator.startAnimating()
//            loadingIndicator.isHidden = false
//            break
//        @unknown default:
//            loadingIndicator.startAnimating()
//            loadingIndicator.isHidden = false
//            break
//        }
    }
    
    // MARK: - Player Control
    func playVideo() {
        guard let player = player else { return }
        VideoManager.shared.playVideo(player: player)
//        updatePlayPauseButton(isPlaying: true)
    }
    
    func pauseVideo() {
        player?.pause()
//        updatePlayPauseButton(isPlaying: false)
    }
    
    func seekToBeginning() {
        guard let player = player else { return }
        VideoManager.shared.seekToBeginning(player: player)
    }
    
    // MARK: - UI Setup
    private func setupUI() {
        // Add all subviews
        contentView.addSubview(playerContainerView)
        contentView.addSubview(overlayView)
        // Remove these two lines
        // overlayView.addSubview(playPauseButton)
//        overlayView.addSubview(loadingIndicator)
        overlayView.addSubview(contentStackView)

        // Setup player container
        playerContainerView.backgroundColor = .black

        // Setup overlay
        overlayView.backgroundColor = .clear
        setupOverlayGradient()

        // Setup stack views
        setupStackViews()

        // Setup labels
        setupLabels()

        // Setup play/pause button
        setupPlayPauseButton()

        // Setup loading indicator
//        setupLoadingIndicator()
    }

    private func setupStackViews() {
        // Stats stack view
        statsStackView.axis = .horizontal
        statsStackView.spacing = 12
        statsStackView.alignment = .leading
        statsStackView.addArrangedSubview(likesLabel)
        statsStackView.addArrangedSubview(viewsLabel)
        statsStackView.addArrangedSubview(UIView()) // Spacer

        // Info stack view
        infoStackView.axis = .horizontal
        infoStackView.spacing = 16
        infoStackView.alignment = .leading
        infoStackView.addArrangedSubview(cookingTimeLabel)
        infoStackView.addArrangedSubview(difficultyLabel)
        infoStackView.addArrangedSubview(categoryLabel)

        // Ingredients stack view
        ingredientsStackView.axis = .vertical
        ingredientsStackView.spacing = 2
        ingredientsStackView.alignment = .leading

        // Content stack view
        contentStackView.axis = .vertical
        contentStackView.spacing = 8
        contentStackView.alignment = .leading
        contentStackView.addArrangedSubview(statsStackView)
        contentStackView.addArrangedSubview(titleLabel)
        contentStackView.addArrangedSubview(descriptionLabel)
        contentStackView.addArrangedSubview(authorLabel)
        contentStackView.addArrangedSubview(infoStackView)
        contentStackView.addArrangedSubview(ingredientsStackView)
    }

    private func setupLabels() {
        titleLabel.font = UIFont.boldSystemFont(ofSize: 18)
        titleLabel.textColor = .white
        titleLabel.numberOfLines = 2

        descriptionLabel.font = UIFont.systemFont(ofSize: 14)
        descriptionLabel.textColor = .white
        descriptionLabel.numberOfLines = 3

        authorLabel.font = UIFont.systemFont(ofSize: 12)
        authorLabel.textColor = UIColor.white.withAlphaComponent(0.8)

        cookingTimeLabel.font = UIFont.systemFont(ofSize: 12)
        cookingTimeLabel.textColor = .white

        difficultyLabel.font = UIFont.boldSystemFont(ofSize: 12)
        difficultyLabel.layer.cornerRadius = 8
        difficultyLabel.layer.masksToBounds = true
        difficultyLabel.backgroundColor = UIColor.black.withAlphaComponent(0.3)
        difficultyLabel.textAlignment = .center

        categoryLabel.font = UIFont.systemFont(ofSize: 12)
        categoryLabel.textColor = .white

        likesLabel.font = UIFont.systemFont(ofSize: 12)
        likesLabel.textColor = .white

        viewsLabel.font = UIFont.systemFont(ofSize: 12)
        viewsLabel.textColor = .white
    }

    private func setupPlayPauseButton() {
//        playPauseButton.setImage(UIImage(systemName: "play.fill"), for: .normal)
//        playPauseButton.setImage(UIImage(systemName: "pause.fill"), for: .selected)
//        playPauseButton.tintColor = .white
//        playPauseButton.backgroundColor = UIColor.black.withAlphaComponent(0.5)
//        playPauseButton.layer.cornerRadius = 25
//        playPauseButton.addTarget(self, action: #selector(playPauseButtonTapped), for: .touchUpInside)
    }

//    private func setupLoadingIndicator() {
//        loadingIndicator.style = .large
//        loadingIndicator.color = .white
//        loadingIndicator.hidesWhenStopped = true
//    }
    
    private func setupOverlayGradient() {
        gradientLayer = CAGradientLayer()
        gradientLayer?.colors = [
            UIColor.clear.cgColor,
            UIColor.black.withAlphaComponent(0.7).cgColor
        ]
        gradientLayer?.locations = [0.0, 1.0]
        if let gradientLayer = gradientLayer {
            overlayView.layer.insertSublayer(gradientLayer, at: 0)
        }
    }

    private func setupConstraints() {
        // Player container - full screen
        playerContainerView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        // Overlay view - full screen
        overlayView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        // Remove these constraints
        // Play/pause button - center
        // playPauseButton.snp.makeConstraints { make in
        //     make.center.equalToSuperview()
        //     make.width.height.equalTo(50)
        // }
        
         //Loading indicator - center
//         loadingIndicator.snp.makeConstraints { make in
//             make.center.equalToSuperview()
//         }

        // Content stack view - bottom with padding
        contentStackView.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().inset(34)
        }

        // Stats stack view - full width
        statsStackView.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview()
        }

        // Title label - full width
        titleLabel.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview()
        }

        // Description label - full width
        descriptionLabel.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview()
        }

        // Author label - full width
        authorLabel.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview()
        }

        // Info stack view - full width
        infoStackView.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview()
        }

        // Ingredients stack view - full width
        ingredientsStackView.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview()
        }
    }
    
    private func setupIngredients(_ ingredients: [String]) {
        // Clear existing ingredient views
        ingredientsStackView.arrangedSubviews.forEach { $0.removeFromSuperview() }
        
        // Add first 3 ingredients
        let displayIngredients = Array(ingredients.prefix(3))
        for ingredient in displayIngredients {
            let label = UILabel()
            label.text = "• \(ingredient)"
            label.font = UIFont.systemFont(ofSize: 11)
            label.textColor = UIColor.white.withAlphaComponent(0.9)
            ingredientsStackView.addArrangedSubview(label)
        }
        
        // Add "and more" if there are more ingredients
        if ingredients.count > 3 {
            let moreLabel = UILabel()
            moreLabel.text = "• and \(ingredients.count - 3) more..."
            moreLabel.font = UIFont.systemFont(ofSize: 11)
            moreLabel.textColor = UIColor.white.withAlphaComponent(0.7)
            ingredientsStackView.addArrangedSubview(moreLabel)
        }
    }
    
    // MARK: - Helper Methods
    private func formatNumber(_ number: Int) -> String {
        if number >= 1000 {
            return String(format: "%.1fK", Double(number) / 1000.0)
        }
        return "\(number)"
    }
    
    // Remove this method
    // private func updatePlayPauseButton(isPlaying: Bool) {
    //     playPauseButton.isSelected = isPlaying
    //     playPauseButton.isHidden = false
    // }
    
    private func cleanup() {
        removePlayerObservers()
        player?.pause()
        playerLayer?.removeFromSuperlayer()
        player = nil
        playerLayer = nil
        videoReel = nil
        isConfigured = false
//        loadingIndicator.stopAnimating()
    }
    
    // MARK: - Actions
    // Remove this method
    // @objc private func playPauseButtonTapped() {
    //     guard let player = player else { return }
    //     
    //     if player.rate > 0 {
    //         pauseVideo()
    //     } else {
    //         playVideo()
    //     }
    // }
    
    @objc private func playerDidFinishPlaying() {
        seekToBeginning()
        playVideo() // Auto-replay
    }
    

}
