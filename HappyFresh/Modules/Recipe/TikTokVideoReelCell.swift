//
//  TikTokVideoReelCell.swift
//  HappyFresh
//
//  Created by Augment Agent on 2025-07-17.
//

import UIKit
import VersaPlayer
import SnapKit

class TikTokVideoReelCell: UICollectionViewCell {
    
    // MARK: - UI Components
    private let playerContainerView = UIView()
    private let overlayView = UIView()
    private let loadingIndicator = UIActivityIndicatorView(style: .large)
    
    // Video info overlay
    private let videoInfoContainer = UIView()
    private let titleLabel = UILabel()
    private let descriptionLabel = UILabel()
    private let authorLabel = UILabel()
    private let statsContainer = UIView()
    private let likesLabel = UILabel()
    private let viewsLabel = UILabel()
    private let cookingTimeLabel = UILabel()
    private let difficultyLabel = UILabel()
    private let categoryLabel = UILabel()
    
    // Ingredients section
    private let ingredientsContainer = UIView()
    private let ingredientsLabel = UILabel()
    private let ingredientsCollectionView: UICollectionView
    
    // MARK: - Properties
    private var versaPlayer: VersaPlayer?
    private var videoReel: VideoReel?
    private var isConfigured = false
    
    // MARK: - Initialization
    override init(frame: CGRect) {
        // Setup ingredients collection view
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .horizontal
        layout.minimumInteritemSpacing = 8
        layout.minimumLineSpacing = 8
        ingredientsCollectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        
        super.init(frame: frame)
        setupUI()
        setupConstraints()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func prepareForReuse() {
        super.prepareForReuse()
        cleanup()
    }
    
    // MARK: - Setup
    private func setupUI() {
        backgroundColor = .black
        
        // Setup player container
        playerContainerView.backgroundColor = .black
        playerContainerView.clipsToBounds = true
        
        // Setup overlay
        overlayView.backgroundColor = UIColor.black.withAlphaComponent(0.3)
        
        // Setup loading indicator
        loadingIndicator.color = .white
        loadingIndicator.hidesWhenStopped = true
        
        // Setup video info labels
        setupVideoInfoLabels()
        
        // Setup ingredients collection view
        setupIngredientsCollectionView()
        
        // Add subviews
        addSubview(playerContainerView)
        addSubview(overlayView)
        addSubview(loadingIndicator)
        addSubview(videoInfoContainer)
        addSubview(ingredientsContainer)
        
        // Add video info subviews
        videoInfoContainer.addSubview(titleLabel)
        videoInfoContainer.addSubview(descriptionLabel)
        videoInfoContainer.addSubview(authorLabel)
        videoInfoContainer.addSubview(statsContainer)
        videoInfoContainer.addSubview(cookingTimeLabel)
        videoInfoContainer.addSubview(difficultyLabel)
        videoInfoContainer.addSubview(categoryLabel)
        
        // Add stats subviews
        statsContainer.addSubview(likesLabel)
        statsContainer.addSubview(viewsLabel)
        
        // Add ingredients subviews
        ingredientsContainer.addSubview(ingredientsLabel)
        ingredientsContainer.addSubview(ingredientsCollectionView)
    }
    
    private func setupVideoInfoLabels() {
        titleLabel.font = UIFont.boldSystemFont(ofSize: 18)
        titleLabel.textColor = .white
        titleLabel.numberOfLines = 2
        
        descriptionLabel.font = UIFont.systemFont(ofSize: 14)
        descriptionLabel.textColor = .white
        descriptionLabel.numberOfLines = 3
        
        authorLabel.font = UIFont.systemFont(ofSize: 12)
        authorLabel.textColor = UIColor.white.withAlphaComponent(0.8)
        
        likesLabel.font = UIFont.systemFont(ofSize: 12)
        likesLabel.textColor = .white
        
        viewsLabel.font = UIFont.systemFont(ofSize: 12)
        viewsLabel.textColor = .white
        
        cookingTimeLabel.font = UIFont.systemFont(ofSize: 12)
        cookingTimeLabel.textColor = .white
        cookingTimeLabel.backgroundColor = UIColor.black.withAlphaComponent(0.5)
        cookingTimeLabel.layer.cornerRadius = 8
        cookingTimeLabel.clipsToBounds = true
        cookingTimeLabel.textAlignment = .center
        
        difficultyLabel.font = UIFont.boldSystemFont(ofSize: 12)
        difficultyLabel.backgroundColor = UIColor.black.withAlphaComponent(0.5)
        difficultyLabel.layer.cornerRadius = 8
        difficultyLabel.clipsToBounds = true
        difficultyLabel.textAlignment = .center
        
        categoryLabel.font = UIFont.systemFont(ofSize: 12)
        categoryLabel.textColor = .white
        categoryLabel.backgroundColor = UIColor.black.withAlphaComponent(0.5)
        categoryLabel.layer.cornerRadius = 8
        categoryLabel.clipsToBounds = true
        categoryLabel.textAlignment = .center
    }
    
    private func setupIngredientsCollectionView() {
        ingredientsLabel.text = "Ingredients"
        ingredientsLabel.font = UIFont.boldSystemFont(ofSize: 14)
        ingredientsLabel.textColor = .white
        
        ingredientsCollectionView.backgroundColor = .clear
        ingredientsCollectionView.showsHorizontalScrollIndicator = false
        ingredientsCollectionView.register(CartItemProductView.self, forCellWithReuseIdentifier: "IngredientCell")
        ingredientsCollectionView.dataSource = self
        ingredientsCollectionView.delegate = self
    }
    
    private func setupConstraints() {
        // Player container fills entire cell
        playerContainerView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // Overlay covers player
        overlayView.snp.makeConstraints { make in
            make.edges.equalTo(playerContainerView)
        }
        
        // Loading indicator centered
        loadingIndicator.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }
        
        // Video info container at bottom left
        videoInfoContainer.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(16)
            make.bottom.equalTo(ingredientsContainer.snp.top).offset(-16)
            make.trailing.equalToSuperview().offset(-80) // Leave space for right side actions
        }
        
        // Ingredients container at bottom
        ingredientsContainer.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview().inset(16)
            make.bottom.equalTo(safeAreaLayoutGuide.snp.bottom).offset(-16)
            make.height.equalTo(80)
        }
        
        setupVideoInfoConstraints()
        setupIngredientsConstraints()
    }
    
    private func setupVideoInfoConstraints() {
        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview()
        }
        
        descriptionLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(4)
            make.leading.trailing.equalToSuperview()
        }
        
        authorLabel.snp.makeConstraints { make in
            make.top.equalTo(descriptionLabel.snp.bottom).offset(4)
            make.leading.trailing.equalToSuperview()
        }
        
        statsContainer.snp.makeConstraints { make in
            make.top.equalTo(authorLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview()
            make.height.equalTo(20)
        }
        
        likesLabel.snp.makeConstraints { make in
            make.leading.centerY.equalToSuperview()
        }
        
        viewsLabel.snp.makeConstraints { make in
            make.leading.equalTo(likesLabel.snp.trailing).offset(16)
            make.centerY.equalToSuperview()
        }
        
        let tagContainer = UIStackView(arrangedSubviews: [cookingTimeLabel, difficultyLabel, categoryLabel])
        tagContainer.axis = .horizontal
        tagContainer.spacing = 8
        tagContainer.distribution = .fillProportionally
        
        videoInfoContainer.addSubview(tagContainer)
        tagContainer.snp.makeConstraints { make in
            make.top.equalTo(statsContainer.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview()
            make.bottom.equalToSuperview()
            make.height.equalTo(24)
        }
    }
    
    private func setupIngredientsConstraints() {
        ingredientsLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview()
            make.height.equalTo(20)
        }
        
        ingredientsCollectionView.snp.makeConstraints { make in
            make.top.equalTo(ingredientsLabel.snp.bottom).offset(4)
            make.leading.trailing.bottom.equalToSuperview()
        }
    }
    
    // MARK: - Configuration
    func configure(with videoReel: VideoReel) {
        print("jilytest123 🔧 Configuring TikTok cell with video: \(videoReel.title)")
        self.videoReel = videoReel
        
        // Update UI with video reel data
        titleLabel.text = videoReel.title
        descriptionLabel.text = videoReel.description
        authorLabel.text = "by \(videoReel.author)"
        cookingTimeLabel.text = " ⏱ \(videoReel.cookingTime) "
        difficultyLabel.text = " \(videoReel.difficulty.rawValue) "
        difficultyLabel.textColor = videoReel.difficulty.color
        categoryLabel.text = " \(videoReel.category.icon) \(videoReel.category.rawValue) "
        likesLabel.text = "❤️ \(formatNumber(videoReel.likes))"
        viewsLabel.text = "👁 \(formatNumber(videoReel.views))"
        
        // Reload ingredients collection view
        ingredientsCollectionView.reloadData()
        
        // Setup video player
        setupVideoPlayer(with: videoReel.videoURL)
        
        isConfigured = true
        print("jilytest123 ✅ TikTok cell configuration complete for: \(videoReel.title)")
    }
    
    // MARK: - Video Player Setup
    private func setupVideoPlayer(with url: URL) {
        print("jilytest123 🎥 Setting up TikTok video player for: \(url.lastPathComponent)")
        
        // Clean up previous player
        cleanup()
        
        // Show loading indicator
        loadingIndicator.startAnimating()
        
        // Get VersaPlayer from TikTok manager
        versaPlayer = TikTokVideoManager.shared.getPlayer(for: url)
        
        guard let player = versaPlayer else {
            print("jilytest123 ❌ Failed to get VersaPlayer for: \(url.lastPathComponent)")
            return
        }
        
        // Add player view to container
        playerContainerView.addSubview(player.view)
        player.view.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // Setup player callbacks
        setupPlayerCallbacks(player)
        
        // Hide loading indicator
        loadingIndicator.stopAnimating()
        
        print("jilytest123 ✅ TikTok video player setup complete for: \(url.lastPathComponent)")
    }
    
    private func setupPlayerCallbacks(_ player: VersaPlayer) {
        // Handle player ready state
        player.ready = { [weak self] in
            print("jilytest123 ✅ VersaPlayer ready")
            DispatchQueue.main.async {
                self?.loadingIndicator.stopAnimating()
            }
        }
        
        // Handle playback start
        player.play = { [weak self] in
            print("jilytest123 ▶️ VersaPlayer started playing")
        }
        
        // Handle playback pause
        player.pause = { [weak self] in
            print("jilytest123 ⏸️ VersaPlayer paused")
        }
        
        // Handle playback end
        player.endTime = { [weak self] in
            print("jilytest123 🔄 VersaPlayer reached end, will loop")
        }
        
        // Handle errors
        player.error = { [weak self] error in
            print("jilytest123 ❌ VersaPlayer error: \(error)")
            DispatchQueue.main.async {
                self?.loadingIndicator.stopAnimating()
            }
        }
    }

    // MARK: - Player Control
    func playVideo() {
        guard let player = versaPlayer else {
            print("jilytest123 ❌ No VersaPlayer available for video playback")
            return
        }

        print("jilytest123 🎬 TikTok cell requesting video playback")
        TikTokVideoManager.shared.playVideo(player: player)
    }

    func pauseVideo() {
        guard let player = versaPlayer else { return }
        print("jilytest123 ⏸️ TikTok cell pausing video")
        player.pause()
    }

    func seekToBeginning() {
        guard let player = versaPlayer else { return }
        print("jilytest123 ⏪ TikTok cell seeking to beginning")
        TikTokVideoManager.shared.seekToBeginning(player: player)
    }

    // MARK: - Utility Methods
    private func formatNumber(_ number: Int) -> String {
        if number >= 1000000 {
            return String(format: "%.1fM", Double(number) / 1000000.0)
        } else if number >= 1000 {
            return String(format: "%.1fK", Double(number) / 1000.0)
        } else {
            return "\(number)"
        }
    }

    private func cleanup() {
        print("jilytest123 🧹 Cleaning up TikTok cell")
        versaPlayer?.pause()
        versaPlayer?.view.removeFromSuperview()
        versaPlayer = nil
        videoReel = nil
        isConfigured = false
        loadingIndicator.stopAnimating()
    }
}

// MARK: - UICollectionViewDataSource (Ingredients)
extension TikTokVideoReelCell: UICollectionViewDataSource {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return videoReel?.ingredients.count ?? 0
    }

    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "IngredientCell", for: indexPath) as! CartItemProductView

        if let ingredient = videoReel?.ingredients[indexPath.item] {
            // Configure the CartItemProductView for ingredient display
            cell.titleLabel.text = ingredient
            cell.imageView.image = UIImage(systemName: "leaf.fill")
            cell.imageView.tintColor = .systemGreen
            cell.priceLabel.isHidden = true
            cell.unitPriceLabel.isHidden = true
            cell.actionsContainerView.isHidden = true
            cell.discountStackView.isHidden = true
            cell.shopperNotesButtonStackView.isHidden = true
            cell.backgroundColor = UIColor.black.withAlphaComponent(0.3)
            cell.layer.cornerRadius = 8
        }

        return cell
    }
}

// MARK: - UICollectionViewDelegateFlowLayout (Ingredients)
extension TikTokVideoReelCell: UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        return CGSize(width: 60, height: 56) // Compact ingredient cell size
    }
}
