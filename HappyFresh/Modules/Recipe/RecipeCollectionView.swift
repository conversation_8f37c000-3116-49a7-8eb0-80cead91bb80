//
//  RecipeCollectionView.swift
//  HappyFresh
//
//  Created by <PERSON> on 7/8/20.
//  Copyright © 2020 HappyFresh Inc. All rights reserved.
//

import Foundation

class RecipeCollectionView: UICollectionView {
    
    var viewModels: [RecipeCellViewModel]? {
        didSet {
            reloadData()
            cellHeights.removeAll()
            calculateCellHeights()
        }
    }
    var recipeDidSelect:((RecipeCellViewModel) -> Void)?
    var cellHeights: [CGFloat] = []
    var height: CGFloat {
        var height: CGFloat = 0
        if cellHeights.count > 0 {
            if cellHeights.count <= 2 {
                height = cellHeights[0]
            } else {
                height = cellHeights[0] + cellHeights[1]
            }
        }
        return height
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        commonInit()
    }
    
    func commonInit() {
        register(OldRecipeCell.self, forCellWithReuseIdentifier: OldRecipeCell.reuseIdentifier())
        showsHorizontalScrollIndicator = false
        backgroundColor = UIColor.hf_asset_transparent()
        dataSource = self
        delegate = self
        
        let flowLayout = UICollectionViewFlowLayout()
        flowLayout.minimumInteritemSpacing = OldRecipeCell.itemSpacing
        flowLayout.minimumLineSpacing = OldRecipeCell.itemSpacing
        flowLayout.scrollDirection = .horizontal
        flowLayout.sectionInset = UIEdgeInsets(top: 0, left: 16, bottom: 8, right: 16)
        collectionViewLayout = flowLayout
        
        accessibilityIdentifier = AccessibilityIdentifier.RecipeCollection
    }
    
    func calculateCellHeights() {
        guard let viewModels = viewModels else { return }
        for viewModel in viewModels {
            var cellHeight: CGFloat = OldRecipeCell.minHeight
            let titleLabelHeight = viewModel.title.height(withConstrainedWidth: OldRecipeCell.width - 24, font: UIFont.hf_title_7())
            if titleLabelHeight > 30 {
                cellHeight = OldRecipeCell.maxHeight
            }
            cellHeights.append(cellHeight)
        }
        if viewModels.count > 2 {
            let firstRow = stride(from: 0, to: cellHeights.count, by: 2).map { cellHeights[$0] }
            let secondRow = stride(from: 1, to: cellHeights.count, by: 2).map { cellHeights[$0] }
            let firstRowMaxHeight = firstRow.max() ?? OldRecipeCell.maxHeight
            let secondRowMaxHeight = secondRow.max() ?? OldRecipeCell.maxHeight
            cellHeights.removeAll()
            var index = 0
            for _ in viewModels {
                if index % 2 == 0 {
                    cellHeights.insert(firstRowMaxHeight, at: index)
                } else {
                    cellHeights.insert(secondRowMaxHeight, at: index)
                }
                index+=1
            }
        } else {
            let maxRowHeight = cellHeights.max() ?? OldRecipeCell.maxHeight
            cellHeights.removeAll()
            for _ in viewModels {
                cellHeights.append(maxRowHeight)
            }
        }
    }
}

extension RecipeCollectionView: UICollectionViewDataSource {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return viewModels?.count ?? 0
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        if let cell = collectionView.dequeueReusableCell(withReuseIdentifier: OldRecipeCell.reuseIdentifier(), for: indexPath) as? OldRecipeCell {
            if let viewModels = viewModels {
                cell.configure(with: viewModels[indexPath.row])
            }
            return cell
        } else {
            return OldRecipeCell()
        }
    }
}

extension RecipeCollectionView: UICollectionViewDelegate {
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        if let recipeDidSelect = recipeDidSelect, let viewModels = viewModels {
            recipeDidSelect(viewModels[indexPath.row])
        }
    }
}

extension RecipeCollectionView: UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        return CGSize(width: OldRecipeCell.width, height: cellHeights[indexPath.row])
    }
}
