//
//  RecipeIngredientShowcaseViewModel.swift
//  HappyFresh
//
//  Created by Augment Agent on 2025-07-25.
//

import Foundation
import ReactiveSwift

class RecipeIngredientShowcaseViewModel: ProductShowcaseViewModel {
    
    // MARK: - Properties
    private let recipeIngredient: RecipeIngredient
    private let stockLocation: StockLocation
    private let inspirationsService: InspirationsServices
    
    // MARK: - ProductShowcaseViewModel Protocol
    var errorDescription: MutableProperty<String>?
    var title: MutableProperty<String>
    var subtitle: MutableProperty<String>?
    var stockItems: MutableProperty<[StockItem]>
    var stockItemCount: Int {
        return stockItems.value.count
    }
    var recommendationType: MutableProperty<String>
    var screenType: MutableProperty<String>
    var showSeeAllButton: Bool
    var showLoadingIndicator: Bool = false // No loading since we already have the data
    
    var oosPositions: [Int] {
        return stockItems.value
            .filter { !($0.inStock?.boolValue ?? true) }
            .map { stockItems.value.firstIndex(of: $0) ?? -1 }
    }
    
    // MARK: - Initialization
    init(recipeIngredient: RecipeIngredient, stockLocation: StockLocation) {
        self.recipeIngredient = recipeIngredient
        self.stockLocation = stockLocation
        self.inspirationsService = InspirationsServices()
        
        // Initialize properties
        self.errorDescription = MutableProperty("")
        self.title = MutableProperty(recipeIngredient.name)
        self.subtitle = MutableProperty("")
        self.stockItems = MutableProperty([])
        self.recommendationType = MutableProperty("recipe_ingredient")
        self.screenType = MutableProperty("recipe_ingredient")
        self.showSeeAllButton = false
        
        // Convert products to stock items immediately since we already have the data
        convertProductsToStockItems()
    }
    
    // MARK: - ProductShowcaseViewModel Protocol Method
    func fetchProduct() {
        // No need to fetch since we already have the data from the API call
        // The stock items are already converted in the initializer
    }
    
    // MARK: - Private Methods
    private func convertProductsToStockItems() {
        let products = recipeIngredient.products
        
        guard !products.isEmpty else {
            stockItems.value = []
            return
        }
        
        // Convert products to stock items using the same pattern as getSimilarProductsWithID
        let predicate = NSPredicate(format: "(variant.product IN %@) AND stockLocation == %@", products, stockLocation)
        let convertedStockItems = SortHelper.sortStockItems(with: predicate, products: products)
        
        // Update the stock items
        stockItems.value = convertedStockItems ?? []
    }
}
