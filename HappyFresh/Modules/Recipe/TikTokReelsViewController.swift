//
//  TikTokReelsViewController.swift
//  HappyFresh
//
//  Created by Augment Agent on 2025-07-17.
//

import UIKit
import SnapKit
import AVFoundation

class TikTokReelsViewController: UIViewController {
    
    // MARK: - UI Components
    private lazy var collectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .vertical
        layout.minimumLineSpacing = 0
        layout.minimumInteritemSpacing = 0
        layout.itemSize = CGSize(width: view.frame.width, height: view.frame.height)
        
        let cv = UICollectionView(frame: .zero, collectionViewLayout: layout)
        cv.backgroundColor = .black
        cv.isPagingEnabled = true
        cv.showsVerticalScrollIndicator = false
        cv.delegate = self
        cv.dataSource = self
        cv.register(VideoReelCell.self, forCellWithReuseIdentifier: "VideoReelCell")
        return cv
    }()
    
    private let closeButton: UIButton = {
        let button = UIButton(type: .system)
        button.setImage(UIImage(systemName: "xmark"), for: .normal)
        button.tintColor = .white
        button.backgroundColor = UIColor.black.withAlphaComponent(0.5)
        button.layer.cornerRadius = 20
        button.layer.masksToBounds = true
        return button
    }()
    
    // MARK: - Properties
    private var videoReels: [VideoReel] = []
    private var currentIndex: Int = 0
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        print("jilytest123 🎬 TikTokReelsViewController loaded")
        setupUI()
        setupConstraints()
        loadVideoReels()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        navigationController?.setNavigationBarHidden(true, animated: animated)
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        playCurrentVideo()
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        ReelsVideoManager.shared.pauseAllVideos()
        navigationController?.setNavigationBarHidden(false, animated: animated)
    }
    
    override var prefersStatusBarHidden: Bool {
        return true
    }
    
    // MARK: - Setup
    private func setupUI() {
        view.backgroundColor = .black
        
        view.addSubview(collectionView)
        view.addSubview(closeButton)
        
        closeButton.addTarget(self, action: #selector(closeButtonTapped), for: .touchUpInside)
    }
    
    private func setupConstraints() {
        collectionView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        closeButton.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide.snp.top).offset(16)
            make.trailing.equalToSuperview().offset(-16)
            make.width.height.equalTo(40)
        }
    }
    
    // MARK: - Data Loading
    private func loadVideoReels() {
        print("jilytest123 📱 Loading video reels")
        
        // Use existing VideoReel sample data
        videoReels = VideoReel.sampleReels
        
        // Preload first 3 videos
        let preloadURLs = Array(videoReels.prefix(3)).map { $0.videoURL }
        ReelsVideoManager.shared.preloadVideos(urls: preloadURLs)
        
        // Reload collection view
        collectionView.reloadData()
        
        print("jilytest123 ✅ Loaded \(videoReels.count) video reels")
    }
    
    // MARK: - Video Control
    private func playCurrentVideo() {
        guard currentIndex < videoReels.count else { return }
        
        let videoReel = videoReels[currentIndex]
        print("jilytest123 🎬 Playing video at index \(currentIndex): \(videoReel.title)")
        
        ReelsVideoManager.shared.playVideo(url: videoReel.videoURL)
        
        // Preload next videos
        preloadNextVideos()
    }
    
    private func preloadNextVideos() {
        let nextIndices = [
            currentIndex + 1,
            currentIndex + 2
        ].filter { $0 < videoReels.count }
        
        let nextURLs = nextIndices.map { videoReels[$0].videoURL }
        if !nextURLs.isEmpty {
            ReelsVideoManager.shared.preloadVideos(urls: nextURLs)
        }
    }
    
    private func updateCurrentIndex() {
        let visibleIndexPaths = collectionView.indexPathsForVisibleItems
        if let indexPath = visibleIndexPaths.first {
            let newIndex = indexPath.item
            if newIndex != currentIndex {
                print("jilytest123 📱 Index changed from \(currentIndex) to \(newIndex)")
                currentIndex = newIndex
            }
        }
    }
    
    // MARK: - Actions
    @objc private func closeButtonTapped() {
        print("jilytest123 ❌ Close button tapped")
        ReelsVideoManager.shared.pauseAllVideos()
        
        if let navigationController = navigationController {
            navigationController.popViewController(animated: true)
        } else {
            dismiss(animated: true)
        }
    }
}

// MARK: - UICollectionViewDataSource
extension TikTokReelsViewController: UICollectionViewDataSource {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return videoReels.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "VideoReelCell", for: indexPath) as! VideoReelCell
        
        let videoReel = videoReels[indexPath.item]
        print("jilytest123 🔧 Configuring cell for index \(indexPath.item): \(videoReel.title)")
        
        cell.configure(with: videoReel)
        
        return cell
    }
}

// MARK: - UICollectionViewDelegate
extension TikTokReelsViewController: UICollectionViewDelegate {
    func scrollViewDidEndDecelerating(_ scrollView: UIScrollView) {
        print("jilytest123 📱 Scroll ended decelerating")
        updateCurrentIndex()
        playCurrentVideo()
    }
    
    func scrollViewWillBeginDragging(_ scrollView: UIScrollView) {
        print("jilytest123 📱 Scroll will begin")
        ReelsVideoManager.shared.pauseCurrentVideo()
    }
    
    func scrollViewDidEndDragging(_ scrollView: UIScrollView, willDecelerate decelerate: Bool) {
        if !decelerate {
            print("jilytest123 📱 Scroll ended without deceleration")
            updateCurrentIndex()
            playCurrentVideo()
        }
    }
}
