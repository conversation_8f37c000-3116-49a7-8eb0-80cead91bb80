//
//  TikTokReelsViewController.swift
//  HappyFresh
//
//  Created by Augment Agent on 2025-07-17.
//

import UIKit
import SnapKit
import AVFoundation

class TikTokReelsViewController: UIViewController {
    
    // MARK: - UI Components
    private lazy var collectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .vertical
        layout.minimumLineSpacing = 0
        layout.minimumInteritemSpacing = 0
        layout.itemSize = CGSize(width: view.frame.width, height: view.frame.height)
        
        let cv = UICollectionView(frame: .zero, collectionViewLayout: layout)
        cv.backgroundColor = .black
        cv.isPagingEnabled = true
        cv.showsVerticalScrollIndicator = false
        cv.delegate = self
        cv.dataSource = self
        cv.register(VideoReelCell.self, forCellWithReuseIdentifier: "VideoReelCell")
        return cv
    }()
    
    private let closeButton: UIButton = {
        let button = UIButton(type: .system)
        button.setImage(UIImage(systemName: "xmark"), for: .normal)
        button.tintColor = .white
        button.backgroundColor = UIColor.black.withAlphaComponent(0.5)
        button.layer.cornerRadius = 20
        button.layer.masksToBounds = true
        return button
    }()
    
    // MARK: - Properties
    private var videoReels: [VideoReel] = []
    private var currentIndex: Int = 0

    // Advanced video management
    private var queuePlayer: AVQueuePlayer?
    private var playerItems: [String: AVPlayerItem] = [:]
    private var playerLayers: [String: AVPlayerLayer] = [:]
    private var bufferObservers: [String: NSKeyValueObservation] = [:]
    private var statusObservers: [String: NSKeyValueObservation] = [:]
    private var preloadQueue = DispatchQueue(label: "com.happyfresh.tiktok.preload", qos: .userInitiated)
    private var isPreloading = false
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        print("jilytest123 🎬 TikTokReelsViewController loaded")
        setupAudioSession()
        setupUI()
        setupConstraints()
        loadVideoReels()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        navigationController?.setNavigationBarHidden(true, animated: animated)
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        playCurrentVideo()
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        pauseAllVideos()
        navigationController?.setNavigationBarHidden(false, animated: animated)
    }

    deinit {
        print("jilytest123 🗑️ TikTokReelsViewController deinit - cleaning up")
        cleanupAllObservers()
        queuePlayer?.pause()
        queuePlayer = nil
    }
    
    override var prefersStatusBarHidden: Bool {
        return true
    }
    
    // MARK: - Setup
    private func setupAudioSession() {
        do {
            try AVAudioSession.sharedInstance().setCategory(.playback, mode: .default, options: [])
            try AVAudioSession.sharedInstance().setActive(true)
            print("jilytest123 🔊 Audio session configured for TikTok reels")
        } catch {
            print("jilytest123 ❌ Audio session setup failed: \(error)")
        }
    }

    private func setupUI() {
        view.backgroundColor = .black

        view.addSubview(collectionView)
        view.addSubview(closeButton)

        closeButton.addTarget(self, action: #selector(closeButtonTapped), for: .touchUpInside)
    }
    
    private func setupConstraints() {
        collectionView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        closeButton.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide.snp.top).offset(16)
            make.trailing.equalToSuperview().offset(-16)
            make.width.height.equalTo(40)
        }
    }
    
    // MARK: - Data Loading
    private func loadVideoReels() {
        print("jilytest123 📱 Loading video reels with advanced preloading")

        // Use existing VideoReel sample data
        videoReels = VideoReel.sampleReels

        // Reload collection view first
        collectionView.reloadData()

        // Start aggressive preloading
        startAdvancedPreloading()

        print("jilytest123 ✅ Loaded \(videoReels.count) video reels")
    }

    private func startAdvancedPreloading() {
        guard !videoReels.isEmpty && !isPreloading else { return }

        isPreloading = true
        print("jilytest123 🚀 Starting advanced preloading for \(videoReels.count) videos")

        preloadQueue.async { [weak self] in
            guard let self = self else { return }

            // Preload first video immediately on main thread for instant playback
            DispatchQueue.main.async {
                self.preloadVideoItem(at: 0, priority: .immediate)
            }

            // Preload next 2 videos with high priority
            for index in 1..<min(3, self.videoReels.count) {
                self.preloadVideoItem(at: index, priority: .high)
                Thread.sleep(forTimeInterval: 0.1) // Small delay between preloads
            }

            // Preload remaining videos with normal priority
            for index in 3..<self.videoReels.count {
                self.preloadVideoItem(at: index, priority: .normal)
                Thread.sleep(forTimeInterval: 0.2) // Longer delay for background preloading
            }

            DispatchQueue.main.async {
                self.isPreloading = false
                print("jilytest123 ✅ Advanced preloading completed")

                // Start playing first video after preloading
                self.playCurrentVideo()
            }
        }
    }

    private enum PreloadPriority {
        case immediate, high, normal
    }

    private func preloadVideoItem(at index: Int, priority: PreloadPriority) {
        guard index < videoReels.count else { return }

        let videoReel = videoReels[index]
        let url = videoReel.videoURL
        let urlString = url.absoluteString

        // Skip if already preloaded
        if playerItems[urlString] != nil {
            print("jilytest123 ⚡ Video \(index) already preloaded: \(videoReel.title)")
            return
        }

        print("jilytest123 🎯 Preloading video \(index) (\(priority)): \(videoReel.title)")

        // Create optimized player item
        let playerItem = AVPlayerItem(url: url)

        // Optimize based on priority
        switch priority {
        case .immediate:
            playerItem.preferredForwardBufferDuration = 5.0 // Longer buffer for current video
        case .high:
            playerItem.preferredForwardBufferDuration = 3.0 // Medium buffer for next videos
        case .normal:
            playerItem.preferredForwardBufferDuration = 1.0 // Minimal buffer for background videos
        }

        playerItem.canUseNetworkResourcesForLiveStreamingWhilePaused = true

        // Store player item
        playerItems[urlString] = playerItem

        // Setup buffer monitoring
        setupBufferMonitoring(for: playerItem, videoTitle: videoReel.title, index: index)

        // Setup status monitoring
        setupStatusMonitoring(for: playerItem, videoTitle: videoReel.title, index: index, urlString: urlString)

        print("jilytest123 📦 Created player item for video \(index): \(videoReel.title)")
    }

    private func setupBufferMonitoring(for playerItem: AVPlayerItem, videoTitle: String, index: Int) {
        let urlString = playerItem.asset.description

        // Monitor loadedTimeRanges for buffer progress
        let bufferObserver = playerItem.observe(\.loadedTimeRanges, options: [.new]) { [weak self] item, _ in
            DispatchQueue.main.async {
                self?.handleBufferUpdate(for: item, videoTitle: videoTitle, index: index)
            }
        }

        bufferObservers[urlString] = bufferObserver
    }

    private func setupStatusMonitoring(for playerItem: AVPlayerItem, videoTitle: String, index: Int, urlString: String) {
        // Monitor player item status
        let statusObserver = playerItem.observe(\.status, options: [.new]) { [weak self] item, _ in
            DispatchQueue.main.async {
                self?.handleStatusUpdate(for: item, videoTitle: videoTitle, index: index)
            }
        }

        statusObservers[urlString] = statusObserver
    }

    private func handleBufferUpdate(for playerItem: AVPlayerItem, videoTitle: String, index: Int) {
        let loadedTimeRanges = playerItem.loadedTimeRanges

        if !loadedTimeRanges.isEmpty {
            let timeRange = loadedTimeRanges[0].timeRangeValue
            let loadedDuration = CMTimeGetSeconds(timeRange.duration)
            let totalDuration = CMTimeGetSeconds(playerItem.duration)

            if totalDuration > 0 {
                let bufferPercentage = (loadedDuration / totalDuration) * 100
                print("jilytest123 📊 Video \(index) (\(videoTitle)) buffer: \(String(format: "%.1f", loadedDuration))s / \(String(format: "%.1f", totalDuration))s (\(String(format: "%.1f", bufferPercentage))%)")
            } else {
                print("jilytest123 📊 Video \(index) (\(videoTitle)) loaded: \(String(format: "%.1f", loadedDuration))s")
            }
        }
    }

    private func handleStatusUpdate(for playerItem: AVPlayerItem, videoTitle: String, index: Int) {
        switch playerItem.status {
        case .readyToPlay:
            print("jilytest123 ✅ Video \(index) (\(videoTitle)) ready to play")
        case .failed:
            if let error = playerItem.error {
                print("jilytest123 ❌ Video \(index) (\(videoTitle)) failed: \(error.localizedDescription)")
            } else {
                print("jilytest123 ❌ Video \(index) (\(videoTitle)) failed with unknown error")
            }
        case .unknown:
            print("jilytest123 ⏳ Video \(index) (\(videoTitle)) status unknown")
        @unknown default:
            print("jilytest123 ❓ Video \(index) (\(videoTitle)) unknown status")
        }
    }
    
    // MARK: - Video Control
    private func playCurrentVideo() {
        guard currentIndex < videoReels.count else { return }

        let videoReel = videoReels[currentIndex]
        let url = videoReel.videoURL
        let urlString = url.absoluteString

        print("jilytest123 🎬 Playing video at index \(currentIndex): \(videoReel.title)")

        // Pause current queue player
        queuePlayer?.pause()

        // Get or create player item
        let playerItem: AVPlayerItem
        if let existingItem = playerItems[urlString] {
            playerItem = existingItem
            print("jilytest123 🔄 Using preloaded item for: \(videoReel.title)")
        } else {
            print("jilytest123 ⚠️ Creating new item for: \(videoReel.title)")
            playerItem = AVPlayerItem(url: url)
            playerItems[urlString] = playerItem
            setupBufferMonitoring(for: playerItem, videoTitle: videoReel.title, index: currentIndex)
            setupStatusMonitoring(for: playerItem, videoTitle: videoReel.title, index: currentIndex, urlString: urlString)
        }

        // Create new queue player with current item
        queuePlayer = AVQueuePlayer(playerItem: playerItem)
        queuePlayer?.automaticallyWaitsToMinimizeStalling = false
        queuePlayer?.volume = 1.0

        // Setup player layer for current cell
        setupPlayerLayerForCurrentVideo()

        // Start playback
        queuePlayer?.play()

        // Monitor playback
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [weak self] in
            if let rate = self?.queuePlayer?.rate, rate > 0 {
                print("jilytest123 ✅ Video playing successfully at rate: \(rate)")
            } else {
                print("jilytest123 ⚠️ Video not playing, retrying...")
                self?.queuePlayer?.play()
            }
        }

        // Preload next video into queue
        preloadNextVideoIntoQueue()
    }

    private func setupPlayerLayerForCurrentVideo() {
        guard let queuePlayer = queuePlayer,
              let cell = collectionView.cellForItem(at: IndexPath(item: currentIndex, section: 0)) as? VideoReelCell else {
            print("jilytest123 ⚠️ Could not get cell for current video")
            return
        }

        // Update the cell's player layer with our queue player
        let videoReel = videoReels[currentIndex]
        let urlString = videoReel.videoURL.absoluteString

        let playerLayer: AVPlayerLayer
        if let existingLayer = playerLayers[urlString] {
            existingLayer.player = queuePlayer
            playerLayer = existingLayer
            print("jilytest123 🔄 Reusing player layer for: \(videoReel.title)")
        } else {
            playerLayer = AVPlayerLayer(player: queuePlayer)
            playerLayer.videoGravity = .resizeAspectFill
            playerLayers[urlString] = playerLayer
            print("jilytest123 🆕 Created new player layer for: \(videoReel.title)")
        }

        // Set the player layer on the cell
        cell.setPlayerLayer(playerLayer)
    }

    private func preloadNextVideoIntoQueue() {
        let nextIndex = currentIndex + 1
        guard nextIndex < videoReels.count else { return }

        let nextVideoReel = videoReels[nextIndex]
        let nextUrlString = nextVideoReel.videoURL.absoluteString

        // Get or create next player item
        if let nextPlayerItem = playerItems[nextUrlString] {
            print("jilytest123 ⏭️ Adding preloaded next video to queue: \(nextVideoReel.title)")
            queuePlayer?.insert(nextPlayerItem, after: nil)
        } else {
            print("jilytest123 🔄 Preloading next video for queue: \(nextVideoReel.title)")
            preloadVideoItem(at: nextIndex, priority: .high)
        }
    }

    private func pauseAllVideos() {
        print("jilytest123 ⏸️ Pausing all videos")
        queuePlayer?.pause()
    }

    private func cleanupAllObservers() {
        print("jilytest123 🧹 Cleaning up all observers")

        // Cleanup buffer observers
        for (_, observer) in bufferObservers {
            observer.invalidate()
        }
        bufferObservers.removeAll()

        // Cleanup status observers
        for (_, observer) in statusObservers {
            observer.invalidate()
        }
        statusObservers.removeAll()

        // Clear player items and layers
        playerItems.removeAll()
        playerLayers.removeAll()
    }
    
    private func updateCurrentIndex() {
        let visibleIndexPaths = collectionView.indexPathsForVisibleItems
        if let indexPath = visibleIndexPaths.first {
            let newIndex = indexPath.item
            if newIndex != currentIndex {
                print("jilytest123 📱 Index changed from \(currentIndex) to \(newIndex)")
                currentIndex = newIndex
            }
        }
    }
    
    // MARK: - Actions
    @objc private func closeButtonTapped() {
        print("jilytest123 ❌ Close button tapped")
        pauseAllVideos()
        cleanupAllObservers()

        if let navigationController = navigationController {
            navigationController.popViewController(animated: true)
        } else {
            dismiss(animated: true)
        }
    }
}

// MARK: - UICollectionViewDataSource
extension TikTokReelsViewController: UICollectionViewDataSource {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return videoReels.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "VideoReelCell", for: indexPath) as! VideoReelCell
        
        let videoReel = videoReels[indexPath.item]
        print("jilytest123 🔧 Configuring cell for index \(indexPath.item): \(videoReel.title)")
        
        cell.configure(with: videoReel)
        
        return cell
    }
}

// MARK: - UICollectionViewDelegate
extension TikTokReelsViewController: UICollectionViewDelegate {
    func scrollViewDidEndDecelerating(_ scrollView: UIScrollView) {
        print("jilytest123 📱 Scroll ended decelerating")
        updateCurrentIndex()
        playCurrentVideo()
    }

    func scrollViewWillBeginDragging(_ scrollView: UIScrollView) {
        print("jilytest123 📱 Scroll will begin - pausing current video")
        pauseAllVideos()
    }

    func scrollViewDidEndDragging(_ scrollView: UIScrollView, willDecelerate decelerate: Bool) {
        if !decelerate {
            print("jilytest123 📱 Scroll ended without deceleration")
            updateCurrentIndex()
            playCurrentVideo()
        }
    }
}
