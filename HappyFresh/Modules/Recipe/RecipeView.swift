//
//  RecipeView.swift
//  HappyFresh
//
//  Created by <PERSON><PERSON><PERSON> on 24/06/19.
//  Copyright © 2019 HappyFresh Inc. All rights reserved.
//

import UIKit

class RecipeView: BaseCustomView {

    @IBOutlet weak var recipeCollectionView: RecipeCollectionView!
    @IBOutlet weak var progressIndicator: UIActivityIndicatorView!
    @IBOutlet weak var recipeLabel: UILabel!
    @IBOutlet weak var recipeCollectionViewHeightConstraint: NSLayoutConstraint!
    lazy var seeAllView: SeeAllView = {
        let view = SeeAllView(title: NSLocalizedString("See all", comment: ""))
        return view
    }()
    let titleHeight: CGFloat = 50
    var recipesFetchedCount: Int?
    var collectionViewHeight: CGFloat {
        let numberOfRows = viewModel?.numberOfRows ?? 1
        let height = recipeCollectionView.height + (CGFloat(numberOfRows - 1) * OldRecipeCell.itemSpacing) + 8
        return height
    }
    
    var recipeDidSelect:((RecipeCellViewModel) -> Void)?
    
    var recipesHeightUpdated:((CGFloat) -> Void)?
   
    var recipesFetchedCountBlock:((Int) -> Void)?
    
    var seeAllRecipesTappedBlock:(() -> Void)?
    
    var viewModel: RecipeViewModel? {
        didSet {
            if viewModel?.isLoaded ?? false {
                recipeCollectionView.viewModels = viewModel?.recipeCellViewModels.value
                isHidden = viewModel?.recipeCellViewModels.value.isEmpty ?? true
                updateCollectionViewHeight()
            } else {
                setupObserver()
                viewModel?.fetchRecipes()
            }
        }
    }
    
    override func awakeFromNib() {
        super.awakeFromNib()
        commonInit()
    }
    
    convenience init() {
        self.init(frame: .zero)
    }
    
    override init(frame: CGRect) {
        super.init(frame: .zero)
        commonInit()
    }
    
    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        commonInit()
    }
    
    var contentHeight: CGFloat {
        (viewModel?.recipeCellViewModels.value.isEmpty ?? true) ?
            0 : collectionViewHeight + titleHeight
    }
    
    func setupObserver() {
        viewModel?.recipeCellViewModels.signal.observeValues({ [weak self] (viewModels) in
            if viewModels.count > 0 {
                self?.isHidden = false
                self?.recipeCollectionView.viewModels = viewModels
                self?.recipesFetchedCountBlock?(viewModels.count)
                self?.updateCollectionViewHeight()
            } else {
                self?.isHidden = true
            }
        })
        viewModel?.isFetching.signal.observeValues({ [weak self] (isFetching) in
            if isFetching {
                self?.progressIndicator.startAnimating()
            } else {
                self?.progressIndicator.stopAnimating()
            }
        })
    }
    
    private func commonInit() {
        recipeLabel.text = NSLocalizedString("Explore recipes", comment: "")
        recipeLabel.font = .hf_title_4()
        recipeLabel.textColor = .hf_grey_100()
        
        addSubview(seeAllView)
        seeAllView.snp.makeConstraints { make in
            make.centerY.equalTo(recipeLabel)
            make.trailing.equalToSuperview().inset(16)
        }
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(seeAllTapped))
        seeAllView.addGestureRecognizer(tapGesture)
        recipeCollectionView.recipeDidSelect = recipeDidSelect
    }
    
    func updateCollectionViewHeight() {
        let newHeight: CGFloat = collectionViewHeight
        let heightChanged = recipeCollectionViewHeightConstraint.constant != newHeight
        recipeCollectionViewHeightConstraint.constant = newHeight
        if heightChanged {
            recipesHeightUpdated?(collectionViewHeight + titleHeight)
        }
    }
    
    @objc
    func seeAllTapped() {
        if let seeAllTappedBlock = self.seeAllRecipesTappedBlock {
            seeAllTappedBlock()
        }
    }
    
}

