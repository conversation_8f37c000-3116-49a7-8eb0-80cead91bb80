//
//  RecipeDetailViewController.swift
//  HappyFresh
//
//  Created by Augment Agent on 2025-07-07.
//

import UIKit
import SnapKit
import WebKit

class RecipeDetailViewController: BaseViewController {
    
    // MARK: - UI Components
    private let scrollView = UIScrollView()
    private let contentView = UIView()
    private let youtubePlayerView = {
        let configuration = WKWebViewConfiguration()
        configuration.allowsInlineMediaPlayback = true
        configuration.mediaTypesRequiringUserActionForPlayback = []
        configuration.allowsPictureInPictureMediaPlayback = true
        
        let view = WKWebView(frame: .zero, configuration: configuration)
        view.backgroundColor = .black
        view.isUserInteractionEnabled = true
        view.scrollView.isScrollEnabled = false
        view.scrollView.bounces = false
        view.scrollView.isUserInteractionEnabled = true
        return view
    }()
    private let recipeInfoView = UIView()
    private let titleLabel = UILabel()
    private let descriptionLabel = UILabel()
    private let infoStackView = UIStackView()
    private let cookingTimeView = RecipeInfoBadgeView()
    private let difficultyView = RecipeInfoBadgeView()
    private let servingView = RecipeInfoBadgeView()
    private let ingredientsTableView = UITableView()
    private let addToBasketButton: UIButton = {
        let button = HappyButton(title: NSLocalizedString("Add to basket", comment: ""),
                                accentColor: UIColor.hf_prime(),
                                type: HappyButton.HappyButtonType.Primary)
        return button
    }()
    private let loadingIndicator = UIActivityIndicatorView(style: .large)
    
    // MARK: - Properties
    private let recipeID: NSNumber
    private let inspirationsService = InspirationsServices()
    private let storeSelectionRouter = StoreSelectionRouter()
    private var recipe: Recipe?
    private var products: [Product] = []
    private var stockItems: [StockItem] = []
    private var gradientLayer: CAGradientLayer?
    private var isStockLocationSelected = false
    private var isSubtitleExpanded = false
    
    // Store selection tracking
    private var isWaitingForStoreSelection = false
    
    // YouTube video URL
    private let youtubeVideoURL = "https://youtu.be/FYu0vJHYt9U"
    
    // MARK: - Initialization
    init(recipeID: NSNumber) {
        self.recipeID = recipeID
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        screenName = ScreenNameInspirationDetail
        setupUI()
        setupConstraints()
        setupOrderObserver()
        setupYouTubePlayer()
        fetchRecipeDetail()
    }
    
    // MARK: - Setup
    private func setupUI() {
        view.backgroundColor = .hf_white()
        
        // Setup scroll view
        scrollView.showsVerticalScrollIndicator = false

        // Setup recipe info view
        setupRecipeInfoView()
        
        // Setup ingredients table view
        setupIngredientsTableView()
        
        // Setup add to basket button
        setupAddToBasketButton()
        
        // Setup loading indicator
        loadingIndicator.color = .gray
        loadingIndicator.hidesWhenStopped = true
        
        // Add subviews
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
        contentView.addSubview(youtubePlayerView)
        contentView.addSubview(recipeInfoView)
        contentView.addSubview(ingredientsTableView)
        view.addSubview(addToBasketButton)
        view.addSubview(loadingIndicator)
    }
    
    private func createYouTubeEmbedHTML(videoID: String) -> String {
        return """
        <!DOCTYPE html>
        <html>
        <head>
            <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
            <style>
                * {
                    margin: 0;
                    padding: 0;
                    box-sizing: border-box;
                }
                body {
                    background-color: #000;
                    overflow: hidden;
                    touch-action: auto;
                }
                .video-container {
                    position: relative;
                    width: 100vw;
                    height: 100vh;
                    background-color: #000;
                }
                iframe {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    border: none;
                    touch-action: auto;
                }
            </style>
        </head>
        <body>
            <div class="video-container">
                <iframe 
                    id="youtube-player"
                    src="https://www.youtube.com/embed/\(videoID)?playsinline=0&controls=1&showinfo=0&rel=0&iv_load_policy=3&modestbranding=1&enablejsapi=1&fs=1"
                    frameborder="0"
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share; fullscreen"
                    allowfullscreen
                    webkitallowfullscreen
                    mozallowfullscreen>
                </iframe>
            </div>
            <script>
                // YouTube API ready function
                var tag = document.createElement('script');
                tag.src = "https://www.youtube.com/iframe_api";
                var firstScriptTag = document.getElementsByTagName('script')[0];
                firstScriptTag.parentNode.insertBefore(tag, firstScriptTag);

                var player;
                function onYouTubeIframeAPIReady() {
                    player = new YT.Player('youtube-player', {
                        videoId: '\(videoID)',
                        playerVars: {
                            'playsinline': 0,
                            'controls': 1,
                            'showinfo': 0,
                            'rel': 0,
                            'iv_load_policy': 3,
                            'modestbranding': 1,
                            'fs': 1,
                            'cc_load_policy': 0,
                            'disablekb': 0,
                            'enablejsapi': 1
                        },
                        events: {
                            'onReady': onPlayerReady,
                            'onStateChange': onPlayerStateChange
                        }
                    });
                }

                function onPlayerReady(event) {
                    // Player is ready
                    console.log('YouTube player ready');
                }

                function onPlayerStateChange(event) {
                    // When video starts playing, request fullscreen
                    if (event.data == YT.PlayerState.PLAYING) {
                        requestFullscreen();
                    }
                }

                function requestFullscreen() {
                    var iframe = document.getElementById('youtube-player');
                    if (iframe) {
                        if (iframe.requestFullscreen) {
                            iframe.requestFullscreen();
                        } else if (iframe.webkitRequestFullscreen) {
                            iframe.webkitRequestFullscreen();
                        } else if (iframe.mozRequestFullScreen) {
                            iframe.mozRequestFullScreen();
                        } else if (iframe.msRequestFullscreen) {
                            iframe.msRequestFullscreen();
                        }
                    }
                }

                // Fallback: Listen for click events on the iframe
                document.addEventListener('DOMContentLoaded', function() {
                    var iframe = document.getElementById('youtube-player');
                    if (iframe) {
                        iframe.style.pointerEvents = 'auto';
                        
                        // Add click listener as fallback
                        iframe.addEventListener('click', function() {
                            setTimeout(function() {
                                if (player && typeof player.getPlayerState === 'function') {
                                    if (player.getPlayerState() === YT.PlayerState.PLAYING) {
                                        requestFullscreen();
                                    }
                                }
                            }, 500); // Small delay to ensure play state is detected
                        });
                    }
                });

                // Handle fullscreen exit
                document.addEventListener('fullscreenchange', function() {
                    if (!document.fullscreenElement) {
                        // Exited fullscreen
                        console.log('Exited fullscreen');
                    }
                });

                document.addEventListener('webkitfullscreenchange', function() {
                    if (!document.webkitFullscreenElement) {
                        // Exited fullscreen
                        console.log('Exited webkit fullscreen');
                    }
                });
            </script>
        </body>
        </html>
        """
    }

    private func setupYouTubePlayer() {
        guard let videoID = extractVideoID(from: youtubeVideoURL) else {
            print("❌ Failed to extract video ID from URL: \(youtubeVideoURL)")
            return
        }
        
        let embedHTML = createYouTubeEmbedHTML(videoID: videoID)
        youtubePlayerView.loadHTMLString(embedHTML, baseURL: nil)
    }
    
    private func extractVideoID(from urlString: String) -> String? {
        // Handle different YouTube URL formats
        if let url = URL(string: urlString) {
            // For youtu.be format
            if url.host == "youtu.be" {
                return String(url.path.dropFirst()) // Remove leading "/"
            }
            
            // For youtube.com format
            if url.host?.contains("youtube.com") == true {
                let components = URLComponents(url: url, resolvingAgainstBaseURL: false)
                return components?.queryItems?.first(where: { $0.name == "v" })?.value
            }
        }
        
        return nil
    }

    private func setupRecipeInfoView() {
        // Setup title label
        titleLabel.font = UIFont.boldSystemFont(ofSize: 20)
        titleLabel.textColor = .black
        titleLabel.numberOfLines = 0

        // Setup subtitle label
        descriptionLabel.font = UIFont.systemFont(ofSize: 14)
        descriptionLabel.textColor = .gray
        descriptionLabel.numberOfLines = 0

        // Setup info stack view
        infoStackView.axis = .horizontal
        infoStackView.distribution = .fillEqually

        // Setup info badge views
        cookingTimeView.configure(icon: "clock", title: "30 mins")
        difficultyView.configure(icon: "star", title: "Easy")
        servingView.configure(icon: "person.2", title: "2 People")

        // Add subviews to recipe info view
        recipeInfoView.addSubview(titleLabel)
        recipeInfoView.addSubview(infoStackView)
        recipeInfoView.addSubview(descriptionLabel)

        // Add badge views to stack view
        infoStackView.addArrangedSubview(cookingTimeView)
        infoStackView.addArrangedSubview(difficultyView)
        infoStackView.addArrangedSubview(servingView)

        // Setup constraints for recipe info view content
        setupRecipeInfoViewConstraints()
    }

    private func setupRecipeInfoViewConstraints() {
        // Title label constraints
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
        }
        
        // Info stack view constraints
        infoStackView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom)
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
            make.height.equalTo(40)
        }

        // Subtitle label constraints
        descriptionLabel.snp.makeConstraints { make in
            make.top.equalTo(infoStackView.snp.bottom)
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
        }
    }
    
    private func setupIngredientsTableView() {
        ingredientsTableView.delegate = self
        ingredientsTableView.dataSource = self
        ingredientsTableView.separatorStyle = .singleLine
        ingredientsTableView.isScrollEnabled = false // Keep this false since it's inside scroll view
        ingredientsTableView.backgroundColor = .clear
        ingredientsTableView.register(IngredientCell.self, forCellReuseIdentifier: IngredientCell.reuseIdentifier)
    }
    
    private func setupAddToBasketButton() {
        addToBasketButton.addTarget(self, action: #selector(addToBasketButtonTapped), for: .touchUpInside)
    }

    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.bottom.equalTo(addToBasketButton.snp.top).offset(-16)
        }
    
        // Content view constraints
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }
    
        // YouTube player constraints (replacing header image)
        youtubePlayerView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(300)
        }
    
        // Recipe info view constraints
        recipeInfoView.snp.makeConstraints { make in
            make.top.equalTo(youtubePlayerView.snp.bottom)
            make.left.right.equalToSuperview()
        }
    
        // Ingredients table view constraints - Use dynamic height
        ingredientsTableView.snp.makeConstraints { make in
            make.top.equalTo(recipeInfoView.snp.bottom).offset(16)
            make.left.right.equalToSuperview()
            make.height.equalTo(0) // Will be updated dynamically
            make.bottom.equalToSuperview().offset(-16) // This defines the content view height
        }
        
        // Add to basket button - Fixed at bottom
        addToBasketButton.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(50)
            make.bottom.equalTo(view.safeAreaLayoutGuide.snp.bottom)
        }
    
        // Loading indicator constraints
        loadingIndicator.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }
    }
    
    private func setupOrderObserver() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(orderDidChange(_:)),
            name: NSNotification.Name.OrderChanged,
            object: nil
        )
    }

    // MARK: - Data Fetching
    private func fetchRecipeDetail(stockLocation: StockLocation? = nil) {
        showLoading()

        inspirationsService.getInspirationDetail(withInspirationID: recipeID,
                                                 stockLocation: stockLocation,
                                                 success: { [weak self] operation, recipe, products in
            DispatchQueue.main.async {
                guard let self = self else { return }
                self.hideLoading()
                self.recipe = recipe
                self.products = products
                self.updateUI()
                self.updateTableViewHeight(totalProducts: products.count)
            }
        }, failure: { [weak self] operation, error in
            DispatchQueue.main.async {
                self?.loadingIndicator.stopAnimating()
                Popup.showErrorMessage(fromResponse: operation)
            }
        })
    }
    
    private func showLoading() {
        loadingIndicator.startAnimating()
        contentView.isHidden = true
    }
    
    private func hideLoading() {
        self.loadingIndicator.stopAnimating()
        contentView.isHidden = false
    }

    private func updateUI() {
        guard let recipe = recipe else { return }

        // Update title and subtitle
        title = recipe.name
        titleLabel.text = recipe.name
        descriptionLabel.text = recipe.displayInstructions

        // Update info badge views
        cookingTimeView.configure(icon: "clock", title: recipe.displayTimeToCook ?? "30 mins")
        difficultyView.configure(icon: "star", title: recipe.displayDifficulty ?? "Easy")
        servingView.configure(icon: "person.2", title: recipe.displayServingPortion ?? "2 People")

        // Note: YouTube video is already loaded in setupYouTubePlayer()
        // No need to load recipe image anymore

        // Reload ingredients table
        ingredientsTableView.reloadData()
    }

    private func updateTableViewHeight(totalProducts: Int) {
        let height = IngredientCell.height * CGFloat(totalProducts)
        ingredientsTableView.snp.updateConstraints { make in
            make.height.equalTo(height)
        }
        
        // Force layout update to ensure scroll view content size is recalculated
        view.layoutIfNeeded()
    }

    private func showErrorAlert(message: String) {
        let alert = UIAlertController(title: "Error", message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "OK", style: .default))
        present(alert, animated: true)
    }

    @objc func addToBasketButtonTapped() {
        if (isStockLocationSelected) {
            addAllIngredientsToCart()
        } else {
            // Check if we have an address first
            guard let address = App.sharedInstance().address else {
                print("❌ No address found, need to set location first")
                showLocationPrompt()
                return
            }

            // Show store selection
            showStoreSelection(deliveryAddress: address)
        }
    }

    @objc func addAllIngredientsButtonTapped() {
        print("🛒 Add all ingredients button tapped")
        addAllIngredientsToCart()
    }

    private func showLocationPrompt() {
        // Show location prompt similar to HomeViewController
        let alert = UIAlertController(
            title: "Location Required",
            message: "Please set your delivery location first to select a store.",
            preferredStyle: .alert
        )

        alert.addAction(UIAlertAction(title: "Set Location", style: .default) { _ in
            // Navigate to location setting
            App.sharedInstance().goToGlobalHomeTab {
                // This will trigger location setting flow
            }
        })

        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))

        present(alert, animated: true)
    }

    private func showStoreSelection(deliveryAddress: Address) {
        isWaitingForStoreSelection = true

        // Set up dismissal completion handler
        storeSelectionRouter.dismissalCompletionHandler = { [weak self] in
            self?.handleStoreSelectionDismissed()
        }

        // Present store selection without going to store home
        storeSelectionRouter.present(
            from: self,
            campaignFromDeeplink: nil,
            showCloseButton: true,
            popToRootAfterDismiss: false,
            fromFindStore: false,
            showStoreHome: false, // Don't go to store home
            deliveryAddress: deliveryAddress,
            source: screenName
        )
    }
    
    private func handleStoreSelectionDismissed() {
        guard isWaitingForStoreSelection else { return }
        isWaitingForStoreSelection = false

        if let stockLocation = App.sharedInstance().stockLocation {
            fetchRecipeDetail(stockLocation: stockLocation)
        }
        
        isStockLocationSelected = true
        rightBarButtonItemTypes = .cart
    }
    
    @objc private func orderDidChange(_ notification: Notification) {
        ingredientsTableView.reloadData()
    }

    // MARK: - Add All Ingredients to Cart
    func addAllIngredientsToCart() {
        print("🛒 Adding all ingredients to cart")

        guard let stockLocation = App.sharedInstance().stockLocation else {
            print("❌ No stock location available")
            showLocationPrompt()
            return
        }

        guard let order = App.sharedInstance().order else {
            print("❌ No order available")
            return
        }

        guard !products.isEmpty else {
            print("❌ No products available to add")
            return
        }

        // Convert products to stock items
        let stockItems = stockItemsFromProducts(products, stockLocation: stockLocation)

        guard !stockItems.isEmpty else {
            print("❌ No available stock items found")
            showNoAvailableItemsAlert()
            return
        }

        // Show loading
        showAddingToCartLoading()

        // Create line item payloads
        let oldLineItemPayloads = createOldLineItemPayloads(from: order)
        let lineItemPayloads = createLineItemPayloads(from: stockItems, order: order)

        // Update multiple line items
        let productService = ProductsService.sharedInstance()
        productService?.updateMultipleLineItems(
            with: order,
            stockLocation: stockLocation,
            lineItemPayloads: lineItemPayloads,
            oldLineItemPayloads: oldLineItemPayloads,
            loadBundleQuantity: false,
            success: { [weak self] operation, updatedOrder, failedItems in
                DispatchQueue.main.async {
                    self?.hideAddingToCartLoading()
                    self?.showAddAllToCartSuccessMessage()
                    self?.trackAddAllToCart()
                    NotificationCenter.default.post(name: NSNotification.Name.OrderChanged, object: nil)
                }
            },
            failure: { [weak self] operation, error in
                DispatchQueue.main.async {
                    self?.hideAddingToCartLoading()
                    self?.handleAddAllToCartError(operation: operation, error: error)
                }
            }
        )
    }

    private func stockItemsFromProducts(_ products: [Product], stockLocation: StockLocation) -> [StockItem] {
        var stockItems: [StockItem] = []

        for product in products {
            if let variant = product.variant() {
                if let stockItem = StockItem.find(by: stockLocation, variant: variant) {
                    stockItems.append(stockItem)
                }
            }
        }

        print("🔍 Found \(stockItems.count) available stock items out of \(products.count) products")
        return stockItems
    }

    private func createOldLineItemPayloads(from order: Order) -> [LineItemPayload] {
        guard let lineItems = order.lineItems?.array as? [LineItem] else {
            return []
        }

        return lineItems.map { lineItem in
            LineItemPayload(lineItem: lineItem, quantity: lineItem.quantity ?? NSNumber(value: 0))
        }
    }

    private func createLineItemPayloads(from stockItems: [StockItem], order: Order) -> [LineItemPayload] {
        return stockItems.map { stockItem in
            let currentQuantity = stockItem.quantity(on: order).intValue
            let newQuantity = NSNumber(value: currentQuantity + 1)
            return LineItemPayload(stockItem: stockItem, quantity: newQuantity)
        }
    }

    private func showAddingToCartLoading() {
        // Show loading indicator or disable button
        addToBasketButton.isEnabled = false
        addToBasketButton.setTitle("Adding to Cart...", for: .normal)
    }

    private func hideAddingToCartLoading() {
        // Hide loading indicator and re-enable button
        addToBasketButton.isEnabled = true
        addToBasketButton.setTitle("Add to Basket", for: .normal)
    }

    private func showAddAllToCartSuccessMessage() {
        let message = "All available ingredients were added to your cart. Happy Cooking!"
        BannerManager.sharedInstance().showBanner(withText: message, userInfo: nil)
    }

    private func showNoAvailableItemsAlert() {
        let alert = UIAlertController(
            title: "No Available Items",
            message: "No ingredients are currently available in the selected store.",
            preferredStyle: .alert
        )
        alert.addAction(UIAlertAction(title: "OK", style: .default))
        present(alert, animated: true)
    }

    private func handleAddAllToCartError(operation: AFHTTPRequestOperation?, error: Error?) {
        if let operation = operation {
            if operation.response == nil {
                AlertController.showError(withMessage: NSLocalizedString("Connection problem occurred", comment: ""))
            } else if operation.response?.statusCode == 404 {
                // Line item has been deleted while updating (perhaps caused by some concurrent request).
                // Don't show pop up message.
            } else {
                AlertController.showError(withMessage: operation.errorMessage())
            }
        } else {
            AlertController.showError(withMessage: "An error occurred while adding items to cart")
        }
    }

    private func trackAddAllToCart() {
        guard let stockLocation = App.sharedInstance().stockLocation else { return }

        let unavailableItemCount = calculateUnavailableItemCount(stockLocation: stockLocation)
        let availableItemCount = products.count - unavailableItemCount
        
        Tracker.sharedInstance().trackAddAllToCart(
            withShoppingListID: recipeID,
            unavailableItemCount: unavailableItemCount,
            lineItemCount: availableItemCount,
            screen: screenName
        )
    }

    private func calculateUnavailableItemCount(stockLocation: StockLocation) -> Int {
        var unavailableCount = 0

        for product in products {
            if let variant = product.variant() {
                if let stockItem = StockItem.find(by: stockLocation, variant: variant) {
                    let unavailable = (stockItem.price?.intValue ?? 0) == 0 || !(stockItem.inStock?.boolValue ?? false)
                    if unavailable {
                        unavailableCount += 1
                    }
                } else {
                    // No stock item found means unavailable
                    unavailableCount += 1
                }
            } else {
                // No variant means unavailable
                unavailableCount += 1
            }
        }

        return unavailableCount
    }

    deinit {
        // Clean up observer
        NotificationCenter.default.removeObserver(self)
    }
}

// MARK: - UITableViewDataSource & UITableViewDelegate
extension RecipeDetailViewController: UITableViewDataSource, UITableViewDelegate {

    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return products.count
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard let cell = tableView.dequeueReusableCell(withIdentifier: IngredientCell.reuseIdentifier, for: indexPath) as? IngredientCell else {
            return UITableViewCell()
        }

        if let product = products[safe: indexPath.row] {
            cell.configure(with: product, hideActionView: !isStockLocationSelected)
        }
        cell.delegate = self
        return cell
    }

    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return IngredientCell.height
    }
}

// MARK: - IngredientCellDelegate
extension RecipeDetailViewController: IngredientCellDelegate {
    func didTapPlusButton(for product: Product) {
        trackAddToCartEvent(for: product, isAdding: true)
    }
    
    func didTapMinusButton(for product: Product) {
        trackAddToCartEvent(for: product, isAdding: false)
    }
    
    private func trackAddToCartEvent(for product: Product, isAdding: Bool) {
        guard let variant = product.variant(),
              let stockLocation = App.sharedInstance().stockLocation,
              let order = App.sharedInstance().order else { return }
        
        // Find StockItem for tracking
        let stockItem = StockItem.find(by: stockLocation, variant: variant)
        
        if isAdding {
            // Track add to cart
            Tracker.sharedInstance().trackAddToCart(
                withType: "Recipe",
                screen: screenName,
                row: 0,
                productPrice: product.price,
                variant: variant,
                promotionID: NSNumber(value: 0),
                bundleItemCount: 0,
                stockLocation: stockLocation,
                promotionTypeTracking: stockItem?.promotionTypesTracking ?? [],
                isSpecial: (stockItem?.promotionTypesTracking?.count ?? 0) > 0,
                searchProperties: nil,
                position: 0,
                page: 0,
                order: order,
                similarItemPosition: 0,
                oftenBoughtPosition: 0,
                recommendationPosition: 0,
                sourceSKU: nil,
                shoppingListID: recipe?.recipeID,
                newsfeedCode: NSNumber(value: 0),
                isFromDeeplinkCampaignScreen: false,
                recommendationType: nil,
                hasShopperNotes: nil,
                vendorID: stockItem?.vendorID() ?? 0,
                itemStatus: nil,
                itemRemainingCount: nil,
                promoFilter: nil
            )
        } else {
            // Track remove from cart
            Tracker.sharedInstance().trackRemoveFromCart(
                withType: "Recipe",
                screen: screenName,
                taxon: stockItem?.taxonForTracking(),
                row: 0,
                promotionType: stockItem?.promotionTypesTracking ?? [],
                sku: variant.sku,
                name: product.name,
                quantity: 1,
                fromSearch: false,
                isSpecials: stockItem?.isSpecial() ?? false,
                order: order,
                shoppingListID: recipe?.recipeID,
                newsfeedCode: NSNumber(value: 0)
            )
        }
    }
}
