//
//  RecipeDetailViewController.swift
//  HappyFresh
//
//  Created by Augment Agent on 2025-07-07.
//

import UIKit
import SnapKit
import WebKit

class RecipeDetailViewController: BaseViewController {
    
    // MARK: - UI Components
    private let scrollView = UIScrollView()
    private let contentView = UIView()
    private let youtubePlayerView = {
        let configuration = WKWebViewConfiguration()
        configuration.allowsInlineMediaPlayback = true
        configuration.mediaTypesRequiringUserActionForPlayback = []
        configuration.allowsPictureInPictureMediaPlayback = true
        
        let view = WKWebView(frame: .zero, configuration: configuration)
        view.backgroundColor = .black
        view.isUserInteractionEnabled = true
        view.scrollView.isScrollEnabled = false
        view.scrollView.bounces = false
        view.scrollView.isUserInteractionEnabled = true
        return view
    }()
    private let recipeInfoView = UIView()
    private let titleLabel = {
        let label = UILabel()
        label.font = .hf_large()
        label.textColor = .hf_grey_100()
        label.numberOfLines = 0
        return label
    }()
    private let descriptionLabel = UILabel()
    private let infoStackView = UIStackView()
    private let cookingTimeView = RecipeInfoBadgeView()
    private let difficultyView = RecipeInfoBadgeView()
    private let servingView = RecipeInfoBadgeView()
    private let storeSelectionBannerView = StoreSelectionBannerView()
    private let ingredientShowcasesStackView = UIStackView()
    private let loadingIndicator = UIActivityIndicatorView(style: .large)
    
    // MARK: - Properties
    private let recipeID: NSNumber
    private let inspirationsService = InspirationsServices()
    private let storeSelectionRouter = StoreSelectionRouter()
    private var recipe: Recipe?
    private var products: [Product] = []
    private var stockItems: [StockItem] = []
    private var gradientLayer: CAGradientLayer?
    private var isSubtitleExpanded = false

    // Recipe ingredients data
    private var recipeIngredientsResponse: RecipeIngredientsResponse?
    private var ingredientShowcaseViews: [ProductShowcaseView] = []
    private var ingredientShowcaseViewModels: [RecipeIngredientShowcaseViewModel] = []
    
    // Store selection tracking
    private var isWaitingForStoreSelection = false
    
    // MARK: - Initialization
    init(recipeID: NSNumber) {
        self.recipeID = recipeID
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        screenName = ScreenNameInspirationDetail
        setupUI()
        setupConstraints()
        fetchRecipeDetail()
    }
    
    // MARK: - Setup
    private func setupUI() {
        view.backgroundColor = .hf_white()
        
        // Setup scroll view
        scrollView.showsVerticalScrollIndicator = false

        // Setup recipe info view
        setupRecipeInfoView()
        
        // Setup store selection banner
        setupStoreSelectionBanner()

        // Setup ingredient showcases stack view
        setupIngredientShowcasesStackView()

        // Setup loading indicator
        loadingIndicator.color = .gray
        loadingIndicator.hidesWhenStopped = true

        // Add subviews
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
        contentView.addSubview(youtubePlayerView)
        contentView.addSubview(recipeInfoView)
        contentView.addSubview(storeSelectionBannerView)
        contentView.addSubview(ingredientShowcasesStackView)
        view.addSubview(loadingIndicator)
    }

    private func setupVideoPlayer(stringUrl : String?) {
        guard let stringUrl = stringUrl, let videoID = extractVideoID(from: stringUrl) else {
            return
        }
        
        let embedHTML = createYouTubeEmbedHTML(videoID: videoID)
        youtubePlayerView.loadHTMLString(embedHTML, baseURL: nil)
    }

    private func setupRecipeInfoView() {
        // Setup subtitle label
        descriptionLabel.font = UIFont.systemFont(ofSize: 14)
        descriptionLabel.textColor = .gray
        descriptionLabel.numberOfLines = 0

        // Setup info stack view
        infoStackView.axis = .horizontal
        infoStackView.distribution = .fillEqually

        // Add subviews to recipe info view
        recipeInfoView.addSubview(titleLabel)
        recipeInfoView.addSubview(infoStackView)
        recipeInfoView.addSubview(descriptionLabel)

        // Add badge views to stack view with separators
        infoStackView.addArrangedSubview(cookingTimeView)
        infoStackView.addArrangedSubview(servingView)
        infoStackView.addArrangedSubview(difficultyView)
        
        // Setup constraints for recipe info view content
        setupRecipeInfoViewConstraints()
    }

    private func setupRecipeInfoViewConstraints() {
        // Title label constraints
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
        }
        
        // Info stack view constraints
        infoStackView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(16)
            make.left.equalToSuperview().inset(16)
            make.right.equalToSuperview().inset(16)
            make.height.equalTo(40)
        }

        // Subtitle label constraints
        descriptionLabel.snp.makeConstraints { make in
            make.top.equalTo(infoStackView.snp.bottom).offset(16)
            make.left.right.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().inset(16)
        }
    }
    
    private func setupStoreSelectionBanner() {
        storeSelectionBannerView.delegate = self
        storeSelectionBannerView.configure(with: App.sharedInstance().stockLocation)
    }

    private func setupIngredientShowcasesStackView() {
        ingredientShowcasesStackView.axis = .vertical
        ingredientShowcasesStackView.spacing = 0
        ingredientShowcasesStackView.alignment = .fill
        ingredientShowcasesStackView.distribution = .fill
    }

    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    
        // Content view constraints
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }
    
        // YouTube player constraints (replacing header image)
        youtubePlayerView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(300)
        }
    
        // Recipe info view constraints
        recipeInfoView.snp.makeConstraints { make in
            make.top.equalTo(youtubePlayerView.snp.bottom)
            make.left.right.equalToSuperview()
        }

        // Store selection banner view constraints
        storeSelectionBannerView.snp.makeConstraints { make in
            make.top.equalTo(recipeInfoView.snp.bottom)
            make.left.right.equalToSuperview()
        }

        // Ingredient showcases stack view constraints
        ingredientShowcasesStackView.snp.makeConstraints { make in
            make.top.equalTo(storeSelectionBannerView.snp.bottom)
            make.left.right.equalToSuperview()
            make.bottom.equalToSuperview().offset(-16)
        }
    
        // Loading indicator constraints
        loadingIndicator.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }
    }

    // MARK: - Data Fetching
    private func fetchRecipeDetail() {
        showLoading()

        let currentStockLocation = App.sharedInstance().stockLocation
        inspirationsService.getInspirationDetail(withInspirationID: recipeID,
                                                 stockLocation: currentStockLocation,
                                                 success: { [weak self] operation, recipe, products in
            DispatchQueue.main.async {
                guard let self = self else { return }
                self.hideLoading()
                self.recipe = recipe
                self.products = products
                self.updateUI()

                // Fetch recipe ingredients if we have a stock location
                if let stockLocation = currentStockLocation {
                    self.rightBarButtonItemTypes = .cart
                    self.fetchRecipeIngredients(stockLocation: stockLocation)
                }
            }
        }, failure: { [weak self] operation, error in
            DispatchQueue.main.async {
                self?.loadingIndicator.stopAnimating()
                Popup.showErrorMessage(fromResponse: operation)
            }
        })
    }

    private func fetchRecipeIngredients(stockLocation: StockLocation) {
        print("🔍 Fetching recipe ingredients for recipe \(recipeID) at stock location \(stockLocation.stockLocationID ?? 0)")



        inspirationsService.getRecipeIngredients(stockLocation : stockLocation,
                                                 recipeID: recipeID,
                                                 success: { [weak self] ingredientsResponse in
            DispatchQueue.main.async {
                guard let self = self else { return }
                print("✅ Successfully fetched \(ingredientsResponse.ingredients.count) recipe ingredients")
                self.recipeIngredientsResponse = ingredientsResponse
                self.setupIngredientShowcases()
            }
        }, failure: { operation, error in
            DispatchQueue.main.async {
                print("❌ Failed to fetch recipe ingredients: \(error.localizedDescription)")
                // Don't show error to user, just continue without ingredient showcases
            }
        })
    }
    
    private func showLoading() {
        loadingIndicator.startAnimating()
        contentView.isHidden = true
    }
    
    private func hideLoading() {
        self.loadingIndicator.stopAnimating()
        contentView.isHidden = false
    }

    private func updateUI() {
        guard let recipe = recipe else { return }

        // Update title and subtitle
        title = recipe.name
        titleLabel.text = recipe.name
        descriptionLabel.html(recipe.displayInstructions, fontSize: 12)

        // Update info badge views
        cookingTimeView.configure(icon: "hf_icon_recipe_time", title: recipe.displayTimeToCook)
        servingView.configure(icon: "hf_icon_recipe_smile", title: recipe.displayServingPortion)
        difficultyView.configure(icon: "hf_icon_recipe_serving", title: recipe.displayDifficulty)

        setupVideoPlayer(stringUrl: recipe.link)
    }

    private func setupIngredientShowcases() {
        guard let ingredientsResponse = recipeIngredientsResponse,
              let stockLocation = App.sharedInstance().stockLocation else {
            return
        }

        // Clear existing showcases
        clearIngredientShowcases()

        // Create showcases for each ingredient that has products
        let ingredientsWithProducts = ingredientsResponse.ingredients.filter { !$0.products.isEmpty }

        guard !ingredientsWithProducts.isEmpty else {
            print("ℹ️ No ingredients with products found")
            return
        }

        print("🎯 Setting up \(ingredientsWithProducts.count) ingredient showcases")

        for ingredient in ingredientsWithProducts {
            // Create view model
            let viewModel = RecipeIngredientShowcaseViewModel(recipeIngredient: ingredient, stockLocation: stockLocation)
            ingredientShowcaseViewModels.append(viewModel)

            // Create showcase view
            let showcaseView = ProductShowcaseView()
            showcaseView.screenName = screenName
            showcaseView.shouldHideOverlayButton = true

            // Set up product tap handler
            showcaseView.productTappedBlock = { [weak self] stockItem, recommendationType, position in
                guard let self = self else { return }
                self.openProductDetail(stockItem: stockItem, recommendationType: recommendationType, position: position)
            }

            // Set view model (this will trigger fetchProduct)
            showcaseView.viewModel = viewModel

            // Add to stack view (much simpler than manual constraints!)
            ingredientShowcasesStackView.addArrangedSubview(showcaseView)
            ingredientShowcaseViews.append(showcaseView)
        }

        // Force layout update
        view.layoutIfNeeded()
    }

    private func clearIngredientShowcases() {
        // Remove existing views from stack view
        for showcaseView in ingredientShowcaseViews {
            ingredientShowcasesStackView.removeArrangedSubview(showcaseView)
            showcaseView.removeFromSuperview()
        }
        ingredientShowcaseViews.removeAll()
        ingredientShowcaseViewModels.removeAll()
    }

    private func openProductDetail(stockItem: StockItem, recommendationType: String, position: Int) {
        guard let stockLocation = App.sharedInstance().stockLocation,
              let order = App.sharedInstance().order else {
            return
        }

        // Create product detail view model
        let productDetailViewModel = ProductDetailViewModel(
            order: order,
            stockLocation: stockLocation,
            stockItem: stockItem,
            position: position,
            isFromGlobalSearchScreen: false,
            isFromGlobalCategoryScreen: false,
            trackingType: recommendationType,
            source: screenName
        )

        // Create and push product detail view controller
        let productDetailViewController = ProductDetailViewController(productDetailViewModel)
        navigationController?.pushViewController(productDetailViewController, animated: true)
    }

    private func showErrorAlert(message: String) {
        let alert = UIAlertController(title: "Error", message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "OK", style: .default))
        present(alert, animated: true)
    }

    private func showLocationPrompt() {
        // Show location prompt similar to HomeViewController
        let alert = UIAlertController(
            title: "Location Required",
            message: "Please set your delivery location first to select a store.",
            preferredStyle: .alert
        )

        alert.addAction(UIAlertAction(title: "Set Location", style: .default) { _ in
            // Navigate to location setting
            App.sharedInstance().goToGlobalHomeTab {
                // This will trigger location setting flow
            }
        })

        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))

        present(alert, animated: true)
    }

    private func showStoreSelection(deliveryAddress: Address) {
        // Set up custom completion handling by observing stock location changes
        setupStockLocationObserver()

        // Present store selection without going to store home
        storeSelectionRouter.present(
            from: self,
            campaignFromDeeplink: nil,
            showCloseButton: true,
            popToRootAfterDismiss: false,
            fromFindStore: false,
            showStoreHome: false, // Don't go to store home
            deliveryAddress: deliveryAddress,
            source: screenName
        )
    }

    private func setupStockLocationObserver() {
        // Listen for stock location changes using the proper notification
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(stockLocationDidChange(_:)),
            name: NSNotification.Name.StockLocationChanged,
            object: nil
        )
    }

    @objc private func stockLocationDidChange(_ notification: Notification) {
        // Remove observer
        NotificationCenter.default.removeObserver(self, name: NSNotification.Name.StockLocationChanged, object: nil)
        
        // Dismiss store selection
        storeSelectionRouter.dismiss()
        
        // Get the selected stock location
        if let stockLocation = App.sharedInstance().stockLocation {
            self.rightBarButtonItemTypes = .cart
            // Update the banner with the selected store
            storeSelectionBannerView.configure(with: stockLocation)
            // Fetch ingredients for the new stock location
            fetchRecipeIngredients(stockLocation: stockLocation)
        }
    }
    
    deinit {
        // Clean up observer
        NotificationCenter.default.removeObserver(self)
    }
}

// MARK: - StoreSelectionBannerViewDelegate
extension RecipeDetailViewController: StoreSelectionBannerViewDelegate {
    func storeSelectionBannerDidTapChangeStore() {
        // Check if we have an address first
        guard let address = App.sharedInstance().address else {
            showLocationPrompt()
            return
        }

        // Show store selection
        showStoreSelection(deliveryAddress: address)
    }
}

extension RecipeDetailViewController {
    private func extractVideoID(from urlString: String) -> String? {
        // Handle different YouTube URL formats
        if let url = URL(string: urlString) {
            // For youtu.be format
            if url.host == "youtu.be" {
                return String(url.path.dropFirst()) // Remove leading "/"
            }
            
            // For youtube.com format
            if url.host?.contains("youtube.com") == true {
                // Handle YouTube Shorts format: /shorts/VIDEO_ID
                if url.path.hasPrefix("/shorts/") {
                    return String(url.path.dropFirst(8)) // Remove "/shorts/"
                }
                
                // Handle regular YouTube format with v parameter
                let components = URLComponents(url: url, resolvingAgainstBaseURL: false)
                return components?.queryItems?.first(where: { $0.name == "v" })?.value
            }
        }
        
        return nil
    }
    
    private func createYouTubeEmbedHTML(videoID: String) -> String {
        return """
        <!DOCTYPE html>
        <html>
        <head>
            <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
            <style>
                * {
                    margin: 0;
                    padding: 0;
                    box-sizing: border-box;
                }
                body {
                    background-color: #000;
                    overflow: hidden;
                    touch-action: auto;
                }
                .video-container {
                    position: relative;
                    width: 100vw;
                    height: 100vh;
                    background-color: #000;
                }
                iframe {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    border: none;
                    touch-action: auto;
                }
            </style>
        </head>
        <body>
            <div class="video-container">
                <iframe 
                    id="youtube-player"
                    src="https://www.youtube.com/embed/\(videoID)?autoplay=0&mute=0&controls=1&rel=0&modestbranding=1&playsinline=0&fs=1&origin=\(Bundle.main.bundleIdentifier ?? "com.happyfresh.HappyFresh")"
                    frameborder="0"
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share; fullscreen"
                    allowfullscreen
                    webkitallowfullscreen
                    mozallowfullscreen>
                </iframe>
            </div>
            <script>
                // Simple click handler for fullscreen
                document.addEventListener('DOMContentLoaded', function() {
                    var iframe = document.getElementById('youtube-player');
                    if (iframe) {
                        iframe.style.pointerEvents = 'auto';
                        
                        // Listen for messages from YouTube iframe
                        window.addEventListener('message', function(event) {
                            if (event.origin !== 'https://www.youtube.com') return;
                            
                            // Check if video is playing and trigger fullscreen
                            if (event.data && typeof event.data === 'string') {
                                try {
                                    var data = JSON.parse(event.data);
                                    if (data.event === 'video-progress' && data.info && data.info.currentTime > 0) {
                                        requestFullscreen();
                                    }
                                } catch (e) {
                                    // Ignore parsing errors
                                }
                            }
                        });
                    }
                });

                function requestFullscreen() {
                    var iframe = document.getElementById('youtube-player');
                    if (iframe) {
                        if (iframe.requestFullscreen) {
                            iframe.requestFullscreen();
                        } else if (iframe.webkitRequestFullscreen) {
                            iframe.webkitRequestFullscreen();
                        } else if (iframe.mozRequestFullScreen) {
                            iframe.mozRequestFullScreen();
                        } else if (iframe.msRequestFullscreen) {
                            iframe.msRequestFullscreen();
                        }
                    }
                }
            </script>
        </body>
        </html>
        """
    }
}
