//
//  RecipeDetailViewController.swift
//  HappyFresh
//
//  Created by Augment Agent on 2025-07-07.
//

import UIKit
import SnapKit

class RecipeDetailViewController: BaseViewController {
    
    // MARK: - UI Components
    private let scrollView = UIScrollView()
    private let contentView = UIView()
    private let headerImageView = UIImageView()
    private let recipeInfoView = UIView()
    private let titleLabel = UILabel()
    private let subtitleLabel = UILabel()
    private let readMoreButton = UIButton()
    private let infoStackView = UIStackView()
    private let cookingTimeView = RecipeInfoBadgeView()
    private let difficultyView = RecipeInfoBadgeView()
    private let servingView = RecipeInfoBadgeView()
    private let ingredientsHeaderLabel = UILabel()
    private let ingredientsTableView = UITableView()
    private let addToBasketButton: UIButton = {
        let button = HappyButton(title: NSLocalizedString("Add to basket", comment: ""),
                                accentColor: UIColor.hf_prime(),
                                type: HappyButton.HappyButtonType.Primary)
        return button
    }()
    private let loadingIndicator = UIActivityIndicatorView(style: .large)
    
    // MARK: - Properties
    private let recipeID: NSNumber
    private let inspirationsService = InspirationsServices()
    private let storeSelectionRouter = StoreSelectionRouter()
    private var recipe: Recipe?
    private var products: [Product] = []
    private var stockItems: [StockItem] = []
    private var gradientLayer: CAGradientLayer?
    private var isStockLocationSelected = false
    private var isSubtitleExpanded = false
    
    // MARK: - Initialization
    init(recipeID: NSNumber) {
        self.recipeID = recipeID
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        fetchRecipeDetail()
    }
    
    // MARK: - Setup
    private func setupUI() {
        view.backgroundColor = .hf_white()
        
        // Setup scroll view
        scrollView.showsVerticalScrollIndicator = false
        
        // Setup header image
        headerImageView.contentMode = .scaleAspectFill
        headerImageView.clipsToBounds = true
        headerImageView.backgroundColor = .lightGray
        
        // Setup recipe info view
        setupRecipeInfoView()
        
        // Setup ingredients header
        ingredientsHeaderLabel.text = "Ingredients"
        ingredientsHeaderLabel.font = UIFont.boldSystemFont(ofSize: 18)
        ingredientsHeaderLabel.textColor = .black
        
        // Setup ingredients table view
        setupIngredientsTableView()
        
        // Setup add to basket button
        setupAddToBasketButton()
        
        // Setup loading indicator
        loadingIndicator.color = .gray
        loadingIndicator.hidesWhenStopped = true
        
        // Add subviews
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
        contentView.addSubview(headerImageView)
        contentView.addSubview(recipeInfoView)
        contentView.addSubview(ingredientsHeaderLabel)
        contentView.addSubview(ingredientsTableView)
        view.addSubview(addToBasketButton)
        view.addSubview(loadingIndicator)
    }
    
    private func setupRecipeInfoView() {
        // Setup title label
        titleLabel.font = UIFont.boldSystemFont(ofSize: 20)
        titleLabel.textColor = .black
        titleLabel.numberOfLines = 0

        // Setup subtitle label
        subtitleLabel.font = UIFont.systemFont(ofSize: 14)
        subtitleLabel.textColor = .gray
        subtitleLabel.numberOfLines = 2 // Initially limited to 2 lines

        // Setup read more button
        readMoreButton.setTitle("Read more", for: .normal)
        readMoreButton.setTitleColor(.systemBlue, for: .normal)
        readMoreButton.titleLabel?.font = UIFont.systemFont(ofSize: 14)
        readMoreButton.addTarget(self, action: #selector(readMoreButtonTapped), for: .touchUpInside)

        // Setup info stack view
        infoStackView.axis = .horizontal
        infoStackView.distribution = .fillEqually

        // Setup info badge views
        cookingTimeView.configure(icon: "clock", title: "30 mins")
        difficultyView.configure(icon: "star", title: "Easy")
        servingView.configure(icon: "person.2", title: "2 People")

        // Add subviews to recipe info view
        recipeInfoView.addSubview(titleLabel)
        recipeInfoView.addSubview(infoStackView)
        recipeInfoView.addSubview(subtitleLabel)
        recipeInfoView.addSubview(readMoreButton)

        // Add badge views to stack view
        infoStackView.addArrangedSubview(cookingTimeView)
        infoStackView.addArrangedSubview(difficultyView)
        infoStackView.addArrangedSubview(servingView)

        // Setup constraints for recipe info view content
        setupRecipeInfoViewConstraints()
    }

    private func setupRecipeInfoViewConstraints() {
        // Title label constraints
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
        }
        
        // Info stack view constraints
        infoStackView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom)
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
            make.height.equalTo(40)
        }

        // Subtitle label constraints
        subtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(infoStackView.snp.bottom)
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
        }

        // Read more button constraints
        readMoreButton.snp.makeConstraints { make in
            make.top.equalTo(subtitleLabel.snp.bottom).offset(4)
            make.left.equalToSuperview().offset(16)
            make.height.equalTo(20)
            make.bottom.equalToSuperview().offset(-16)
        }
    }
    
    private func setupIngredientsTableView() {
        ingredientsTableView.delegate = self
        ingredientsTableView.dataSource = self
        ingredientsTableView.separatorStyle = .singleLine
        ingredientsTableView.isScrollEnabled = false // Keep this false since it's inside scroll view
        ingredientsTableView.backgroundColor = .clear
        ingredientsTableView.register(IngredientCell.self, forCellReuseIdentifier: IngredientCell.reuseIdentifier)
    }
    
    private func setupAddToBasketButton() {
        addToBasketButton.addTarget(self, action: #selector(addToBasketButtonTapped), for: .touchUpInside)
    }

    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.bottom.equalTo(addToBasketButton.snp.top).offset(-16)
        }
    
        // Content view constraints
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }
    
        // Header image constraints
        headerImageView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(300)
        }
    
        // Recipe info view constraints
        recipeInfoView.snp.makeConstraints { make in
            make.top.equalTo(headerImageView.snp.bottom)
            make.left.right.equalToSuperview()
        }
    
        // Ingredients header constraints
        ingredientsHeaderLabel.snp.makeConstraints { make in
            make.top.equalTo(recipeInfoView.snp.bottom).offset(24)
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
        }
    
        // Ingredients table view constraints - Use dynamic height
        ingredientsTableView.snp.makeConstraints { make in
            make.top.equalTo(ingredientsHeaderLabel.snp.bottom).offset(16)
            make.left.right.equalToSuperview()
            make.height.equalTo(0) // Will be updated dynamically
            make.bottom.equalToSuperview().offset(-16) // This defines the content view height
        }
        
        // Add to basket button - Fixed at bottom
        addToBasketButton.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(50)
            make.bottom.equalTo(view.safeAreaLayoutGuide.snp.bottom)
        }
    
        // Loading indicator constraints
        loadingIndicator.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }
    }

    // MARK: - Data Fetching
    private func fetchRecipeDetail(stockLocation: StockLocation? = nil) {
        showLoading()

        inspirationsService.getInspirationDetail(withInspirationID: recipeID,
                                                 stockLocation: stockLocation,
                                                 success: { [weak self] operation, recipe, products in
            DispatchQueue.main.async {
                guard let self = self else { return }
                self.hideLoading()
                self.recipe = recipe
                self.products = products
                self.updateUI()
                self.updateTableViewHeight(totalProducts: products.count)
            }
        }, failure: { [weak self] operation, error in
            DispatchQueue.main.async {
                self?.loadingIndicator.stopAnimating()
                Popup.showErrorMessage(fromResponse: operation)
            }
        })
    }
    
    private func showLoading() {
        loadingIndicator.startAnimating()
        contentView.isHidden = true
    }
    
    private func hideLoading() {
        self.loadingIndicator.stopAnimating()
        contentView.isHidden = false
    }

    private func updateUI() {
        guard let recipe = recipe else { return }

        // Update title and subtitle
        title = recipe.name
        titleLabel.text = recipe.name
        subtitleLabel.text = "Rekomendasi bekal untuk anak bisa buat frozen juga buat stock di freezer juga gampang, saus keju, saus tomat, bumbu bawang putih & bawang putih... Read more"

        // Update info badge views
        cookingTimeView.configure(icon: "clock", title: recipe.displayTimeToCook ?? "30 mins")
        difficultyView.configure(icon: "star", title: recipe.displayDifficulty ?? "Easy")
        servingView.configure(icon: "person.2", title: recipe.displayServingPortion ?? "2 People")

        // Load recipe image
        if let imageURL = recipe.photoDefaultURL ?? recipe.photoMediumURL,
           let url = URL(string: imageURL) {
            let placeHolder = UIImage.add_imageNamed("hf_img_product_placeholder_600px")
            headerImageView.add_setImage(with: url, placeholderImage: placeHolder)
        }

        // Reload ingredients table
        ingredientsTableView.reloadData()
    }

    private func updateTableViewHeight(totalProducts: Int) {
        let height = IngredientCell.height * CGFloat(totalProducts)
        ingredientsTableView.snp.updateConstraints { make in
            make.height.equalTo(height)
        }
        
        // Force layout update to ensure scroll view content size is recalculated
        view.layoutIfNeeded()
    }

    private func showErrorAlert(message: String) {
        let alert = UIAlertController(title: "Error", message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "OK", style: .default))
        present(alert, animated: true)
    }

    @objc private func readMoreButtonTapped() {
        isSubtitleExpanded.toggle()

        UIView.animate(withDuration: 0.3) {
            if self.isSubtitleExpanded {
                self.subtitleLabel.numberOfLines = 0
                self.readMoreButton.setTitle("Read less", for: .normal)
            } else {
                self.subtitleLabel.numberOfLines = 2
                self.readMoreButton.setTitle("Read more", for: .normal)
            }
            self.view.layoutIfNeeded()
        }
    }

    @objc func addToBasketButtonTapped() {
        print("🛒 Add to basket button tapped")

        // Check if we have an address first
        guard let address = App.sharedInstance().address else {
            print("❌ No address found, need to set location first")
            showLocationPrompt()
            return
        }

        // Show store selection
        showStoreSelection(deliveryAddress: address)
    }

    private func showLocationPrompt() {
        // Show location prompt similar to HomeViewController
        let alert = UIAlertController(
            title: "Location Required",
            message: "Please set your delivery location first to select a store.",
            preferredStyle: .alert
        )

        alert.addAction(UIAlertAction(title: "Set Location", style: .default) { _ in
            // Navigate to location setting
            App.sharedInstance().goToGlobalHomeTab {
                // This will trigger location setting flow
            }
        })

        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))

        present(alert, animated: true)
    }

    private func showStoreSelection(deliveryAddress: Address) {
        // Set up custom completion handling by observing stock location changes
        setupStockLocationObserver()

        // Present store selection without going to store home
        storeSelectionRouter.present(
            from: self,
            campaignFromDeeplink: nil,
            showCloseButton: true,
            popToRootAfterDismiss: false,
            fromFindStore: false,
            showStoreHome: false, // Don't go to store home
            deliveryAddress: deliveryAddress,
            source: screenName
        )
    }

    private func setupStockLocationObserver() {
        // Listen for stock location changes using the proper notification
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(stockLocationDidChange(_:)),
            name: NSNotification.Name.StockLocationChanged,
            object: nil
        )
    }

    @objc private func stockLocationDidChange(_ notification: Notification) {
        // Remove observer
        NotificationCenter.default.removeObserver(self, name: NSNotification.Name.StockLocationChanged, object: nil)
        
        // Dismiss store selection
        storeSelectionRouter.dismiss()
        
        // Get the selected stock location
        if let stockLocation = App.sharedInstance().stockLocation {
            isStockLocationSelected = true
            rightBarButtonItemTypes = .cart
            handleStockLocationSelected(stockLocation)
        }
    }

    private func handleStockLocationSelected(_ stockLocation: StockLocation) {
        fetchRecipeDetail(stockLocation: stockLocation)
    }

    deinit {
        // Clean up observer
        NotificationCenter.default.removeObserver(self)
    }
}

// MARK: - UITableViewDataSource & UITableViewDelegate
extension RecipeDetailViewController: UITableViewDataSource, UITableViewDelegate {

    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return products.count
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard let cell = tableView.dequeueReusableCell(withIdentifier: IngredientCell.reuseIdentifier, for: indexPath) as? IngredientCell else {
            return UITableViewCell()
        }

        if let product = products[safe: indexPath.row] {
            cell.configure(with: product, hideActionView: !isStockLocationSelected)
        }
        cell.delegate = self
        return cell
    }

    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return IngredientCell.height
    }
}

// MARK: - IngredientCellDelegate
extension RecipeDetailViewController: IngredientCellDelegate {
    func didTapPlusButton(for product: Product) {
        trackAddToCartEvent(for: product, isAdding: true)
    }
    
    func didTapMinusButton(for product: Product) {
        trackAddToCartEvent(for: product, isAdding: false)
    }
    
    private func trackAddToCartEvent(for product: Product, isAdding: Bool) {
        guard let variant = product.variant(),
              let stockLocation = App.sharedInstance().stockLocation,
              let order = App.sharedInstance().order else { return }
        
        // Find StockItem for tracking
        let stockItem = StockItem.find(by: stockLocation, variant: variant)
        
        if isAdding {
            // Track add to cart
            Tracker.sharedInstance().trackAddToCart(
                withType: "Recipe",
                screen: screenName,
                row: 0,
                productPrice: product.price,
                variant: variant,
                promotionID: NSNumber(value: 0),
                bundleItemCount: 0,
                stockLocation: stockLocation,
                promotionTypeTracking: stockItem?.promotionTypesTracking ?? [],
                isSpecial: (stockItem?.promotionTypesTracking?.count ?? 0) > 0,
                searchProperties: nil,
                position: 0,
                page: 0,
                order: order,
                similarItemPosition: 0,
                oftenBoughtPosition: 0,
                recommendationPosition: 0,
                sourceSKU: nil,
                shoppingListID: recipe?.recipeID,
                newsfeedCode: NSNumber(value: 0),
                isFromDeeplinkCampaignScreen: false,
                recommendationType: nil,
                hasShopperNotes: nil,
                vendorID: stockItem?.vendorID() ?? 0,
                itemStatus: nil,
                itemRemainingCount: nil,
                promoFilter: nil
            )
        } else {
            // Track remove from cart
            Tracker.sharedInstance().trackRemoveFromCart(
                withType: "Recipe",
                screen: screenName,
                taxon: stockItem?.taxonForTracking(),
                row: 0,
                promotionType: stockItem?.promotionTypesTracking ?? [],
                sku: variant.sku,
                name: product.name,
                quantity: 1,
                fromSearch: false,
                isSpecials: stockItem?.isSpecial() ?? false,
                order: order,
                shoppingListID: recipe?.recipeID,
                newsfeedCode: NSNumber(value: 0)
            )
        }
    }
}
