//
//  RecipeDetailViewController.swift
//  HappyFresh
//
//  Created by Augment Agent on 2025-07-07.
//

import UIKit
import SnapKit

class RecipeDetailViewController: BaseViewController {
    
    // MARK: - UI Components
    private let scrollView = UIScrollView()
    private let contentView = UIView()
    private let headerImageView = UIImageView()
    private let recipeInfoView = UIView()
    private let titleLabel = UILabel()
    private let subtitleLabel = UILabel()
    private let readMoreButton = UIButton()
    private let infoStackView = UIStackView()
    private let cookingTimeView = RecipeInfoBadgeView()
    private let difficultyView = RecipeInfoBadgeView()
    private let servingView = RecipeInfoBadgeView()
    private let ingredientsHeaderLabel = UILabel()
    private let ingredientsTableView = UITableView()
    private let addToBasketButton: UIButton = {
        let button = HappyButton(title: NSLocalizedString("Add to basket", comment: ""),
                                accentColor: UIColor.hf_prime(),
                                type: HappyButton.HappyButtonType.Primary)
        return button
    }()
    private let loadingIndicator = UIActivityIndicatorView(style: .large)
    
    // MARK: - Properties
    private let recipeID: NSNumber
    private let inspirationsService = InspirationsServices()
    private var recipe: Recipe?
    private var products: [Product] = []
    private var stockItems: [StockItem] = []
    private var gradientLayer: CAGradientLayer?
    private var isSubtitleExpanded = false
    
    // MARK: - Initialization
    init(recipeID: NSNumber) {
        self.recipeID = recipeID
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        fetchRecipeDetail()
    }
    
    // MARK: - Setup
    private func setupUI() {
        view.backgroundColor = .hf_white()
        rightBarButtonItemTypes = .cart
        
        // Setup scroll view
        scrollView.showsVerticalScrollIndicator = false
        
        // Setup header image
        headerImageView.contentMode = .scaleAspectFill
        headerImageView.clipsToBounds = true
        headerImageView.backgroundColor = .lightGray
        
        // Setup recipe info view
        setupRecipeInfoView()
        
        // Setup ingredients header
        ingredientsHeaderLabel.text = "Ingredients"
        ingredientsHeaderLabel.font = UIFont.boldSystemFont(ofSize: 18)
        ingredientsHeaderLabel.textColor = .black
        
        // Setup ingredients table view
        setupIngredientsTableView()
        
        // Setup add to basket button
        setupAddToBasketButton()
        
        // Setup loading indicator
        loadingIndicator.color = .gray
        loadingIndicator.hidesWhenStopped = true
        
        // Add subviews
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
        contentView.addSubview(headerImageView)
        contentView.addSubview(recipeInfoView)
        contentView.addSubview(ingredientsHeaderLabel)
        contentView.addSubview(ingredientsTableView)
        view.addSubview(addToBasketButton)
        view.addSubview(loadingIndicator)
    }
    
    private func setupRecipeInfoView() {
        // Setup title label
        titleLabel.font = UIFont.boldSystemFont(ofSize: 20)
        titleLabel.textColor = .black
        titleLabel.numberOfLines = 0

        // Setup subtitle label
        subtitleLabel.font = UIFont.systemFont(ofSize: 14)
        subtitleLabel.textColor = .gray
        subtitleLabel.numberOfLines = 2 // Initially limited to 2 lines

        // Setup read more button
        readMoreButton.setTitle("Read more", for: .normal)
        readMoreButton.setTitleColor(.systemBlue, for: .normal)
        readMoreButton.titleLabel?.font = UIFont.systemFont(ofSize: 14)
        readMoreButton.addTarget(self, action: #selector(readMoreButtonTapped), for: .touchUpInside)

        // Setup info stack view
        infoStackView.axis = .horizontal
        infoStackView.distribution = .fillEqually

        // Setup info badge views
        cookingTimeView.configure(icon: "clock", title: "30 mins")
        difficultyView.configure(icon: "star", title: "Easy")
        servingView.configure(icon: "person.2", title: "2 People")

        // Add subviews to recipe info view
        recipeInfoView.addSubview(titleLabel)
        recipeInfoView.addSubview(infoStackView)
        recipeInfoView.addSubview(subtitleLabel)
        recipeInfoView.addSubview(readMoreButton)

        // Add badge views to stack view
        infoStackView.addArrangedSubview(cookingTimeView)
        infoStackView.addArrangedSubview(difficultyView)
        infoStackView.addArrangedSubview(servingView)

        // Setup constraints for recipe info view content
        setupRecipeInfoViewConstraints()
    }

    private func setupRecipeInfoViewConstraints() {
        // Title label constraints
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
        }
        
        // Info stack view constraints
        infoStackView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom)
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
            make.height.equalTo(40)
        }

        // Subtitle label constraints
        subtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(infoStackView.snp.bottom)
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
        }

        // Read more button constraints
        readMoreButton.snp.makeConstraints { make in
            make.top.equalTo(subtitleLabel.snp.bottom).offset(4)
            make.left.equalToSuperview().offset(16)
            make.height.equalTo(20)
            make.bottom.equalToSuperview().offset(-16)
        }
    }
    
    private func setupIngredientsTableView() {
        ingredientsTableView.delegate = self
        ingredientsTableView.dataSource = self
        ingredientsTableView.separatorStyle = .singleLine
        ingredientsTableView.isScrollEnabled = false
        ingredientsTableView.backgroundColor = .clear
        ingredientsTableView.register(IngredientCell.self, forCellReuseIdentifier: IngredientCell.reuseIdentifier)
    }
    
    private func setupAddToBasketButton() {
        addToBasketButton.addTarget(self, action: #selector(addToBasketButtonTapped), for: .touchUpInside)
    }

    private func setupConstraints() {
        // Scroll view constraints
        scrollView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        // Content view constraints
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }

        // Header image constraints
        headerImageView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(300)
        }

        // Recipe info view constraints
        recipeInfoView.snp.makeConstraints { make in
            make.top.equalTo(headerImageView.snp.bottom)
            make.left.right.equalToSuperview()
        }

        // Ingredients header constraints
        ingredientsHeaderLabel.snp.makeConstraints { make in
            make.top.equalTo(recipeInfoView.snp.bottom).offset(24)
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
        }

        // Ingredients table view constraints
        ingredientsTableView.snp.makeConstraints { make in
            make.top.equalTo(ingredientsHeaderLabel.snp.bottom).offset(16)
            make.left.right.equalToSuperview()
            make.height.equalTo(0) // Will be updated dynamically
        }
        
        addToBasketButton.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(50)
            make.bottom.equalTo(view.safeAreaLayoutGuide.snp.bottom)
        }

        // Loading indicator constraints
        loadingIndicator.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }
    }

    // MARK: - Data Fetching
    private func fetchRecipeDetail() {
        loadingIndicator.startAnimating()
        
        let stockLocation = App.sharedInstance().order?.currentStockLocation()
        inspirationsService.getInspirationDetail(withInspirationID: recipeID,
                                                 stockLocation: stockLocation,
                                                 success: { [weak self] operation, recipe, products in
            DispatchQueue.main.async {
                guard let self = self else { return }
                self.loadingIndicator.stopAnimating()
                self.recipe = recipe
                self.updateUI()
                self.products = products
                self.updateTableViewHeight(totalProducts: products.count)
            }
        }, failure: { [weak self] operation, error in
            DispatchQueue.main.async {
                self?.loadingIndicator.stopAnimating()
                Popup.showErrorMessage(fromResponse: operation)
            }
        })
    }

    private func updateUI() {
        guard let recipe = recipe else { return }

        // Update title and subtitle
        title = recipe.name
        titleLabel.text = recipe.name
        subtitleLabel.text = "Rekomendasi bekal untuk anak bisa buat frozen juga buat stock di freezer juga gampang, saus keju, saus tomat, bumbu bawang putih & bawang putih... Read more"

        // Update info badge views
        cookingTimeView.configure(icon: "clock", title: recipe.displayTimeToCook ?? "30 mins")
        difficultyView.configure(icon: "star", title: recipe.displayDifficulty ?? "Easy")
        servingView.configure(icon: "person.2", title: recipe.displayServingPortion ?? "2 People")

        // Load recipe image
        if let imageURL = recipe.photoDefaultURL ?? recipe.photoMediumURL {
            loadImage(from: imageURL)
        }

        // Reload ingredients table
        ingredientsTableView.reloadData()
    }

    private func loadImage(from urlString: String) {
        guard let url = URL(string: urlString) else { return }

        // Simple image loading - in production you'd use a proper image loading library
        URLSession.shared.dataTask(with: url) { [weak self] data, response, error in
            guard let data = data, let image = UIImage(data: data) else { return }

            DispatchQueue.main.async {
                self?.headerImageView.image = image
            }
        }.resume()
    }

    private func updateTableViewHeight(totalProducts: Int) {
        let height = IngredientCell.height * CGFloat(totalProducts)
        ingredientsTableView.snp.updateConstraints { make in
            make.height.equalTo(height)
        }
    }

    private func showErrorAlert(message: String) {
        let alert = UIAlertController(title: "Error", message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "OK", style: .default))
        present(alert, animated: true)
    }

    @objc private func readMoreButtonTapped() {
        isSubtitleExpanded.toggle()

        UIView.animate(withDuration: 0.3) {
            if self.isSubtitleExpanded {
                self.subtitleLabel.numberOfLines = 0
                self.readMoreButton.setTitle("Read less", for: .normal)
            } else {
                self.subtitleLabel.numberOfLines = 2
                self.readMoreButton.setTitle("Read more", for: .normal)
            }
            self.view.layoutIfNeeded()
        }
    }

    @objc func addToBasketButtonTapped() {
        guard let recipeID = recipe?.recipeID else { return }
        let deeplinkUrl = "happyfresh-stage://happyfresh.com/?deeplink_type=inspiration_detail&shopping_list_id=\(recipeID)"
        if let encodedUrl = deeplinkUrl.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed),
           let url = URL(string: encodedUrl) {
            DeeplinkUtils.sharedInstance().open(url)
        }
    }
}

// MARK: - UITableViewDataSource & UITableViewDelegate
extension RecipeDetailViewController: UITableViewDataSource, UITableViewDelegate {

    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return products.count
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard let cell = tableView.dequeueReusableCell(withIdentifier: IngredientCell.reuseIdentifier, for: indexPath) as? IngredientCell else {
            return UITableViewCell()
        }

        let product = products[indexPath.row]
        cell.configure(with: product)
        cell.delegate = self

        return cell
    }

    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return IngredientCell.height
    }
}

// MARK: - IngredientCellDelegate
extension RecipeDetailViewController: IngredientCellDelegate {
    func didTapAddButton(for product: Product) {
        print("🛒 Adding product to cart: \(product.name ?? "Unknown")")
        // Implement add to cart logic here
    }
}
