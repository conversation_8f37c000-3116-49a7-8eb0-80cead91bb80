//
//  RecipeDetailViewController.swift
//  HappyFresh
//
//  Created by Augment Agent on 2025-07-07.
//

import UIKit
import SnapKit
import youtube_ios_player_helper

class RecipeDetailViewController: BaseViewController {
    
    // MARK: - UI Components
    private let scrollView = UIScrollView()
    private let contentView = UIView()
    private let youtubePlayerView: YTPlayerView = {
        let playerView = YTPlayerView()
        return playerView
    }()
    private let recipeInfoView = UIView()
    private let titleLabel = {
        let label = UILabel()
        label.font = .hf_large()
        label.textColor = .hf_grey_100()
        label.numberOfLines = 0
        return label
    }()
    private let descriptionLabel = UILabel()
    private let infoStackView = UIStackView()
    private let cookingTimeView = RecipeInfoBadgeView()
    private let difficultyView = RecipeInfoBadgeView()
    private let servingView = RecipeInfoBadgeView()
    private let storeSelectionBannerView = StoreSelectionBannerView()
    private let ingredientShowcasesStackView = UIStackView()
    private let loadingIndicator = UIActivityIndicatorView(style: .large)
    
    // MARK: - Properties
    private let recipeID: NSNumber
    private let recipePosition: Int?
    private let triggerSource: String
    private let deeplinkSource: String?
    private let inspirationsService = InspirationsServices()
    private let storeSelectionRouter = StoreSelectionRouter()
    private var recipe: Recipe?
    private var products: [Product] = []
    private var stockItems: [StockItem] = []
    private var gradientLayer: CAGradientLayer?
    private var isSubtitleExpanded = false

    // Recipe ingredients data
    private var recipeIngredientsResponse: RecipeIngredientsResponse?
    private var ingredientShowcaseViews: [ProductShowcaseView] = []
    private var ingredientShowcaseViewModels: [RecipeIngredientShowcaseViewModel] = []
    
    // Store selection tracking
    private var isWaitingForStoreSelection = false
    
    // MARK: - Initialization
    init(
        recipeID: NSNumber,
        recipePosition: Int? = nil,
        triggerSource: String,
        deeplinkSource: String? = nil) {
        self.recipeID = recipeID
        self.recipePosition = recipePosition
        self.triggerSource = triggerSource
        self.deeplinkSource = deeplinkSource
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        screenName = "recipe detail"
        setupUI()
        setupConstraints()
        fetchRecipeDetail()
    }
    
    // MARK: - Setup
    private func setupUI() {
        view.backgroundColor = .hf_white()
        
        // Setup scroll view
        scrollView.showsVerticalScrollIndicator = false

        // Setup recipe info view
        setupRecipeInfoView()
        
        // Setup store selection banner
        setupStoreSelectionBanner()

        // Setup ingredient showcases stack view
        setupIngredientShowcasesStackView()

        // Setup loading indicator
        loadingIndicator.color = .gray
        loadingIndicator.hidesWhenStopped = true

        // Add subviews
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
        contentView.addSubview(youtubePlayerView)
        contentView.addSubview(recipeInfoView)
        contentView.addSubview(storeSelectionBannerView)
        contentView.addSubview(ingredientShowcasesStackView)
        view.addSubview(loadingIndicator)
    }

    private func setupVideoPlayer(stringUrl : String?) {
        guard let stringUrl = stringUrl, let videoID = extractVideoID(from: stringUrl) else {
            return
        }

        // Configure player variables for better experience
        let playerVars: [String: Any] = [
            "playsinline": 1,
            "showinfo": 0,
            "rel": 0,
            "modestbranding": 1,
            "controls": 1,
            "fs": 1,
            "cc_load_policy": 0,
            "iv_load_policy": 3,
            "autohide": 0
        ]

        youtubePlayerView.delegate = self
        youtubePlayerView.load(withVideoId: videoID, playerVars: playerVars)
    }

    private func setupRecipeInfoView() {
        // Setup subtitle label
        descriptionLabel.font = UIFont.systemFont(ofSize: 14)
        descriptionLabel.textColor = .gray
        descriptionLabel.numberOfLines = 0

        // Setup info stack view
        infoStackView.axis = .horizontal
        infoStackView.distribution = .fillEqually

        // Add subviews to recipe info view
        recipeInfoView.addSubview(titleLabel)
        recipeInfoView.addSubview(infoStackView)
        recipeInfoView.addSubview(descriptionLabel)

        // Create vertical dividers
        let divider1 = createVerticalDivider()
        let divider2 = createVerticalDivider()
        servingView.addSubview(divider1)
        servingView.addSubview(divider2)
        divider1.snp.makeConstraints { make in
            make.top.left.bottom.equalToSuperview()
            make.width.equalTo(1)
        }
        divider2.snp.makeConstraints { make in
            make.top.right.bottom.equalToSuperview()
            make.width.equalTo(1)
        }

        // Add badge views to stack view with separators
        infoStackView.addArrangedSubview(cookingTimeView)
        infoStackView.addArrangedSubview(servingView)
        infoStackView.addArrangedSubview(difficultyView)
        
        // Setup constraints for recipe info view content
        setupRecipeInfoViewConstraints()
    }
    
    private func createVerticalDivider() -> UIView {
        let view = UIView()
        view.backgroundColor = .hf_grey_20()
        return view
    }

    private func setupRecipeInfoViewConstraints() {
        // Title label constraints
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
        }
        
        // Info stack view constraints
        infoStackView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(16)
            make.left.equalToSuperview().inset(16)
            make.right.equalToSuperview().inset(16)
            make.height.equalTo(30)
        }

        // Subtitle label constraints
        descriptionLabel.snp.makeConstraints { make in
            make.top.equalTo(infoStackView.snp.bottom).offset(16)
            make.left.right.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().inset(16)
        }
    }
    
    private func setupStoreSelectionBanner() {
        storeSelectionBannerView.delegate = self
        storeSelectionBannerView.configure(with: App.sharedInstance().stockLocation)
    }

    private func setupIngredientShowcasesStackView() {
        ingredientShowcasesStackView.axis = .vertical
        ingredientShowcasesStackView.spacing = 0
        ingredientShowcasesStackView.alignment = .fill
        ingredientShowcasesStackView.distribution = .fill
    }

    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    
        // Content view constraints
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }
    
        // YouTube player constraints (replacing header image)
        youtubePlayerView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(300)
        }
    
        // Recipe info view constraints
        recipeInfoView.snp.makeConstraints { make in
            make.top.equalTo(youtubePlayerView.snp.bottom)
            make.left.right.equalToSuperview()
        }

        // Store selection banner view constraints
        storeSelectionBannerView.snp.makeConstraints { make in
            make.top.equalTo(recipeInfoView.snp.bottom)
            make.left.right.equalToSuperview()
        }

        // Ingredient showcases stack view constraints
        ingredientShowcasesStackView.snp.makeConstraints { make in
            make.top.equalTo(storeSelectionBannerView.snp.bottom)
            make.left.right.equalToSuperview()
            make.bottom.equalToSuperview().offset(-16)
        }
    
        // Loading indicator constraints
        loadingIndicator.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }
    }

    // MARK: - Data Fetching
    private func fetchRecipeDetail() {
        showLoading()

        let currentStockLocation = App.sharedInstance().stockLocation
        inspirationsService.getInspirationDetail(withInspirationID: recipeID,
                                                 stockLocation: currentStockLocation,
                                                 success: { [weak self] operation, recipe, products in
            DispatchQueue.main.async {
                guard let self = self else { return }
                self.hideLoading()
                self.recipe = recipe
                self.products = products
                self.updateUI()
                self.trackRecipeDetailViewed()

                // Fetch recipe ingredients if we have a stock location
                if let stockLocation = currentStockLocation {
                    self.rightBarButtonItemTypes = .cart
                    self.fetchRecipeIngredients(stockLocation: stockLocation)
                }
            }
        }, failure: { [weak self] operation, error in
            DispatchQueue.main.async {
                self?.loadingIndicator.stopAnimating()
                Popup.showErrorMessage(fromResponse: operation)
            }
        })
    }
    
    private func trackRecipeDetailViewed() {
        Tracker.sharedInstance().trackRecipeDetailViewed(
            triggerSource: triggerSource,
            recipeID: recipeID,
            recipeName: recipe?.name,
            timeToCook: recipe?.displayTimeToCook,
            recipePosition: recipePosition,
            deeplinkSource: deeplinkSource
        )
    }

    private func fetchRecipeIngredients(stockLocation: StockLocation) {
        inspirationsService.getRecipeIngredients(stockLocation : stockLocation,
                                                 recipeID: recipeID,
                                                 success: { [weak self] ingredientsResponse in
            DispatchQueue.main.async {
                guard let self = self else { return }
                self.recipeIngredientsResponse = ingredientsResponse
                self.setupIngredientShowcases()
            }
        }, failure: { _, _ in
        })
    }
    
    private func showLoading() {
        loadingIndicator.startAnimating()
        contentView.isHidden = true
    }
    
    private func hideLoading() {
        self.loadingIndicator.stopAnimating()
        contentView.isHidden = false
    }

    private func updateUI() {
        guard let recipe = recipe else { return }

        // Update title and subtitle
        title = recipe.name
        titleLabel.text = recipe.name
        descriptionLabel.html(recipe.displayInstructions, fontSize: 12)

        // Update info badge views
        cookingTimeView.configure(icon: "hf_icon_recipe_time", title: recipe.displayTimeToCook)
        servingView.configure(icon: "hf_icon_recipe_smile", title: recipe.displayServingPortion)
        difficultyView.configure(icon: "hf_icon_recipe_serving", title: recipe.displayDifficulty)

        setupVideoPlayer(stringUrl: recipe.link)
    }

    private func setupIngredientShowcases() {
        guard let ingredientsResponse = recipeIngredientsResponse,
              let stockLocation = App.sharedInstance().stockLocation else {
            return
        }

        // Clear existing showcases
        clearIngredientShowcases()

        // Create showcases for each ingredient that has products
        let ingredientsWithProducts = ingredientsResponse.ingredients.filter { !$0.products.isEmpty }

        guard !ingredientsWithProducts.isEmpty else {
            print("ℹ️ No ingredients with products found")
            return
        }

        print("🎯 Setting up \(ingredientsWithProducts.count) ingredient showcases")

        for ingredient in ingredientsWithProducts {
            // Create view model
            let viewModel = RecipeIngredientShowcaseViewModel(recipeIngredient: ingredient, stockLocation: stockLocation)
            ingredientShowcaseViewModels.append(viewModel)

            // Create showcase view
            let showcaseView = ProductShowcaseView()
            showcaseView.screenName = screenName
            showcaseView.shouldHideOverlayButton = true

            // Set up product tap handler
            showcaseView.productTappedBlock = { [weak self] stockItem, recommendationType, position in
                guard let self = self else { return }
                self.openProductDetail(stockItem: stockItem, recommendationType: recommendationType, position: position)
            }

            // Set view model (this will trigger fetchProduct)
            showcaseView.viewModel = viewModel

            // Add to stack view (much simpler than manual constraints!)
            ingredientShowcasesStackView.addArrangedSubview(showcaseView)
            ingredientShowcaseViews.append(showcaseView)
        }

        // Force layout update
        view.layoutIfNeeded()
    }

    private func clearIngredientShowcases() {
        // Remove existing views from stack view
        for showcaseView in ingredientShowcaseViews {
            ingredientShowcasesStackView.removeArrangedSubview(showcaseView)
            showcaseView.removeFromSuperview()
        }
        ingredientShowcaseViews.removeAll()
        ingredientShowcaseViewModels.removeAll()
    }

    private func openProductDetail(stockItem: StockItem, recommendationType: String, position: Int) {
        guard let stockLocation = App.sharedInstance().stockLocation,
              let order = App.sharedInstance().order else {
            return
        }

        // Create product detail view model
        let productDetailViewModel = ProductDetailViewModel(
            order: order,
            stockLocation: stockLocation,
            stockItem: stockItem,
            position: position,
            isFromGlobalSearchScreen: false,
            isFromGlobalCategoryScreen: false,
            trackingType: recommendationType,
            source: screenName
        )

        // Create and push product detail view controller
        let productDetailViewController = ProductDetailViewController(productDetailViewModel)
        navigationController?.pushViewController(productDetailViewController, animated: true)
    }
    
    override func showCart(screenName: String?, order: Order, type: CartScreenType, position: Int = 1) {
        super.showCart(screenName: screenName, order: order, type: .pending, position: position)
    }

    private func showErrorAlert(message: String) {
        let alert = UIAlertController(title: "Error", message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "OK", style: .default))
        present(alert, animated: true)
    }

    private func showLocationPrompt() {
        let errorPopup = HFPopup(title: NSLocalizedString("Delivery Address Required", comment: ""),
                                 subtitle: NSLocalizedString("Please set your delivery location first to select a store.", comment: ""),
                                 buttonTitle: NSLocalizedString("Got it", comment: ""))
        errorPopup.primaryButtonTappedBlock = {
            App.sharedInstance().goToGlobalHomeTab()
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                if let homeViewController = App.topViewController() as? HomeViewController {
                    homeViewController.showLocationPrompt()
                }
            }
        }
        App.topViewController()?.showPopup(errorPopup)
    }

    private func showStoreSelection(deliveryAddress: Address) {
        // Set up custom completion handling by observing stock location changes
        setupStockLocationObserver()

        // Present store selection without going to store home
        storeSelectionRouter.present(
            from: self,
            campaignFromDeeplink: nil,
            showCloseButton: true,
            popToRootAfterDismiss: false,
            fromFindStore: false,
            showStoreHome: false, // Don't go to store home
            deliveryAddress: deliveryAddress,
            source: screenName
        )
    }

    private func setupStockLocationObserver() {
        // Listen for stock location changes using the proper notification
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(stockLocationDidChange(_:)),
            name: NSNotification.Name.StockLocationChanged,
            object: nil
        )
    }

    @objc private func stockLocationDidChange(_ notification: Notification) {
        // Remove observer
        NotificationCenter.default.removeObserver(self, name: NSNotification.Name.StockLocationChanged, object: nil)
        
        // Dismiss store selection
        storeSelectionRouter.dismiss()
        
        // Get the selected stock location
        if let stockLocation = App.sharedInstance().stockLocation {
            self.rightBarButtonItemTypes = .cart
            // Update the banner with the selected store
            storeSelectionBannerView.configure(with: stockLocation)
            // Fetch ingredients for the new stock location
            fetchRecipeIngredients(stockLocation: stockLocation)
        }
    }
    
    deinit {
        // Clean up observer
        NotificationCenter.default.removeObserver(self)
    }
}

// MARK: - StoreSelectionBannerViewDelegate
extension RecipeDetailViewController: StoreSelectionBannerViewDelegate {
    func storeSelectionBannerDidTapChangeStore() {
        // Check if we have an address first
        guard let address = App.sharedInstance().address else {
            showLocationPrompt()
            return
        }

        // Show store selection
        showStoreSelection(deliveryAddress: address)
    }
}

extension RecipeDetailViewController: YTPlayerViewDelegate {
    func playerView(_ playerView: YTPlayerView, didChangeTo state: YTPlayerState) {
        if state == .playing {
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                self.enterFullScreen()
            }
        }
    }
    
    private func enterFullScreen() {
        let js = "var iframe = document.getElementsByTagName('iframe')[0]; iframe.requestFullscreen();"
        youtubePlayerView.webView?.evaluateJavaScript(js)
    }
}

extension RecipeDetailViewController {
    private func extractVideoID(from urlString: String) -> String? {
        // Handle different YouTube URL formats
        if let url = URL(string: urlString) {
            // For youtu.be format
            if url.host == "youtu.be" {
                return String(url.path.dropFirst()) // Remove leading "/"
            }
            
            // For youtube.com format
            if url.host?.contains("youtube.com") == true {
                // Handle YouTube Shorts format: /shorts/VIDEO_ID
                if url.path.hasPrefix("/shorts/") {
                    return String(url.path.dropFirst(8)) // Remove "/shorts/"
                }
                
                // Handle regular YouTube format with v parameter
                let components = URLComponents(url: url, resolvingAgainstBaseURL: false)
                return components?.queryItems?.first(where: { $0.name == "v" })?.value
            }
        }
        
        return nil
    }
}
