//
//  RecipeCell.swift
//  HappyFresh
//
//  Created by <PERSON> on 7/7/20.
//  Copyright © 2020 HappyFresh Inc. All rights reserved.
//

import Foundation
import SnapKit
import UIKit

class OldRecipeCell: UICollectionViewCell, WidgetItemCell {
    private lazy var containerView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        view.setupShadow()
        view.layer.masksToBounds = false
        view.layer.cornerRadius = 10
        return view
    }()
    
    private lazy var imageView: UIImageView = {
        let imageView = UIImageView(frame: .zero)
        imageView.clipsToBounds = true
        imageView.layer.cornerRadius = 10
        imageView.layer.maskedCorners = [.layerMinXMinYCorner, .layerMaxXMinYCorner]
        return imageView
    }()
    
    private lazy var titleLabel: UILabel = {
        let label = UILabel(frame: .zero)
        label.font = .hf_title_7()
        label.textColor = .hf_grey_80()
        label.numberO<PERSON>Lines = 3
        label.sizeToFit()
        return label
    }()
    
    private lazy var infoView: UIView = {
        let view = UIView()
        return view
    }()
    
    private lazy var durationIconImageView: UIImageView = {
        let imageView = UIImageView(frame: .zero)
        imageView.image = UIImage(named: "hf_icon_duration_16px_dark")
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    private lazy var durationLabel: UILabel = {
        let label = UILabel(frame: .zero)
        label.font = .hf_title_12()
        label.textColor = .hf_grey_80()
        label.numberOfLines = 1
        return label
    }()
    
    private lazy var portionIconImageView: UIImageView = {
        let imageView = UIImageView(frame: .zero)
        imageView.image = UIImage(named: "hf_icon_portion_16px_dark")
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    private lazy var portionLabel: UILabel = {
        let label = UILabel(frame: .zero)
        label.font = .hf_title_12()
        label.textColor = .hf_grey_80()
        label.numberOfLines = 1
        return label
    }()
    
    private lazy var difficultyIconImageView: UIImageView = {
        let imageView = UIImageView(frame: .zero)
        imageView.image = UIImage(named: "hf_icon_difficulty_16px_dark")
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    private lazy var difficultyLabel: UILabel = {
        let label = UILabel(frame: .zero)
        label.font = .hf_title_12()
        label.textColor = .hf_grey_80()
        label.numberOfLines = 1
        return label
    }()
    
    static var minHeight: CGFloat { 222 }
    static var maxHeight: CGFloat { 238 }
    static var width: CGFloat { 156 }
    static var itemSpacing: CGFloat { 8 }
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        commonInit()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        commonInit()
    }
    
    private func commonInit() {
        backgroundColor = .clear
        
        addSubview(containerView)
        containerView.addSubview(imageView)
        containerView.addSubview(titleLabel)
        containerView.addSubview(infoView)
        
        containerView.snp.makeConstraints { (make) in
            make.leading.equalToSuperview()
            make.top.equalToSuperview()
            make.bottom.equalToSuperview()
            make.trailing.equalToSuperview()
        }
        
        imageView.snp.makeConstraints { (make) in
            make.leading.equalToSuperview()
            make.trailing.equalToSuperview()
            make.top.equalToSuperview()
            make.height.equalTo(96)
        }
        
        titleLabel.snp.makeConstraints { (make) in
            make.leading.equalToSuperview().offset(12)
            make.trailing.equalToSuperview().offset(-12)
            make.top.equalTo(imageView.snp.bottom).offset(8)
        }
        
        infoView.snp.makeConstraints { (make) in
            make.leading.equalToSuperview().offset(12)
            make.trailing.equalToSuperview().offset(-12)
            make.bottom.equalToSuperview().offset(-8)
        }
        
        infoView.addSubview(durationIconImageView)
        infoView.addSubview(durationLabel)
        infoView.addSubview(portionIconImageView)
        infoView.addSubview(portionLabel)
        infoView.addSubview(difficultyIconImageView)
        infoView.addSubview(difficultyLabel)
        
        durationIconImageView.snp.makeConstraints { (make) in
            make.leading.equalToSuperview()
            make.top.equalToSuperview()
            make.width.equalTo(24)
            make.height.equalTo(24)
        }
        
        durationLabel.snp.makeConstraints { (make) in
            make.leading.equalTo(durationIconImageView.snp.trailing).offset(4)
            make.trailing.equalToSuperview()
            make.top.equalTo(durationIconImageView.snp.top)
            make.bottom.equalTo(durationIconImageView.snp.bottom)
        }
        
        portionIconImageView.snp.makeConstraints { (make) in
            make.leading.equalToSuperview()
            make.top.equalTo(durationIconImageView.snp.bottom)
            make.bottom.equalTo(difficultyIconImageView.snp.top)
            make.width.equalTo(24)
            make.height.equalTo(24)
        }
        
        portionLabel.snp.makeConstraints { (make) in
            make.leading.equalTo(portionIconImageView.snp.trailing).offset(4)
            make.trailing.equalToSuperview()
            make.top.equalTo(portionIconImageView.snp.top)
            make.bottom.equalTo(portionIconImageView.snp.bottom)
        }
        
        difficultyIconImageView.snp.makeConstraints { (make) in
            make.leading.equalToSuperview()
            make.top.equalTo(portionIconImageView.snp.bottom)
            make.bottom.equalToSuperview()
            make.width.equalTo(24)
            make.height.equalTo(24)
        }
        
        difficultyLabel.snp.makeConstraints { (make) in
            make.leading.equalTo(difficultyIconImageView.snp.trailing).offset(4)
            make.trailing.equalToSuperview()
            make.top.equalTo(difficultyIconImageView.snp.top)
            make.bottom.equalTo(difficultyIconImageView.snp.bottom)
        }
        
        accessibilityIdentifier = AccessibilityIdentifier.RecipeCollectionCell
    }
    
    func configure(with viewModel: RecipeCellViewModel) {
        imageView.add_setImage(with: viewModel.imageUrl,
                               placeholderImage: UIImage(named: "hf_img_placeholder_with_background_144px"))
        titleLabel.text = viewModel.title
        durationLabel.text = viewModel.duration
        portionLabel.text = viewModel.portion
        difficultyLabel.text = viewModel.difficulty
    }
}
