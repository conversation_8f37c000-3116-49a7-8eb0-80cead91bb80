//
//  IngredientCell.swift
//  HappyFresh
//
//  Created by Augment Agent on 2025-07-07.
//

import UIKit
import SnapKit

protocol IngredientCellDelegate: AnyObject {
    func didTapPlusButton(for product: Product)
    func didTapMinusButton(for product: Product)
}

class IngredientCell: UITableViewCell {

    static let reuseIdentifier = "IngredientCell"
    static let height: CGFloat = 90

    // MARK: - UI Components
    private let cartItemProductView = CartItemProductView()

    // MARK: - Properties
    weak var delegate: IngredientCellDelegate?
    private var product: Product?
    private var stockItem: StockItem?
    private var currentQuantity: Int = 0
    
    // MARK: - Initialization
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
        setupConstraints()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - Setup
    private func setupUI() {
        selectionStyle = .none
        backgroundColor = .clear

        // Hide shopper notes and replacement features
        cartItemProductView.shopperNotesButtonStackView.isHidden = true

        // Configure button actions
        cartItemProductView.plusButton.addTarget(self, action: #selector(plusButtonTapped), for: .touchUpInside)
        cartItemProductView.minusButton.addTarget(self, action: #selector(minusButtonTapped), for: .touchUpInside)

        // Add subview
        contentView.addSubview(cartItemProductView)
    }
    
    private func setupConstraints() {
        // CartItemProductView constraints
        cartItemProductView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(8)
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
            make.bottom.equalToSuperview().offset(-8)
        }
    }
    
    // MARK: - Configuration
    func configure(with product: Product, hideActionView: Bool) {
        self.product = product

        // Get StockItem for this product
        if let variant = product.variant(),
           let stockLocation = App.sharedInstance().stockLocation {
            self.stockItem = StockItem.find(by: stockLocation, variant: variant)
        }

        // Configure product name
        cartItemProductView.titleLabel.text = product.name

        // Configure price
        if let price = product.price {
            let formattedPrice = formatPrice(price)
            cartItemProductView.priceLabel.text = formattedPrice

            // Configure unit price if available
            if let unit = product.unit {
                cartItemProductView.unitPriceLabel.text = "1 \(unit)"
            } else {
                cartItemProductView.unitPriceLabel.text = "1 kg"
            }
        } else {
            cartItemProductView.priceLabel.text = "Price not available"
            cartItemProductView.unitPriceLabel.text = ""
        }

        // Load product image
        if let imageURL = product.variant()?.imageURL() {
            cartItemProductView.imageView.setImage(with: imageURL,
                                                   placeholder: UIImage(named: "hf_img_product_placeholder_600px"))
        } else {
            cartItemProductView.imageView.image = UIImage(systemName: "photo")
            cartItemProductView.imageView.tintColor = .gray
        }
        
        cartItemProductView.actionsContainerView.isHidden = hideActionView

        // Configure discount/promo elements (hide them for ingredients)
        cartItemProductView.discountContainer.isHidden = true
        cartItemProductView.discountOldPriceLabel.isHidden = true
        cartItemProductView.freeItemLabel.isHidden = true
        cartItemProductView.freeItemQuantityLabel.isHidden = true
        cartItemProductView.oosLabel.isHidden = true

        // Update quantity display
        updateQuantityDisplay()
    }
    
    private func updateQuantityDisplay() {
        guard let variant = product?.variant(),
              let order = App.sharedInstance().order else {
            currentQuantity = 0
            cartItemProductView.quantityLabel.text = "0"
            cartItemProductView.minusButton.isEnabled = false
            return
        }

        // Get current quantity from order
        let quantity = OrderService.shared.quantityOf(variant: variant, order: order)?.intValue ?? 0
        currentQuantity = quantity
        cartItemProductView.quantityLabel.text = "\(quantity)"
        
        // Update button states
        cartItemProductView.minusButton.isEnabled = quantity > 0
        
        // Check max order quantity if available
        if let stockItem = stockItem,
           let maxOrderQuantity = stockItem.maxOrderQuantity,
           maxOrderQuantity.intValue > 0 {
            cartItemProductView.plusButton.isEnabled = quantity < maxOrderQuantity.intValue
        } else {
            cartItemProductView.plusButton.isEnabled = true
        }
    }
    
    private func formatPrice(_ price: NSNumber) -> String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = "IDR"
        formatter.maximumFractionDigits = 0
        
        if let formattedPrice = formatter.string(from: price) {
            return formattedPrice
        } else {
            return "Rp \(price.intValue)"
        }
    }
    
    // MARK: - Actions
    @objc private func plusButtonTapped() {
        guard let product = product else { return }
        
        // Add visual feedback
        UIView.animate(withDuration: 0.1, animations: {
            self.cartItemProductView.plusButton.transform = CGAffineTransform(scaleX: 0.9, y: 0.9)
        }) { _ in
            UIView.animate(withDuration: 0.1) {
                self.cartItemProductView.plusButton.transform = CGAffineTransform.identity
            }
        }
        
        // Update cart
        updateCartQuantity(isAdding: true)
        delegate?.didTapPlusButton(for: product)
    }
    
    @objc private func minusButtonTapped() {
        guard let product = product, currentQuantity > 0 else { return }
        
        // Add visual feedback
        UIView.animate(withDuration: 0.1, animations: {
            self.cartItemProductView.minusButton.transform = CGAffineTransform(scaleX: 0.9, y: 0.9)
        }) { _ in
            UIView.animate(withDuration: 0.1) {
                self.cartItemProductView.minusButton.transform = CGAffineTransform.identity
            }
        }
        
        // Update cart
        updateCartQuantity(isAdding: false)
        delegate?.didTapMinusButton(for: product)
    }
    
    private func updateCartQuantity(isAdding: Bool) {
        guard let order = App.sharedInstance().order else { return }
        
        let newQuantity = max(0, currentQuantity + (isAdding ? 1 : -1))
        
        if let stockItem = stockItem {
            // Use StockItem if available (preferred method)
            OrderService.shared.updateQuantity(stockItem: stockItem, order: order, quantity: newQuantity)
        } else if let variant = product?.variant() {
            // Fallback to using variant directly
            OrderService.shared.updateQuantity(variantID: variant.variantID ?? -1, order: order, quantity: newQuantity)
        }
        
        // Update local state
        currentQuantity = newQuantity
        cartItemProductView.quantityLabel.text = "\(newQuantity)"
        cartItemProductView.minusButton.isEnabled = newQuantity > 0
        
        // Check max quantity limit
        if let stockItem = stockItem,
           let maxOrderQuantity = stockItem.maxOrderQuantity,
           maxOrderQuantity.intValue > 0 {
            cartItemProductView.plusButton.isEnabled = newQuantity < maxOrderQuantity.intValue
            
            if isAdding && newQuantity == maxOrderQuantity.intValue {
                let warningText = NSLocalizedString("This item has reached maximum quantity allowed", comment: "")
                BannerManager.sharedInstance()?.showBanner(withText: warningText, userInfo: nil)
            }
        }
        
        // Post notification for cart updates
        NotificationCenter.default.post(name: NSNotification.Name.OrderChanged, object: nil)
    }

    override func prepareForReuse() {
        super.prepareForReuse()
        cartItemProductView.imageView.image = nil
        cartItemProductView.titleLabel.text = nil
        cartItemProductView.priceLabel.text = nil
        cartItemProductView.unitPriceLabel.text = nil
        product = nil
        stockItem = nil
        currentQuantity = 0
    }
}
