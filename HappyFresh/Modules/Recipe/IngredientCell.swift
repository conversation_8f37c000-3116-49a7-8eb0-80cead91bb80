//
//  IngredientCell.swift
//  HappyFresh
//
//  Created by Augment Agent on 2025-07-07.
//

import UIKit
import SnapKit

protocol IngredientCellDelegate: AnyObject {
    func didTapAddButton(for product: Product)
}

class IngredientCell: UITableViewCell {
    
    static let reuseIdentifier = "IngredientCell"
    
    // MARK: - UI Components
    private let productImageView = UIImageView()
    private let nameLabel = UILabel()
    private let priceLabel = UILabel()
    private let addButton = UIButton()
    private let containerView = UIView()
    
    // MARK: - Properties
    weak var delegate: IngredientCellDelegate?
    private var product: Product?
    
    // MARK: - Initialization
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
        setupConstraints()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Setup
    private func setupUI() {
        selectionStyle = .none
        backgroundColor = .clear
        
        // Container view
        containerView.backgroundColor = .white
        containerView.layer.cornerRadius = 8
        containerView.layer.shadowColor = UIColor.black.cgColor
        containerView.layer.shadowOffset = CGSize(width: 0, height: 2)
        containerView.layer.shadowRadius = 4
        containerView.layer.shadowOpacity = 0.1
        
        // Product image
        productImageView.contentMode = .scaleAspectFill
        productImageView.clipsToBounds = true
        productImageView.layer.cornerRadius = 6
        productImageView.backgroundColor = .lightGray
        
        // Name label
        nameLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        nameLabel.textColor = .black
        nameLabel.numberOfLines = 2
        
        // Price label
        priceLabel.font = UIFont.systemFont(ofSize: 12)
        priceLabel.textColor = .gray
        
        // Add button
        addButton.setTitle("+", for: .normal)
        addButton.setTitleColor(.white, for: .normal)
        addButton.backgroundColor = UIColor.systemOrange
        addButton.titleLabel?.font = UIFont.boldSystemFont(ofSize: 18)
        addButton.layer.cornerRadius = 15
        addButton.addTarget(self, action: #selector(addButtonTapped), for: .touchUpInside)
        
        // Add subviews
        contentView.addSubview(containerView)
        containerView.addSubview(productImageView)
        containerView.addSubview(nameLabel)
        containerView.addSubview(priceLabel)
        containerView.addSubview(addButton)
    }
    
    private func setupConstraints() {
        // Container view constraints
        containerView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(4)
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
            make.bottom.equalToSuperview().offset(-4)
        }
        
        // Product image constraints
        productImageView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(12)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(50)
        }
        
        // Name label constraints
        nameLabel.snp.makeConstraints { make in
            make.left.equalTo(productImageView.snp.right).offset(12)
            make.top.equalToSuperview().offset(12)
            make.right.equalTo(addButton.snp.left).offset(-12)
        }
        
        // Price label constraints
        priceLabel.snp.makeConstraints { make in
            make.left.equalTo(productImageView.snp.right).offset(12)
            make.top.equalTo(nameLabel.snp.bottom).offset(4)
            make.right.equalTo(addButton.snp.left).offset(-12)
            make.bottom.lessThanOrEqualToSuperview().offset(-12)
        }
        
        // Add button constraints
        addButton.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-12)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(30)
        }
    }
    
    // MARK: - Configuration
    func configure(with product: Product) {
        self.product = product
        
        nameLabel.text = product.name
        
        // Format price
        if let price = product.price {
            priceLabel.text = formatPrice(price)
        } else {
            priceLabel.text = "Price not available"
        }
        
        // Load product image
//        if let imageURL = product.imageURL {
//            loadImage(from: imageURL)
//        } else {
//            productImageView.image = UIImage(systemName: "photo")
//            productImageView.tintColor = .gray
//        }
    }
    
    private func formatPrice(_ price: NSNumber) -> String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = "IDR"
        formatter.maximumFractionDigits = 0
        
        if let formattedPrice = formatter.string(from: price) {
            return formattedPrice
        } else {
            return "Rp \(price.intValue)"
        }
    }
    
//    private func loadImage(from urlString: String) {
//        guard let url = URL(string: urlString) else { return }
//        
//        // Simple image loading - in production you'd use a proper image loading library
//        URLSession.shared.dataTask(with: url) { [weak self] data, response, error in
//            guard let data = data, let image = UIImage(data: data) else { return }
//            
//            DispatchQueue.main.async {
//                self?.productImageView.image = image
//            }
//        }.resume()
//    }
    
    // MARK: - Actions
    @objc private func addButtonTapped() {
        guard let product = product else { return }
        delegate?.didTapAddButton(for: product)
        
        // Add visual feedback
        UIView.animate(withDuration: 0.1, animations: {
            self.addButton.transform = CGAffineTransform(scaleX: 0.9, y: 0.9)
        }) { _ in
            UIView.animate(withDuration: 0.1) {
                self.addButton.transform = CGAffineTransform.identity
            }
        }
    }
    
    override func prepareForReuse() {
        super.prepareForReuse()
        productImageView.image = nil
        nameLabel.text = nil
        priceLabel.text = nil
        product = nil
    }
}
