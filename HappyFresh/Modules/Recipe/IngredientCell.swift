//
//  IngredientCell.swift
//  HappyFresh
//
//  Created by Augment Agent on 2025-07-07.
//

import UIKit
import SnapKit

protocol IngredientCellDelegate: AnyObject {
    func didTapAddButton(for product: Product)
}

class IngredientCell: UITableViewCell {

    static let reuseIdentifier = "IngredientCell"
    static let height: CGFloat = 90

    // MARK: - UI Components
    private let cartItemProductView = CartItemProductView()

    // MARK: - Properties
    weak var delegate: IngredientCellDelegate?
    private var product: Product?
    
    // MARK: - Initialization
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
        setupConstraints()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - Setup
    private func setupUI() {
        selectionStyle = .none
        backgroundColor = .clear

        // Hide shopper notes and replacement features
        cartItemProductView.shopperNotesButtonStackView.isHidden = true

        // Configure plus button action
        cartItemProductView.plusButton.addTarget(self, action: #selector(addButtonTapped), for: .touchUpInside)

        // Add subview
        contentView.addSubview(cartItemProductView)
    }
    
    private func setupConstraints() {
        // CartItemProductView constraints
        cartItemProductView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(8)
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
            make.bottom.equalToSuperview().offset(-8)
        }
    }
    
    // MARK: - Configuration
    func configure(with product: Product) {
        self.product = product

        // Configure product name
        cartItemProductView.titleLabel.text = product.name

        // Configure price
        if let price = product.price {
            let formattedPrice = formatPrice(price)
            cartItemProductView.priceLabel.text = formattedPrice

            // Configure unit price if available
            if let unit = product.unit {
                cartItemProductView.unitPriceLabel.text = "1 \(unit)"
            } else {
                cartItemProductView.unitPriceLabel.text = "1 kg"
            }
        } else {
            cartItemProductView.priceLabel.text = "Price not available"
            cartItemProductView.unitPriceLabel.text = ""
        }

        // Load product image
        if let imageURL = product.variant().imageURL() {
            cartItemProductView.imageView.setImage(with: imageURL,
                                                   placeholder: UIImage(named: "hf_img_product_placeholder_600px"))
        } else {
            cartItemProductView.imageView.image = UIImage(systemName: "photo")
            cartItemProductView.imageView.tintColor = .gray
        }

        // Configure discount/promo elements (hide them for ingredients)
        cartItemProductView.discountContainer.isHidden = true
        cartItemProductView.discountOldPriceLabel.isHidden = true
        cartItemProductView.freeItemLabel.isHidden = true
        cartItemProductView.freeItemQuantityLabel.isHidden = true
        cartItemProductView.oosLabel.isHidden = true
    }
    
    private func formatPrice(_ price: NSNumber) -> String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = "IDR"
        formatter.maximumFractionDigits = 0
        
        if let formattedPrice = formatter.string(from: price) {
            return formattedPrice
        } else {
            return "Rp \(price.intValue)"
        }
    }
    
    // MARK: - Actions
    @objc private func addButtonTapped() {
        guard let product = product else { return }
        delegate?.didTapAddButton(for: product)

        // Add visual feedback
        UIView.animate(withDuration: 0.1, animations: {
            self.cartItemProductView.plusButton.transform = CGAffineTransform(scaleX: 0.9, y: 0.9)
        }) { _ in
            UIView.animate(withDuration: 0.1) {
                self.cartItemProductView.plusButton.transform = CGAffineTransform.identity
            }
        }
    }

    override func prepareForReuse() {
        super.prepareForReuse()
        cartItemProductView.imageView.image = nil
        cartItemProductView.titleLabel.text = nil
        cartItemProductView.priceLabel.text = nil
        cartItemProductView.unitPriceLabel.text = nil
        product = nil
    }
}
