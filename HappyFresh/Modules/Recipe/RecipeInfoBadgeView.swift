//
//  RecipeInfoBadgeView.swift
//  HappyFresh
//
//  Created by Augment Agent on 2025-07-07.
//

import UIKit
import SnapKit

class RecipeInfoBadgeView: UIView {
    
    // MARK: - UI Components
    private let iconImageView = UIImageView()
    private let titleLabel = UILabel()
    private let stackView = UIStackView()
    
    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        setupConstraints()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Setup
    private func setupUI() {
        // Setup icon
        iconImageView.contentMode = .scaleAspectFit
        iconImageView.tintColor = .systemGray
        iconImageView.snp.makeConstraints { make in
            make.width.height.equalTo(20)
        }
        
        // Setup title label
        titleLabel.font = .hf_tiny()
        titleLabel.textColor = .hf_grey_100()
        titleLabel.textAlignment = .left
        titleLabel.numberOfLines = 1

        // Setup stack view
        stackView.axis = .horizontal
        stackView.spacing = 4
        stackView.alignment = .center
        stackView.distribution = .fill
        stackView.addArrangedSubview(iconImageView)
        stackView.addArrangedSubview(titleLabel)

        // Add stack view to view
        addSubview(stackView)
    }
    
    private func setupConstraints() {
        // Center stack view inside the view
        stackView.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }
    }
    
    // MARK: - Configuration
    func configure(icon: String, title: String?) {
        iconImageView.image = UIImage(named: icon)
        titleLabel.text = title
    }
}
