//
//  TikTokReelsViewController.swift
//  HappyFresh
//
//  Created by Augment Agent on 2025-07-17.
//

import UIKit
import SnapKit
import AVFoundation

class VideoReelsViewController: UIViewController {
    
    // MARK: - UI Components
    private lazy var collectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .vertical
        layout.minimumLineSpacing = 0
        layout.minimumInteritemSpacing = 0
        layout.itemSize = CGSize(width: view.frame.width, height: view.frame.height)
        
        let cv = UICollectionView(frame: .zero, collectionViewLayout: layout)
        cv.backgroundColor = .black
        cv.isPagingEnabled = true
        cv.showsVerticalScrollIndicator = false
        cv.delegate = self
        cv.dataSource = self
        cv.register(VideoReelCell.self, forCellWithReuseIdentifier: "VideoReelCell")
        return cv
    }()
    
    private let backButton: UIButton = {
        let button = UIButton(type: .system)
        button.setImage(UIImage(systemName: "chevron.left"), for: .normal)
        button.tintColor = .white
        button.backgroundColor = UIColor.black.withAlphaComponent(0.5)
        button.layer.cornerRadius = 20
        button.layer.masksToBounds = true
        button.addTarget(self, action: #selector(backButtonTapped), for: .touchUpInside)
        return button
    }()
    
    // MARK: - Properties
    private var videoReels: [VideoReel] = []
    private var currentIndex: Int = 0

    // Advanced video management
    private var queuePlayer: AVQueuePlayer?
    private var playerItems: [String: AVPlayerItem] = [:]
    private var playerLayers: [String: AVPlayerLayer] = [:]
    private var bufferObservers: [String: NSKeyValueObservation] = [:]
    private var statusObservers: [String: NSKeyValueObservation] = [:]
    private var preloadQueue = DispatchQueue(label: "com.happyfresh.tiktok.preload", qos: .userInitiated)
    private var isPreloading = false
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        print("jilytest123 🎬 TikTokReelsViewController loaded")
        setupAudioSession()
        setupUI()
        setupConstraints()
        loadVideoReels()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        navigationController?.setNavigationBarHidden(true, animated: animated)
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        playCurrentVideo()
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        pauseAllVideos()
        cleanupNotificationObservers()
        navigationController?.setNavigationBarHidden(false, animated: animated)
    }

    deinit {
        print("jilytest123 🗑️ TikTokReelsViewController deinit - cleaning up")
        cleanupAllObservers()
        cleanupNotificationObservers()
        queuePlayer?.pause()
        queuePlayer = nil
    }
    
    override var prefersStatusBarHidden: Bool {
        return true
    }
    
    // MARK: - Setup
    private func setupAudioSession() {
        do {
            try AVAudioSession.sharedInstance().setCategory(.playback, mode: .default, options: [])
            try AVAudioSession.sharedInstance().setActive(true)
            print("jilytest123 🔊 Audio session configured for TikTok reels")
        } catch {
            print("jilytest123 ❌ Audio session setup failed: \(error)")
        }
    }

    private func setupUI() {
        view.backgroundColor = .black

        view.addSubview(collectionView)
        view.addSubview(backButton)
    }
    
    private func setupConstraints() {
        collectionView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // Back button - top left corner with safe area
        backButton.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide.snp.top).offset(16)
            make.leading.equalToSuperview().offset(16)
            make.width.height.equalTo(40)
        }
    }
    
    // MARK: - Data Loading
    private func loadVideoReels() {
        print("jilytest123 📱 Loading video reels with advanced preloading")

        // Use existing VideoReel sample data
        videoReels = VideoReel.sampleReels

        // Reload collection view first
        collectionView.reloadData()

        // Start aggressive preloading
        startAdvancedPreloading()

        print("jilytest123 ✅ Loaded \(videoReels.count) video reels")
    }

    private func startAdvancedPreloading() {
        guard !videoReels.isEmpty && !isPreloading else { return }

        isPreloading = true
        print("jilytest123 🚀 Starting advanced preloading for \(videoReels.count) videos")

        preloadQueue.async { [weak self] in
            guard let self = self else { return }

            // Preload first video immediately on main thread for instant playback
            DispatchQueue.main.async {
                self.preloadVideoItem(at: 0, priority: .immediate)
            }

            // Preload next 2 videos with high priority
            for index in 1..<min(3, self.videoReels.count) {
                self.preloadVideoItem(at: index, priority: .high)
                Thread.sleep(forTimeInterval: 0.1) // Small delay between preloads
            }

            // Preload remaining videos with normal priority
            for index in 3..<self.videoReels.count {
                self.preloadVideoItem(at: index, priority: .normal)
                Thread.sleep(forTimeInterval: 0.2) // Longer delay for background preloading
            }

            DispatchQueue.main.async {
                self.isPreloading = false
                print("jilytest123 ✅ Advanced preloading completed")

                // Start playing first video after preloading
                self.playCurrentVideo()
            }
        }
    }

    private enum PreloadPriority {
        case immediate, high, normal
    }

    private func preloadVideoItem(at index: Int, priority: PreloadPriority) {
        guard index < videoReels.count else { return }

        let videoReel = videoReels[index]
        let url = videoReel.videoURL
        let urlString = url.absoluteString

        // Skip if already preloaded
        if playerItems[urlString] != nil {
            print("jilytest123 ⚡ Video \(index) already preloaded: \(videoReel.title)")
            return
        }

        print("jilytest123 🎯 Background preloading video \(index) (\(priority)): \(videoReel.title)")

        // Create optimized player item for background preloading
        // Note: These items are for monitoring and caching purposes only
        // Fresh items will be created for actual playback to avoid sharing issues
        let playerItem = createFreshPlayerItem(for: url, priority: priority)

        // Store player item for reference (not for direct playback)
        playerItems[urlString] = playerItem

        // Setup buffer monitoring
        setupBufferMonitoring(for: playerItem, videoTitle: videoReel.title, index: index)

        // Setup status monitoring
        setupStatusMonitoring(for: playerItem, videoTitle: videoReel.title, index: index, urlString: "\(urlString)_preload")

        print("jilytest123 📦 Created background preload item for video \(index): \(videoReel.title)")
    }

    private func setupBufferMonitoring(for playerItem: AVPlayerItem, videoTitle: String, index: Int) {
        let observerKey = "\(playerItem.asset.description)_\(index)_\(Date().timeIntervalSince1970)"

        // Monitor loadedTimeRanges for buffer progress
        let bufferObserver = playerItem.observe(\.loadedTimeRanges, options: [.new]) { [weak self] item, _ in
            DispatchQueue.main.async {
                self?.handleBufferUpdate(for: item, videoTitle: videoTitle, index: index)
            }
        }

        bufferObservers[observerKey] = bufferObserver
        print("jilytest123 👁️ Setup buffer monitoring for: \(videoTitle) (key: \(observerKey))")
    }

    private func setupStatusMonitoring(for playerItem: AVPlayerItem, videoTitle: String, index: Int, urlString: String) {
        let observerKey = "\(urlString)_\(index)_\(Date().timeIntervalSince1970)"

        // Monitor player item status
        let statusObserver = playerItem.observe(\.status, options: [.new]) { [weak self] item, _ in
            DispatchQueue.main.async {
                self?.handleStatusUpdate(for: item, videoTitle: videoTitle, index: index)
            }
        }

        statusObservers[observerKey] = statusObserver
        print("jilytest123 👁️ Setup status monitoring for: \(videoTitle) (key: \(observerKey))")
    }

    private func handleBufferUpdate(for playerItem: AVPlayerItem, videoTitle: String, index: Int) {
        let loadedTimeRanges = playerItem.loadedTimeRanges

        if !loadedTimeRanges.isEmpty {
            let timeRange = loadedTimeRanges[0].timeRangeValue
            let loadedDuration = CMTimeGetSeconds(timeRange.duration)
            let totalDuration = CMTimeGetSeconds(playerItem.duration)

            if totalDuration > 0 {
                let bufferPercentage = (loadedDuration / totalDuration) * 100
                print("jilytest123 📊 Video \(index) (\(videoTitle)) buffer: \(String(format: "%.1f", loadedDuration))s / \(String(format: "%.1f", totalDuration))s (\(String(format: "%.1f", bufferPercentage))%)")
            } else {
                print("jilytest123 📊 Video \(index) (\(videoTitle)) loaded: \(String(format: "%.1f", loadedDuration))s")
            }
        }
    }

    private func handleStatusUpdate(for playerItem: AVPlayerItem, videoTitle: String, index: Int) {
        switch playerItem.status {
        case .readyToPlay:
            print("jilytest123 ✅ Video \(index) (\(videoTitle)) ready to play")
        case .failed:
            if let error = playerItem.error {
                print("jilytest123 ❌ Video \(index) (\(videoTitle)) failed: \(error.localizedDescription)")
            } else {
                print("jilytest123 ❌ Video \(index) (\(videoTitle)) failed with unknown error")
            }
        case .unknown:
            print("jilytest123 ⏳ Video \(index) (\(videoTitle)) status unknown")
        @unknown default:
            print("jilytest123 ❓ Video \(index) (\(videoTitle)) unknown status")
        }
    }
    
    // MARK: - Video Control
    private func playCurrentVideo() {
        guard currentIndex < videoReels.count else { return }

        let videoReel = videoReels[currentIndex]
        let url = videoReel.videoURL
        let urlString = url.absoluteString

        print("jilytest123 🎬 Playing video at index \(currentIndex): \(videoReel.title)")

        // Pause current queue player and cleanup previous notifications
        queuePlayer?.pause()
        cleanupNotificationObservers()

        // Always create a fresh player item for the queue player to avoid sharing issues
        let playerItem = createFreshPlayerItem(for: url, priority: .immediate)
        print("jilytest123 🆕 Created fresh player item for queue player: \(videoReel.title)")

        // Create new queue player with fresh item
        queuePlayer = AVQueuePlayer(playerItem: playerItem)
        queuePlayer?.automaticallyWaitsToMinimizeStalling = false
        queuePlayer?.volume = 1.0

        // Setup buffer and status monitoring for this fresh item
        setupBufferMonitoring(for: playerItem, videoTitle: videoReel.title, index: currentIndex)
        setupStatusMonitoring(for: playerItem, videoTitle: videoReel.title, index: currentIndex, urlString: "\(urlString)_queue")

        // Setup video looping
        setupVideoLooping(for: playerItem, videoTitle: videoReel.title)

        // Setup player layer for current cell
        setupPlayerLayerForCurrentVideo()

        // Start playback
        queuePlayer?.play()

        // Monitor playback
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [weak self] in
            if let rate = self?.queuePlayer?.rate, rate > 0 {
                print("jilytest123 ✅ Video playing successfully at rate: \(rate)")
            } else {
                print("jilytest123 ⚠️ Video not playing, retrying...")
                self?.queuePlayer?.play()
            }
        }
    }

    private func createFreshPlayerItem(for url: URL, priority: PreloadPriority) -> AVPlayerItem {
        let playerItem = AVPlayerItem(url: url)

        // Optimize based on priority
        switch priority {
        case .immediate:
            playerItem.preferredForwardBufferDuration = 5.0 // Longer buffer for current video
        case .high:
            playerItem.preferredForwardBufferDuration = 3.0 // Medium buffer for next videos
        case .normal:
            playerItem.preferredForwardBufferDuration = 1.0 // Minimal buffer for background videos
        }

        playerItem.canUseNetworkResourcesForLiveStreamingWhilePaused = true

        return playerItem
    }

    private func setupVideoLooping(for playerItem: AVPlayerItem, videoTitle: String) {
        // Add observer for when video finishes playing
        NotificationCenter.default.addObserver(
            forName: .AVPlayerItemDidPlayToEndTime,
            object: playerItem,
            queue: .main
        ) { [weak self] _ in
            print("jilytest123 🔄 Video finished, looping: \(videoTitle)")

            // Seek back to beginning and continue playing
            self?.queuePlayer?.seek(to: .zero) { [weak self] _ in
                self?.queuePlayer?.play()
                print("jilytest123 ▶️ Video looped and playing again: \(videoTitle)")
            }
        }
    }

    private func setupPlayerLayerForCurrentVideo() {
        guard let queuePlayer = queuePlayer,
              let cell = collectionView.cellForItem(at: IndexPath(item: currentIndex, section: 0)) as? VideoReelCell else {
            print("jilytest123 ⚠️ Could not get cell for current video")
            return
        }

        // Update the cell's player layer with our queue player
        let videoReel = videoReels[currentIndex]
        let urlString = videoReel.videoURL.absoluteString

        let playerLayer: AVPlayerLayer
        if let existingLayer = playerLayers[urlString] {
            existingLayer.player = queuePlayer
            playerLayer = existingLayer
            print("jilytest123 🔄 Reusing player layer for: \(videoReel.title)")
        } else {
            playerLayer = AVPlayerLayer(player: queuePlayer)
            playerLayer.videoGravity = .resizeAspectFill
            playerLayers[urlString] = playerLayer
            print("jilytest123 🆕 Created new player layer for: \(videoReel.title)")
        }

        // Set the player layer on the cell
        cell.setPlayerLayer(playerLayer)
    }



    private func pauseAllVideos() {
        print("jilytest123 ⏸️ Pausing all videos")
        queuePlayer?.pause()
    }

    private func cleanupAllObservers() {
        print("jilytest123 🧹 Cleaning up all observers and resources")

        // Cleanup buffer observers
        for (key, observer) in bufferObservers {
            print("jilytest123 🧹 Invalidating buffer observer for: \(key)")
            observer.invalidate()
        }
        bufferObservers.removeAll()

        // Cleanup status observers
        for (key, observer) in statusObservers {
            print("jilytest123 🧹 Invalidating status observer for: \(key)")
            observer.invalidate()
        }
        statusObservers.removeAll()

        // Clear player items and layers
        print("jilytest123 🧹 Clearing \(playerItems.count) player items and \(playerLayers.count) player layers")
        playerItems.removeAll()
        playerLayers.removeAll()
    }

    private func cleanupNotificationObservers() {
        print("jilytest123 🧹 Cleaning up notification observers")
        NotificationCenter.default.removeObserver(self, name: .AVPlayerItemDidPlayToEndTime, object: nil)
    }
    
    private func updateCurrentIndex() {
        let visibleIndexPaths = collectionView.indexPathsForVisibleItems
        if let indexPath = visibleIndexPaths.first {
            let newIndex = indexPath.item
            if newIndex != currentIndex {
                print("jilytest123 📱 Index changed from \(currentIndex) to \(newIndex)")
                currentIndex = newIndex
            }
        }
    }
    
    // MARK: - Actions
    @objc private func backButtonTapped() {
        print("jilytest123 ❌ Close button tapped")
        pauseAllVideos()
        cleanupAllObservers()
        cleanupNotificationObservers()

        if let navigationController = navigationController {
            navigationController.popViewController(animated: true)
        } else {
            dismiss(animated: true)
        }
    }
}

// MARK: - UICollectionViewDataSource
extension VideoReelsViewController: UICollectionViewDataSource {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return videoReels.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "VideoReelCell", for: indexPath) as! VideoReelCell
        
        let videoReel = videoReels[indexPath.item]
        print("jilytest123 🔧 Configuring cell for index \(indexPath.item): \(videoReel.title)")
        
        cell.configure(with: videoReel)
        
        return cell
    }
}

// MARK: - UICollectionViewDelegate
extension VideoReelsViewController: UICollectionViewDelegate {
    func scrollViewDidEndDecelerating(_ scrollView: UIScrollView) {
        print("jilytest123 📱 Scroll ended decelerating")
        updateCurrentIndex()
        playCurrentVideo()
    }

    func scrollViewWillBeginDragging(_ scrollView: UIScrollView) {
        print("jilytest123 📱 Scroll will begin - pausing current video")
        pauseAllVideos()
    }

    func scrollViewDidEndDragging(_ scrollView: UIScrollView, willDecelerate decelerate: Bool) {
        if !decelerate {
            print("jilytest123 📱 Scroll ended without deceleration")
            updateCurrentIndex()
            playCurrentVideo()
        }
    }
}
