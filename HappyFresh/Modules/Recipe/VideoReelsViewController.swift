//
//  VideoReelsViewController.swift
//  HappyFresh
//
//  Created by Augment Agent on 2025-07-07.
//

import UIKit
import AVFoundation
import SnapKit

class VideoReelsViewController: UIViewController {

    // MARK: - UI Components
    private let collectionView = UICollectionView(frame: .zero, collectionViewLayout: UICollectionViewFlowLayout())
    private let loadingIndicator = UIActivityIndicatorView()
    private let errorView = UIView()
    private let errorLabel = UILabel()
    private let retryButton = UIButton()
    private let backButton = UIButton(type: .system)
    
    // MARK: - Properties
    private var videoReels: [VideoReel] = []
    private var currentIndex: Int = 0
    private var isLoading = false
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        setupCollectionView()
        loadVideoReels()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        
        // Hide navigation bar for full-screen experience
        navigationController?.setNavigationBarHidden(true, animated: animated)
        
        // Resume video if returning to screen
        if !videoReels.isEmpty {
            playCurrentVideo()
        }
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        
        // Pause all videos when leaving screen
        TikTokVideoManager.shared.pauseAllVideos()
        
        // Show navigation bar again
        navigationController?.setNavigationBarHidden(false, animated: animated)
    }
    
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        
        // Update collection view layout
        if let layout = collectionView.collectionViewLayout as? UICollectionViewFlowLayout {
            layout.itemSize = CGSize(width: view.frame.width, height: view.frame.height)
        }
    }
    
    // MARK: - Setup
    private func setupUI() {
        view.backgroundColor = .black

        // Setup tab bar item
        tabBarItem = UITabBarItem(
            title: "Recipes",
            image: UIImage(systemName: "play.rectangle"),
            selectedImage: UIImage(systemName: "play.rectangle.fill")
        )

        // Add subviews
        view.addSubview(collectionView)
        view.addSubview(loadingIndicator)
        view.addSubview(errorView)
        view.addSubview(backButton)
        errorView.addSubview(errorLabel)
        errorView.addSubview(retryButton)

        // Setup collection view
        collectionView.backgroundColor = .black
        collectionView.showsVerticalScrollIndicator = false
        collectionView.showsHorizontalScrollIndicator = false
        collectionView.isPagingEnabled = true
        
        // Setup back button
        backButton.setImage(UIImage(systemName: "chevron.left"), for: .normal)
        backButton.tintColor = .white
        backButton.backgroundColor = UIColor.black.withAlphaComponent(0.5)
        backButton.layer.cornerRadius = 20
        backButton.addTarget(self, action: #selector(backButtonTapped), for: .touchUpInside)

        // Setup error view
        errorView.isHidden = true
        errorView.backgroundColor = .black
        errorLabel.text = "Failed to load videos"
        errorLabel.textColor = .white
        errorLabel.textAlignment = .center
        errorLabel.numberOfLines = 0
        errorLabel.font = UIFont.systemFont(ofSize: 16)

        retryButton.setTitle("Retry", for: .normal)
        retryButton.setTitleColor(.white, for: .normal)
        retryButton.backgroundColor = UIColor.systemBlue
        retryButton.layer.cornerRadius = 8
        retryButton.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        retryButton.addTarget(self, action: #selector(retryButtonTapped), for: .touchUpInside)

        // Setup loading indicator
        loadingIndicator.style = .large
        loadingIndicator.color = .white
    }

    private func setupConstraints() {
        // Collection view - full screen
        collectionView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // Back button - top left corner with safe area
        backButton.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide.snp.top).offset(16)
            make.leading.equalToSuperview().offset(16)
            make.width.height.equalTo(40)
        }

        // Loading indicator - center
        loadingIndicator.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }

        // Error view - full screen
        errorView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        // Error label - center with padding
        errorLabel.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().offset(-30)
            make.leading.trailing.equalToSuperview().inset(20)
        }

        // Retry button - below error label
        retryButton.snp.makeConstraints { make in
            make.top.equalTo(errorLabel.snp.bottom).offset(20)
            make.centerX.equalToSuperview()
            make.width.equalTo(120)
            make.height.equalTo(44)
        }
    }

    private func setupCollectionView() {
        // Register TikTok-style cell
        collectionView.register(TikTokVideoReelCell.self, forCellWithReuseIdentifier: "TikTokVideoReelCell")

        // Setup layout for TikTok-style full-screen videos
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .vertical
        layout.minimumLineSpacing = 0
        layout.minimumInteritemSpacing = 0
        layout.itemSize = CGSize(width: view.frame.width, height: view.frame.height)

        collectionView.collectionViewLayout = layout
        collectionView.delegate = self
        collectionView.dataSource = self

        // Enable paging for TikTok-like experience
        collectionView.isPagingEnabled = true
        collectionView.showsVerticalScrollIndicator = false

        // Add gesture recognizer for tap to play/pause
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleTap(_:)))
        collectionView.addGestureRecognizer(tapGesture)
    }
    
    // MARK: - Data Loading
    private func loadVideoReels() {
        showLoading(true)
    
        // Remove artificial delay
        // DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [weak self] in
        self.videoReels = VideoReel.sampleReels
        self.showLoading(false)
        self.collectionView.reloadData()
    
        // Preload first few videos immediately for better UX
        self.preloadVideos()
    
        // Play first video immediately without delay
        // DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
        if !self.videoReels.isEmpty {
            self.playCurrentVideo()
        }
        // }
        // }
    }
    
    private func preloadVideos() {
        // Preload first 3 videos with TikTok manager for instant playback
        let preloadCount = min(3, videoReels.count)
        let urlsToPreload = Array(videoReels.prefix(preloadCount)).map { $0.videoURL }
        print("jilytest123 🚀 Preloading \(preloadCount) videos with TikTok manager")
        TikTokVideoManager.shared.preloadVideos(urls: urlsToPreload)
    }
    
    // MARK: - Video Control
    private func playCurrentVideo() {
        guard currentIndex < videoReels.count else {
            print("jilytest123 ❌ Current index out of bounds: \(currentIndex)/\(videoReels.count)")
            return
        }

        let indexPath = IndexPath(item: currentIndex, section: 0)
        guard let cell = collectionView.cellForItem(at: indexPath) as? TikTokVideoReelCell else {
            print("jilytest123 ❌ No TikTok cell found for index \(currentIndex)")
            return
        }

        print("jilytest123 🎬 Playing current video at index \(currentIndex)")
        cell.playVideo()
    }

    private func pauseCurrentVideo() {
        guard currentIndex < videoReels.count else { return }

        let indexPath = IndexPath(item: currentIndex, section: 0)
        guard let cell = collectionView.cellForItem(at: indexPath) as? TikTokVideoReelCell else {
            return
        }

        print("jilytest123 ⏸️ Pausing current video at index \(currentIndex)")
        cell.pauseVideo()
    }
    
    private func updateCurrentIndex() {
        let visibleIndexPaths = collectionView.indexPathsForVisibleItems
        if let indexPath = visibleIndexPaths.first {
            let previousIndex = currentIndex
            currentIndex = indexPath.item
            print("jilytest123 📱 Index changed from \(previousIndex) to \(currentIndex)")
        }
    }
    
    // MARK: - UI State
    private func showLoading(_ show: Bool) {
        isLoading = show
        loadingIndicator.isHidden = !show
        collectionView.isHidden = show
        errorView.isHidden = true
        
        if show {
            loadingIndicator.startAnimating()
        } else {
            loadingIndicator.stopAnimating()
        }
    }
    
    private func showError(_ message: String) {
        isLoading = false
        loadingIndicator.stopAnimating()
        loadingIndicator.isHidden = true
        collectionView.isHidden = true
        errorView.isHidden = false
        errorLabel.text = message
    }
    
    // MARK: - Actions
    @objc private func retryButtonTapped() {
        loadVideoReels()
    }
    
    @objc private func handleTap(_ gesture: UITapGestureRecognizer) {
        // We're removing the play/pause toggle functionality
        // This method can be left empty or removed entirely
    }
    
    @objc private func backButtonTapped() {
        navigationController?.popViewController(animated: true)
    }
}

// MARK: - UICollectionViewDataSource
extension VideoReelsViewController: UICollectionViewDataSource {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return videoReels.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "TikTokVideoReelCell", for: indexPath) as! TikTokVideoReelCell

        let videoReel = videoReels[indexPath.item]
        print("jilytest123 🔧 Configuring TikTok cell for index \(indexPath.item): \(videoReel.title)")
        cell.configure(with: videoReel)

        return cell
    }
}

// MARK: - UICollectionViewDelegate
extension VideoReelsViewController: UICollectionViewDelegate {
    func scrollViewDidEndDecelerating(_ scrollView: UIScrollView) {
        print("jilytest123 📱 Scroll ended decelerating")
        updateCurrentIndex()

        // Play video immediately for TikTok-like experience
        playCurrentVideo()

        // Preload next videos
        preloadNextVideos()
    }

    func scrollViewWillBeginDragging(_ scrollView: UIScrollView) {
        print("jilytest123 📱 Scroll will begin dragging")
        // Pause current video when user starts scrolling
        TikTokVideoManager.shared.pauseAllVideos()
    }

    func scrollViewDidEndDragging(_ scrollView: UIScrollView, willDecelerate decelerate: Bool) {
        print("jilytest123 📱 Scroll ended dragging, will decelerate: \(decelerate)")
        if !decelerate {
            updateCurrentIndex()
            playCurrentVideo()
            preloadNextVideos()
        }
    }
    
    private func preloadNextVideos() {
        // Preload current, next, and previous videos for smooth TikTok-like scrolling
        let preloadIndices = [
            max(0, currentIndex - 1),  // Previous
            currentIndex,              // Current
            min(videoReels.count - 1, currentIndex + 1),  // Next
            min(videoReels.count - 1, currentIndex + 2)   // Next + 1
        ]

        print("jilytest123 📱 Preloading videos for indices: \(preloadIndices)")

        let urlsToPreload = preloadIndices.compactMap { index in
            guard index < videoReels.count else { return nil }
            return videoReels[index].videoURL
        }

        TikTokVideoManager.shared.preloadVideos(urls: urlsToPreload)
    }
}

// MARK: - UICollectionViewDelegateFlowLayout
extension VideoReelsViewController: UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        return CGSize(width: view.frame.width, height: view.frame.height)
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, minimumLineSpacingForSectionAt section: Int) -> CGFloat {
        return 0
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, minimumInteritemSpacingForSectionAt section: Int) -> CGFloat {
        return 0
    }
}
