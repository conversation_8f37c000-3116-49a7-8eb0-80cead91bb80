//
//  VideoReelsViewController.swift
//  HappyFresh
//
//  Created by Augment Agent on 2025-07-07.
//

import UIKit
import AVFoundation
import SnapKit

class VideoReelsViewController: UIViewController {

    // MARK: - UI Components
    private let collectionView = UICollectionView(frame: .zero, collectionViewLayout: UICollectionViewFlowLayout())
    private let loadingIndicator = UIActivityIndicatorView()
    private let errorView = UIView()
    private let errorLabel = UILabel()
    private let retryButton = UIButton()
    private let backButton = UIButton(type: .system)
    
    // MARK: - Properties
    private var videoReels: [VideoReel] = []
    private var currentIndex: Int = 0
    private var isLoading = false
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        setupCollectionView()
        loadVideoReels()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        
        // Hide navigation bar for full-screen experience
        navigationController?.setNavigationBarHidden(true, animated: animated)
        
        // Resume video if returning to screen
        if !videoReels.isEmpty {
            playCurrentVideo()
        }
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        
        // Pause all videos when leaving screen
        VideoManager.shared.pauseAllVideos()
        
        // Show navigation bar again
        navigationController?.setNavigationBarHidden(false, animated: animated)
    }
    
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        
        // Update collection view layout
        if let layout = collectionView.collectionViewLayout as? UICollectionViewFlowLayout {
            layout.itemSize = CGSize(width: view.frame.width, height: view.frame.height)
        }
    }
    
    // MARK: - Setup
    private func setupUI() {
        view.backgroundColor = .black

        // Setup tab bar item
        tabBarItem = UITabBarItem(
            title: "Recipes",
            image: UIImage(systemName: "play.rectangle"),
            selectedImage: UIImage(systemName: "play.rectangle.fill")
        )

        // Add subviews
        view.addSubview(collectionView)
        view.addSubview(loadingIndicator)
        view.addSubview(errorView)
        view.addSubview(backButton)
        errorView.addSubview(errorLabel)
        errorView.addSubview(retryButton)

        // Setup collection view
        collectionView.backgroundColor = .black
        collectionView.showsVerticalScrollIndicator = false
        collectionView.showsHorizontalScrollIndicator = false
        collectionView.isPagingEnabled = true
        
        // Setup back button
        backButton.setImage(UIImage(systemName: "chevron.left"), for: .normal)
        backButton.tintColor = .white
        backButton.backgroundColor = UIColor.black.withAlphaComponent(0.5)
        backButton.layer.cornerRadius = 20
        backButton.addTarget(self, action: #selector(backButtonTapped), for: .touchUpInside)

        // Setup error view
        errorView.isHidden = true
        errorView.backgroundColor = .black
        errorLabel.text = "Failed to load videos"
        errorLabel.textColor = .white
        errorLabel.textAlignment = .center
        errorLabel.numberOfLines = 0
        errorLabel.font = UIFont.systemFont(ofSize: 16)

        retryButton.setTitle("Retry", for: .normal)
        retryButton.setTitleColor(.white, for: .normal)
        retryButton.backgroundColor = UIColor.systemBlue
        retryButton.layer.cornerRadius = 8
        retryButton.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        retryButton.addTarget(self, action: #selector(retryButtonTapped), for: .touchUpInside)

        // Setup loading indicator
        loadingIndicator.style = .large
        loadingIndicator.color = .white
    }

    private func setupConstraints() {
        // Collection view - full screen
        collectionView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        // Loading indicator - center
        loadingIndicator.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }

        // Error view - full screen
        errorView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        // Error label - center with padding
        errorLabel.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().offset(-30)
            make.leading.trailing.equalToSuperview().inset(20)
        }

        // Retry button - below error label
        retryButton.snp.makeConstraints { make in
            make.top.equalTo(errorLabel.snp.bottom).offset(20)
            make.centerX.equalToSuperview()
            make.width.equalTo(120)
            make.height.equalTo(44)
        }
    }

    private func setupCollectionView() {
        // Register cell
        collectionView.register(VideoReelCell.self, forCellWithReuseIdentifier: "VideoReelCell")

        // Setup layout
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .vertical
        layout.minimumLineSpacing = 0
        layout.minimumInteritemSpacing = 0
        layout.itemSize = CGSize(width: view.frame.width, height: view.frame.height)

        collectionView.collectionViewLayout = layout
        collectionView.delegate = self
        collectionView.dataSource = self

        // Add gesture recognizer for tap to play/pause
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleTap(_:)))
        collectionView.addGestureRecognizer(tapGesture)
    }
    
    // MARK: - Data Loading
    private func loadVideoReels() {
        showLoading(true)
    
        // Remove artificial delay
        // DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [weak self] in
        self.videoReels = VideoReel.sampleReels
        self.showLoading(false)
        self.collectionView.reloadData()
    
        // Preload first few videos immediately for better UX
        self.preloadVideos()
    
        // Play first video immediately without delay
        // DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
        if !self.videoReels.isEmpty {
            self.playCurrentVideo()
        }
        // }
        // }
    }
    
    private func preloadVideos() {
        // Increase preload count from 3 to 5 for smoother experience
        let preloadCount = min(5, videoReels.count)
        let urlsToPreload = Array(videoReels.prefix(preloadCount)).map { $0.videoURL }
        VideoManager.shared.preloadVideos(urls: urlsToPreload)
    }
    
    // MARK: - Video Control
    private func playCurrentVideo() {
        guard currentIndex < videoReels.count,
              let cell = collectionView.cellForItem(at: IndexPath(item: currentIndex, section: 0)) as? VideoReelCell else {
            return
        }
        
        cell.playVideo()
    }
    
    private func pauseCurrentVideo() {
        guard currentIndex < videoReels.count,
              let cell = collectionView.cellForItem(at: IndexPath(item: currentIndex, section: 0)) as? VideoReelCell else {
            return
        }
        
        cell.pauseVideo()
    }
    
    private func updateCurrentIndex() {
        let visibleIndexPaths = collectionView.indexPathsForVisibleItems
        if let indexPath = visibleIndexPaths.first {
            currentIndex = indexPath.item
        }
    }
    
    // MARK: - UI State
    private func showLoading(_ show: Bool) {
        isLoading = show
        loadingIndicator.isHidden = !show
        collectionView.isHidden = show
        errorView.isHidden = true
        
        if show {
            loadingIndicator.startAnimating()
        } else {
            loadingIndicator.stopAnimating()
        }
    }
    
    private func showError(_ message: String) {
        isLoading = false
        loadingIndicator.stopAnimating()
        loadingIndicator.isHidden = true
        collectionView.isHidden = true
        errorView.isHidden = false
        errorLabel.text = message
    }
    
    // MARK: - Actions
    @objc private func retryButtonTapped() {
        loadVideoReels()
    }
    
    @objc private func handleTap(_ gesture: UITapGestureRecognizer) {
        // We're removing the play/pause toggle functionality
        // This method can be left empty or removed entirely
    }
    
    @objc private func backButtonTapped() {
        navigationController?.popViewController(animated: true)
    }
}

// MARK: - UICollectionViewDataSource
extension VideoReelsViewController: UICollectionViewDataSource {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return videoReels.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "VideoReelCell", for: indexPath) as! VideoReelCell
        
        let videoReel = videoReels[indexPath.item]
        cell.configure(with: videoReel)
        
        return cell
    }
}

// MARK: - UICollectionViewDelegate
extension VideoReelsViewController: UICollectionViewDelegate {
    func scrollViewDidEndDecelerating(_ scrollView: UIScrollView) {
        updateCurrentIndex()

        // Small delay for smooth transition
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) { [weak self] in
            self?.playCurrentVideo()
        }

        // Preload next videos
        preloadNextVideos()
    }

    func scrollViewWillBeginDragging(_ scrollView: UIScrollView) {
        // Pause current video when user starts scrolling
        VideoManager.shared.pauseAllVideos()
    }

    func scrollViewDidEndDragging(_ scrollView: UIScrollView, willDecelerate decelerate: Bool) {
        if !decelerate {
            updateCurrentIndex()
            playCurrentVideo()
            preloadNextVideos()
        }
    }
    
    private func preloadNextVideos() {
        // Preload current, next, and previous videos for smooth scrolling
        let preloadIndices = [
            max(0, currentIndex - 1),  // Previous
            currentIndex,              // Current
            min(videoReels.count - 1, currentIndex + 1),  // Next
            min(videoReels.count - 1, currentIndex + 2)   // Next + 1
        ]

        for index in preloadIndices {
            if index < videoReels.count {
                let videoURL = videoReels[index].videoURL
                VideoManager.shared.preloadVideo(url: videoURL)
            }
        }
    }
}

// MARK: - UICollectionViewDelegateFlowLayout
extension VideoReelsViewController: UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        return CGSize(width: view.frame.width, height: view.frame.height)
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, minimumLineSpacingForSectionAt section: Int) -> CGFloat {
        return 0
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, minimumInteritemSpacingForSectionAt section: Int) -> CGFloat {
        return 0
    }
}
