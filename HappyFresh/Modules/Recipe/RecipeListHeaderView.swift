import UIKit
import SnapKit

class RecipeListHeaderView: UICollectionReusableView {
    static let reuseIdentifier = "RecipeListHeaderView"
    
    private let backgroundImageView: UIImageView = {
        let view = UIImageView()
        view.image = UIImage(named: "hf_img_recipe_header_store")
        return view
    }()
    
    private let titleLabel: UILabel = {
        let label = UILabel()
        label.text = NSLocalizedString("Masak apa hari ini?", comment: "")
        label.font = .hf_large_bold()
        label.textColor = .hf_grey_100()
        label.textAlignment = .center
        label.numberOfLines = 0
        return label
    }()
    
    private let subtitleLabel: UILabel = {
        let label = UILabel()
        label.text = NSLocalizedString("Temukan beragam inspirasi masak bareng HappyFresh", comment: "")
        label.font = .hf_micro()
        label.textColor = .hf_grey_100()
        label.textAlignment = .center
        label.numberOfLines = 0
        return label
    }()
    
    private let roundedView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.hf_white()
        view.cornerRadius = 16
        return view
    }()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupViews()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupViews()
    }
    
    private func setupViews() {
        addSubview(backgroundImageView)
        addSubview(titleLabel)
        addSubview(subtitleLabel)
        addSubview(roundedView)
        
        backgroundImageView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.height.equalTo(130)
        }
        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(safeAreaLayoutGuide.snp.top).inset(16)
            make.left.right.equalToSuperview().inset(16)
        }
        subtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(16)
            make.left.right.equalToSuperview().inset(16)
            make.bottom.lessThanOrEqualToSuperview().inset(32)
        }
        roundedView.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.height.equalTo(40)
            make.bottom.equalToSuperview().offset(16)
        }
    }
} 
