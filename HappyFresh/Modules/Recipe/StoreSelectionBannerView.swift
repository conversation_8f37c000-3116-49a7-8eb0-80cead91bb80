//
//  StoreSelectionBannerView.swift
//  HappyFresh
//
//  Created by Augment Agent on 2025-07-25.
//

import UIKit
import SnapKit

protocol StoreSelectionBannerViewDelegate: AnyObject {
    func storeSelectionBannerDidTapChangeStore()
}

class StoreSelectionBannerView: UIView {
    
    // MARK: - UI Components
    private let containerView = UIImageView()
    private let titleLabel = UILabel()
    private let subtitleLabel = UILabel()
    private let storeInfoView = UIView()
    private let storeImageView = UIImageView()
    private let storeNameLabel = UILabel()
    private let chevronImageView = UIImageView()
    
    // MARK: - Properties
    weak var delegate: StoreSelectionBannerViewDelegate?
    
    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        setupConstraints()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
        setupConstraints()
    }
    
    // MARK: - Setup
    private func setupUI() {
        backgroundColor = .clear
        
        // Setup container view
        containerView.image = UIImage(named: "hf_img_recipe_background_store")
        
        // Setup title label
        titleLabel.text = NSLocalizedString("Want to cook at home?", comment: "")
        titleLabel.font = .hf_small_bold()
        titleLabel.textColor = .hf_grey_100()
        titleLabel.numberOfLines = 1
        
        // Setup subtitle label
        subtitleLabel.text = NSLocalizedString("Choose a store to get the ingredients you need", comment: "")
        subtitleLabel.font = .hf_tiny()
        subtitleLabel.textColor = .hf_grey_100()
        subtitleLabel.numberOfLines = 2
        
        // Setup store info view
        storeInfoView.backgroundColor = .white
        storeInfoView.layer.cornerRadius = 6
        storeInfoView.layer.borderWidth = 1
        storeInfoView.layer.borderColor = UIColor.hf_grey_20().cgColor
        
        // Setup store image view
        storeImageView.contentMode = .scaleAspectFit
        
        // Setup store name label
        storeNameLabel.font = .hf_small()
        storeNameLabel.textColor = .hf_grey_100()
        storeNameLabel.numberOfLines = 1
        
        // Setup chevron image view
        chevronImageView.image = UIImage(systemName: "chevron.down")
        chevronImageView.tintColor = .gray
        chevronImageView.contentMode = .scaleAspectFit
        
        // Add tap gesture
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(didTapBanner))
        containerView.addGestureRecognizer(tapGesture)
        containerView.isUserInteractionEnabled = true
        
        // Add subviews
        addSubview(containerView)
        containerView.addSubview(titleLabel)
        containerView.addSubview(subtitleLabel)
        containerView.addSubview(storeInfoView)
        storeInfoView.addSubview(storeImageView)
        storeInfoView.addSubview(storeNameLabel)
        storeInfoView.addSubview(chevronImageView)
    }
    
    private func setupConstraints() {
        // Container view constraints
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // Title label constraints
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
        }
        
        // Subtitle label constraints
        subtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(4)
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
        }
        
        // Store info view constraints
        storeInfoView.snp.makeConstraints { make in
            make.top.equalTo(subtitleLabel.snp.bottom).offset(12)
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
            make.height.equalTo(58)
            make.bottom.equalToSuperview().offset(-16)
        }
        
        // Store image view constraints
        storeImageView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(12)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(42)
        }
        
        // Store name label constraints
        storeNameLabel.snp.makeConstraints { make in
            make.left.equalTo(storeImageView.snp.right).offset(12)
            make.centerY.equalToSuperview()
            make.right.equalTo(chevronImageView.snp.left).offset(-12)
        }
        
        // Chevron image view constraints
        chevronImageView.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-12)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(16)
        }
    }
    
    // MARK: - Public Methods
    func configure(with stockLocation: StockLocation?) {
        if let stockLocation = stockLocation {
            storeNameLabel.text = stockLocation.name
            
            // Load store image if available
            if let photoURL = stockLocation.supplier?.photo, !photoURL.isEmpty {
                loadStoreImage(from: photoURL)
            } else {
                // Use default store icon
                storeImageView.image = UIImage(named: "hf_img_recipe_store")
            }
        } else {
            storeNameLabel.text = NSLocalizedString("Select store", comment: "")
            storeImageView.image = UIImage(named: "hf_img_recipe_store")
        }
    }
    
    private func loadStoreImage(from urlString: String) {
        guard let url = URL(string: urlString) else { return }
        let placeholderImage = UIImage(named: "hf_img_recipe_store")
        storeImageView.add_setImage(with: url, placeholderImage: placeholderImage)
    }
    
    // MARK: - Actions
    @objc private func didTapBanner() {
        delegate?.storeSelectionBannerDidTapChangeStore()
    }
}
