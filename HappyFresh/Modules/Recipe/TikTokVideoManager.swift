//
//  TikTokVideoManager.swift
//  HappyFresh
//
//  Created by Augment Agent on 2025-07-17.
//

import Foundation
import UIKit
import AVFoundation

class TikTokVideoManager {
    static let shared = TikTokVideoManager()
    
    private var playerCache: NSCache<NSString, AVPlayer> = NSCache<NSString, AVPlayer>()
    private var playerKeys = [NSString]()
    private var playerLayerCache: NSCache<NSString, AVPlayerLayer> = NSCache<NSString, AVPlayerLayer>()
    private var currentlyPlayingPlayer: AVPlayer?
    private var preloadQueue: DispatchQueue
    
    private init() {
        // Configure caches
        playerCache.countLimit = 10 // Cache up to 10 players
        playerLayerCache.countLimit = 10 // Cache up to 10 layers
        
        // Create high-priority queue for video operations
        preloadQueue = DispatchQueue(label: "com.happyfresh.tiktok.video", qos: .userInitiated)
        
        // Setup audio session for video playback
        setupAudioSession()
        
        // Listen for app lifecycle events
        setupNotifications()
        
        // Start preloading videos immediately
        startGlobalVideoPreloading()
    }
    
    // MARK: - Public Methods
    
    func getPlayer(for videoURL: URL) -> AVPlayer {
        let cacheKey = videoURL.absoluteString as NSString
        
        if let cachedPlayer = playerCache.object(forKey: cacheKey) {
            print("jilytest123 🔄 Using cached AVPlayer for: \(videoURL.lastPathComponent)")
            // Reset to beginning for consistent playback
            cachedPlayer.seek(to: .zero, toleranceBefore: .zero, toleranceAfter: .zero)
            return cachedPlayer
        }
        
        print("jilytest123 🆕 Creating new AVPlayer for: \(videoURL.lastPathComponent)")
        
        // Create optimized player item
        let playerItem = createOptimizedPlayerItem(for: videoURL)
        let player = AVPlayer(playerItem: playerItem)
        
        // Configure player for TikTok-like behavior
        configurePlayerForTikTokExperience(player)
        
        // Cache the player
        playerCache.setObject(player, forKey: cacheKey)
        playerKeys.append(cacheKey)
        
        return player
    }
    
    func getPlayerLayer(for videoURL: URL) -> AVPlayerLayer {
        let cacheKey = videoURL.absoluteString as NSString
        
        if let cachedLayer = playerLayerCache.object(forKey: cacheKey) {
            print("jilytest123 🔄 Using cached AVPlayerLayer for: \(videoURL.lastPathComponent)")
            return cachedLayer
        }
        
        print("jilytest123 🆕 Creating new AVPlayerLayer for: \(videoURL.lastPathComponent)")
        
        let player = getPlayer(for: videoURL)
        let playerLayer = AVPlayerLayer(player: player)
        playerLayer.videoGravity = .resizeAspectFill // TikTok-style fill
        
        // Cache the layer
        playerLayerCache.setObject(playerLayer, forKey: cacheKey)
        
        return playerLayer
    }
    
    func preloadVideos(urls: [URL]) {
        print("jilytest123 🚀 Preloading \(urls.count) videos with optimized AVPlayer")
        
        // Preload first video synchronously for immediate playback
        if let firstURL = urls.first {
            let firstPlayer = getPlayer(for: firstURL)
            print("jilytest123 📱 First AVPlayer created synchronously: \(firstURL.lastPathComponent)")
            
            // Force player to start loading content
            if let playerItem = firstPlayer.currentItem {
                print("jilytest123 ⏳ Forcing first video to load")
                playerItem.asset.loadValuesAsynchronously(forKeys: ["playable"]) {
                    print("jilytest123 ✅ First video asset loaded")
                }
            }
        }
        
        // Preload remaining videos asynchronously
        let remainingURLs = Array(urls.dropFirst())
        for url in remainingURLs {
            preloadQueue.async { [weak self] in
                guard let self = self else { return }
                let player = self.getPlayer(for: url)
                print("jilytest123 📱 AVPlayer created asynchronously: \(url.lastPathComponent)")
            }
        }
    }
    
    func playVideo(player: AVPlayer) {
        print("jilytest123 🎬 PlayVideo called with AVPlayer")
        
        // Pause any currently playing video
        pauseCurrentVideo()
        
        // Set as current player
        currentlyPlayingPlayer = player
        
        // Play the video
        player.play()
        print("jilytest123 ▶️ AVPlayer.play() called")
        
        // Monitor playback state
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            let isPlaying = player.rate > 0
            print("jilytest123 📈 AVPlayer isPlaying after 0.5s: \(isPlaying)")
            if !isPlaying {
                print("jilytest123 ⚠️ AVPlayer not playing, trying again")
                player.play()
            }
        }
    }
    
    func pauseCurrentVideo() {
        if let currentPlayer = currentlyPlayingPlayer {
            print("jilytest123 ⏸️ Pausing current AVPlayer")
            currentPlayer.pause()
        }
        currentlyPlayingPlayer = nil
    }
    
    func pauseAllVideos() {
        print("jilytest123 🎥 Pausing all AVPlayers")
        
        // Pause all cached players
//        playerCache.enumerateKeysAndObjects { (key, player, stop) in
//            player.pause()
//        }
        for key in playerKeys {
            if let player = playerCache.object(forKey: key) {
                player.pause()
            }
        }
        currentlyPlayingPlayer = nil
    }
    
    func seekToBeginning(player: AVPlayer) {
        print("jilytest123 ⏪ Seeking AVPlayer to beginning")
        player.seek(to: .zero, toleranceBefore: .zero, toleranceAfter: .zero)
    }
    
    func clearCache() {
        print("jilytest123 🗑️ Clearing AVPlayer cache")
        pauseAllVideos()
        playerCache.removeAllObjects()
        playerLayerCache.removeAllObjects()
    }
    
    // MARK: - Private Methods
    
    private func createOptimizedPlayerItem(for url: URL) -> AVPlayerItem {
        let playerItem = AVPlayerItem(url: url)
        
        // Optimize for faster loading and playback
        playerItem.preferredForwardBufferDuration = 1.0 // Small buffer for faster start
        playerItem.canUseNetworkResourcesForLiveStreamingWhilePaused = false
        
        return playerItem
    }
    
    private func configurePlayerForTikTokExperience(_ player: AVPlayer) {
        print("jilytest123 ⚙️ Configuring AVPlayer for TikTok experience")
        
        // Configure for responsive playback
        player.automaticallyWaitsToMinimizeStalling = false
        player.volume = 1.0
        player.actionAtItemEnd = .pause
        
        // Optimize for low latency on supported iOS versions
        if #available(iOS 15.0, *) {
            player.audiovisualBackgroundPlaybackPolicy = .pauses
        }
    }
    
    private func startGlobalVideoPreloading() {
        // Preload first 3 videos immediately when VideoManager is initialized
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) { [weak self] in
            self?.preloadInitialVideosGlobally()
        }
    }
    
    private func preloadInitialVideosGlobally() {
        // Get sample video URLs and preload them
        let sampleReels = VideoReel.sampleReels
        let firstThreeURLs = Array(sampleReels.prefix(3)).map { $0.videoURL }
        
        print("jilytest123 🌍 Global preloading started for \(firstThreeURLs.count) videos")
        preloadVideos(urls: firstThreeURLs)
    }
    
    private func setupAudioSession() {
        do {
            try AVAudioSession.sharedInstance().setCategory(.playback, mode: .default, options: [.mixWithOthers])
            try AVAudioSession.sharedInstance().setActive(true)
            print("jilytest123 🔊 Audio session configured for video playback")
        } catch {
            print("jilytest123 ❌ Failed to setup audio session: \(error)")
        }
    }

    private func setupNotifications() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appDidEnterBackground),
            name: UIApplication.didEnterBackgroundNotification,
            object: nil
        )

        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appWillEnterForeground),
            name: UIApplication.willEnterForegroundNotification,
            object: nil
        )
    }

    @objc private func appDidEnterBackground() {
        print("jilytest123 📱 App entered background, pausing all videos")
        pauseAllVideos()
    }

    @objc private func appWillEnterForeground() {
        print("jilytest123 📱 App will enter foreground")
        // Videos will be resumed when cells become visible again
    }

    deinit {
        NotificationCenter.default.removeObserver(self)
    }
}
