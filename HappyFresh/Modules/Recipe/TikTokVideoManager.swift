//
//  TikTokVideoManager.swift
//  HappyFresh
//
//  Created by Augment Agent on 2025-07-17.
//

import Foundation
import VersaPlayer
import UIKit
import AVFoundation

class TikTokVideoManager {
    static let shared = TikTokVideoManager()
    
    private var playerCache: NSCache<NSString, VersaPlayer> = NSCache<NSString, VersaPlayer>()
    private var currentlyPlayingPlayer: VersaPlayer?
    private var preloadQueue: DispatchQueue
    
    private init() {
        // Configure cache
        playerCache.countLimit = 10 // Cache up to 10 players
        
        // Create high-priority queue for video operations
        preloadQueue = DispatchQueue(label: "com.happyfresh.tiktok.video", qos: .userInitiated)
        
        // Setup audio session for video playback
        setupAudioSession()
        
        // Listen for app lifecycle events
        setupNotifications()
    }
    
    // MARK: - Public Methods
    
    func getPlayer(for videoURL: URL) -> VersaPlayer {
        let cacheKey = videoURL.absoluteString as NSString
        
        if let cachedPlayer = playerCache.object(forKey: cacheKey) {
            print("jilytest123 🔄 Using cached VersaPlayer for: \(videoURL.lastPathComponent)")
            return cachedPlayer
        }
        
        print("jilytest123 🆕 Creating new VersaPlayer for: \(videoURL.lastPathComponent)")
        
        // Create new VersaPlayer with optimized settings
        let player = VersaPlayer()
        
        // Configure player for TikTok-like behavior
        configurePlayerForTikTokExperience(player)
        
        let item = VersaPlayerItem(url: videoURL)
        player.set(item: item)
        
        // Cache the player
        playerCache.setObject(player, forKey: cacheKey)
        
        return player
    }
    
    func preloadVideos(urls: [URL]) {
        print("jilytest123 🚀 Preloading \(urls.count) videos with VersaPlayer")
        
        // Preload first video synchronously for immediate playback
        if let firstURL = urls.first {
            let firstPlayer = getPlayer(for: firstURL)
            print("jilytest123 📱 First VersaPlayer created synchronously: \(firstURL.lastPathComponent)")
        }
        
        // Preload remaining videos asynchronously
        let remainingURLs = Array(urls.dropFirst())
        for url in remainingURLs {
            preloadQueue.async { [weak self] in
                guard let self = self else { return }
                let player = self.getPlayer(for: url)
                print("jilytest123 📱 VersaPlayer created asynchronously: \(url.lastPathComponent)")
            }
        }
    }
    
    func playVideo(player: VersaPlayer) {
        print("jilytest123 🎬 PlayVideo called with VersaPlayer")
        
        // Pause any currently playing video
        pauseCurrentVideo()
        
        // Set as current player
        currentlyPlayingPlayer = player
        
        // Play the video
        player.play()
        print("jilytest123 ▶️ VersaPlayer.play() called")
        
        // Monitor playback state
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            let isPlaying = player.isPlaying
            print("jilytest123 📈 VersaPlayer isPlaying after 0.5s: \(isPlaying)")
            if !isPlaying {
                print("jilytest123 ⚠️ VersaPlayer not playing, trying again")
                player.play()
            }
        }
    }
    
    func pauseCurrentVideo() {
        if let currentPlayer = currentlyPlayingPlayer {
            print("jilytest123 ⏸️ Pausing current VersaPlayer")
            currentPlayer.pause()
        }
        currentlyPlayingPlayer = nil
    }
    
    func pauseAllVideos() {
        print("jilytest123 🎥 Pausing all VersaPlayers")
        
        // Pause all cached players
        playerCache.enumerateKeysAndObjects { (key, player, stop) in
            player.pause()
        }
        currentlyPlayingPlayer = nil
    }
    
    func seekToBeginning(player: VersaPlayer) {
        print("jilytest123 ⏪ Seeking VersaPlayer to beginning")
        player.seek(to: 0)
    }
    
    func clearCache() {
        print("jilytest123 🗑️ Clearing VersaPlayer cache")
        pauseAllVideos()
        playerCache.removeAllObjects()
    }
    
    // MARK: - Private Methods
    
    private func configurePlayerForTikTokExperience(_ player: VersaPlayer) {
        // Configure for smooth TikTok-like playback
        player.looping = true // Auto-loop videos like TikTok
        player.autoplay = false // We'll control playback manually
        player.mute = false // Allow sound
        
        // Set playback rate for normal speed
        player.rate = 1.0
        
        // Configure for better performance
        player.fillMode = .resizeAspectFill // Fill the screen like TikTok
    }
    
    private func setupAudioSession() {
        do {
            try AVAudioSession.sharedInstance().setCategory(.playback, mode: .default, options: [.mixWithOthers])
            try AVAudioSession.sharedInstance().setActive(true)
            print("jilytest123 🔊 Audio session configured for video playback")
        } catch {
            print("jilytest123 ❌ Failed to setup audio session: \(error)")
        }
    }
    
    private func setupNotifications() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appDidEnterBackground),
            name: UIApplication.didEnterBackgroundNotification,
            object: nil
        )
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appWillEnterForeground),
            name: UIApplication.willEnterForegroundNotification,
            object: nil
        )
    }
    
    @objc private func appDidEnterBackground() {
        print("jilytest123 📱 App entered background, pausing all videos")
        pauseAllVideos()
    }
    
    @objc private func appWillEnterForeground() {
        print("jilytest123 📱 App will enter foreground")
        // Videos will be resumed when cells become visible again
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
}

// MARK: - VersaPlayer Extensions
extension VersaPlayer {
    var isPlaying: Bool {
        return rate > 0 && !isBuffering
    }
}
