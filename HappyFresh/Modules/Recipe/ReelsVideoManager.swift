//
//  ReelsVideoManager.swift
//  HappyFresh
//
//  Created by Augment Agent on 2025-07-17.
//

import Foundation
import AVFoundation
import UIKit

class ReelsVideoManager {
    static let shared = ReelsVideoManager()
    
    private var players: [String: AVPlayer] = [:]
    private var currentlyPlayingURL: String?
    private let preloadQueue = DispatchQueue(label: "com.happyfresh.reels.preload", qos: .userInitiated)
    
    private init() {
        setupAudioSession()
        setupNotifications()
    }
    
    // MARK: - Public Methods
    
    func getPlayer(for url: URL) -> AVPlayer {
        let urlString = url.absoluteString
        
        if let existingPlayer = players[urlString] {
            print("jilytest123 🔄 Reusing player for: \(url.lastPathComponent)")
            return existingPlayer
        }
        
        print("jilytest123 🆕 Creating new player for: \(url.lastPathComponent)")
        
        let playerItem = AVPlayerItem(url: url)
        playerItem.preferredForwardBufferDuration = 2.0
        
        let player = AVPlayer(playerItem: playerItem)
        player.automaticallyWaitsToMinimizeStalling = false
        player.volume = 1.0
        
        players[urlString] = player
        
        return player
    }
    
    func playVideo(url: URL) {
        let urlString = url.absoluteString
        
        print("jilytest123 ▶️ Playing video: \(url.lastPathComponent)")
        
        // Pause currently playing video
        if let currentURL = currentlyPlayingURL,
           let currentPlayer = players[currentURL] {
            currentPlayer.pause()
            print("jilytest123 ⏸️ Paused previous video")
        }
        
        // Play new video
        let player = getPlayer(for: url)
        player.seek(to: .zero)
        player.play()
        
        currentlyPlayingURL = urlString
        
        // Monitor playback
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            if player.rate > 0 {
                print("jilytest123 ✅ Video playing successfully")
            } else {
                print("jilytest123 ⚠️ Video not playing, retrying...")
                player.play()
            }
        }
    }
    
    func pauseCurrentVideo() {
        guard let currentURL = currentlyPlayingURL,
              let player = players[currentURL] else { return }
        
        print("jilytest123 ⏸️ Pausing current video")
        player.pause()
    }
    
    func pauseAllVideos() {
        print("jilytest123 ⏸️ Pausing all videos")
        players.values.forEach { $0.pause() }
        currentlyPlayingURL = nil
    }
    
    func preloadVideos(urls: [URL]) {
        print("jilytest123 🚀 Preloading \(urls.count) videos")
        
        preloadQueue.async { [weak self] in
            for url in urls {
                _ = self?.getPlayer(for: url)
            }
        }
    }
    
    func clearCache() {
        print("jilytest123 🗑️ Clearing video cache")
        pauseAllVideos()
        players.removeAll()
    }
    
    // MARK: - Private Methods
    
    private func setupAudioSession() {
        do {
            try AVAudioSession.sharedInstance().setCategory(.playback, mode: .default, options: [])
            try AVAudioSession.sharedInstance().setActive(true)
            print("jilytest123 🔊 Audio session configured")
        } catch {
            print("jilytest123 ❌ Audio session setup failed: \(error)")
        }
    }
    
    private func setupNotifications() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appDidEnterBackground),
            name: UIApplication.didEnterBackgroundNotification,
            object: nil
        )
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appWillEnterForeground),
            name: UIApplication.willEnterForegroundNotification,
            object: nil
        )
    }
    
    @objc private func appDidEnterBackground() {
        print("jilytest123 📱 App backgrounded - pausing videos")
        pauseAllVideos()
    }
    
    @objc private func appWillEnterForeground() {
        print("jilytest123 📱 App foregrounded")
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
}
