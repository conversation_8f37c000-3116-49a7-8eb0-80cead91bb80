//
//  DeliveryTimeViewModel.swift
//  HappyFresh
//
//  Created by <PERSON><PERSON> on 20/06/19.
//  Copyright © 2019 HappyFresh Inc. All rights reserved.
//

import Foundation
import ReactiveCocoa
import ReactiveSwift
import Result

class DeliveryOptionsViewModel {
    
    let isLoading = MutableProperty<Bool>(true)
    let expressIsLoading = MutableProperty<Bool>(true)

    var updateDeliverySlotOrderStartedBlock: (() -> Void)?
    var updateDeliverySlotOrderSuccessBlock: ((_ slot: Slot?, _ onDemandSLAMinutes: NSNumber?) -> Void)?
    var updateDeliverySlotOrderFailedBlock: ((_ proceedOrder: Bool, _ errorMessage: String?) -> Void)?
    var basketInfo: BasketInfo?
    
    @MutableProducer(false) var isUpdateShipmentLoading: SignalProducer<Bool, NoError>
    @MutableProducer(false) var isLookingForDriver: SignalProducer<Bool, NoError>
    @MutableProducer(false) var isLookingForDriverFailed: SignalProducer<Bool, NoError>
    @MutableProducer(false) var isUpdateShipmentSuccess: SignalProducer<Bool, NoError>
    @MutableProducer(nil) var updateShipmentErrorMessage: SignalProducer<String?, NoError>
    @MutableProducer(false) var isConfirmingExpressShipment: SignalProducer<Bool, NoError>

    private (set) var dateViewModels = MutableProperty<[DeliveryTimeDateViewModel]>([])
    private (set) var slotViewModels = MutableProperty<[DeliveryOptionCellViewModel]>([])
    private (set) var operationMessage = MutableProperty<String?>(nil)
    private (set) var selectedDateViewModel = MutableProperty<DeliveryTimeDateViewModel?>(nil)

    // For tracking purpose, we track day diff between today and earliest day when slot available
    private (set) var earliestAvailableSlotDay: NSNumber?
    
    var onDemandCancelled = false
    var order: Order
    private var location: CLLocation?
    var stockLocationId: NSNumber
    var supplierId: NSNumber? {
        order.currentStockLocation()?.supplier?.supplierID
    }
    private var selectedSlot: Slot?
    private var dateSlotCache: [Date: [Slot]] = [:]
    private var isFetching: [Date: Bool] = [:]
    private var smallBasketExperiment: SmallBasketExperimentType?
    private var isExpressLoaded: Bool = false
    private var isSlotLoaded: Bool = false
    var isDateSelected: Bool = false
    var isExpressEnabled: Bool {
        return onDemandConfig?.enable ?? false
    }
    var onDemandConfig: OnDemandConfig?
    var expressConfirmationTitle: String {
        isExpressEnabled ? NSLocalizedString("One last thing...", comment: "") :
            NSLocalizedString("Yay! Express delivery slot found!", comment: "")
    }
    var expressConfirmationSubtitle: String {
        isExpressEnabled ? String(format: NSLocalizedString("%@ minute deliveries cannot be changed or cancelled.", comment: "%@ will be replaced by the minute."),"\(onDemandConfig?.onDemandSLAMinutes ?? 30)") :
        String(format: NSLocalizedString("We will start immediately after payment is completed. You will receive your order before %@.", comment: ""), selectedSlot?.displayShortEndShippingTimeWithPeriod() ?? "")
    }
    var expressConfirmationImageString: String? {
        onDemandConfig?.onDemandSLAImageURL ?? "hf_img_found_driver_120px"
    }
    var expressConfirmationButtonTitle: String {
        isExpressEnabled ? NSLocalizedString("Continue", comment: "") : NSLocalizedString("Let's go", comment: "")
    }
    
    lazy var sectionsViewModel: SignalProducer<[DeliveryOptionSectionViewModel], NoError> = {
        SignalProducer.combineLatest(expressSectionViewModel, expressSlotViewModel, slotViewModels, selectedDateViewModel.producer)
            .map { [weak self] (expressSectionViewModel, expressSlotViewModel, slotViewModels, selectedDateViewModel) -> [DeliveryOptionSectionViewModel] in
                var sections: [DeliveryOptionSectionViewModel] = []
                if let expressSectionViewModel = expressSectionViewModel, let selectedDate = selectedDateViewModel?.date, selectedDate.isToday {
                    sections.append(expressSectionViewModel)
                }
                if (!slotViewModels.isEmpty) {
                    sections.append(DeliveryOptionSectionViewModel(type: .scheduled))
                }
                return sections
        }
    }()
    
    lazy var expressSectionViewModel: SignalProducer<DeliveryOptionSectionViewModel?, NoError> = {
        SignalProducer { [weak self] (observer, _) in
            API.sharedInstance()
                .getStoreOnDemandConfiguration(stockLocationID: self?.stockLocationId ?? 0,
                                               fetchSuccess: { (onDemandConfig) in
                                                if onDemandConfig.enable ?? false {
                                                    self?.onDemandConfig = onDemandConfig
                                                    var expressSection = DeliveryOptionSectionViewModel(
                                                        type: .express,
                                                        onDemandSLAMinutes: onDemandConfig.onDemandSLAMinutes,
                                                        onDemandSLAImageURL: onDemandConfig.onDemandSLAImageURL,
                                                        onDemandUnavailableImageURL: onDemandConfig.onDemandUnavailableImageURL,
                                                        expressTooltip: onDemandConfig.expressToolTip,
                                                        expressSubtitle: onDemandConfig.expressSubtitle,
                                                        expressSlotTitle: onDemandConfig.expressSlotTitle)
                                                    observer.send(value: expressSection)
                                                } else {
                                                    observer.send(value:nil)
                                                    observer.sendCompleted()
                                                    self?.expressIsLoading.value = false
                                                }
                                               }) { (errorMessage) in
                    observer.send(value:nil)
                    observer.sendCompleted()
            }
        }.replayLazily(upTo: 1)
    }()
    
    var isFromMergedCheckoutScreen: Bool
    let isUseRelativeExpressEta: Bool // false = "Receive by ..:..", true = "In ... minutes"
    var showVIPSlotAvailabilityPopup: (() -> Void)?
    var checkoutConfiguration: CheckoutConfiguration?
    var isExtraSlotsAvailable: Bool {
        checkoutConfiguration?.isExtraSlotsAvailable ?? false
    }

    init(
        order: Order,
        stockLocationId: NSNumber,
        location: CLLocation?,
        selectedSlot: Slot?,
        smallBasketExperiment: SmallBasketExperimentType?,
        isFromMergedCheckoutScreen: Bool,
        isUseRelativeExpressEta: Bool,
        checkoutConfiguration: CheckoutConfiguration?
    ) {
        self.location = location
        self.order = order
        self.stockLocationId = stockLocationId
        self.selectedSlot = selectedSlot
        self.smallBasketExperiment = smallBasketExperiment
        self.isFromMergedCheckoutScreen = isFromMergedCheckoutScreen
        self.isUseRelativeExpressEta = isUseRelativeExpressEta
        self.checkoutConfiguration = checkoutConfiguration

        SignalProducer.combineLatest(dateViewModels.producer,
                                     slotViewModels.producer,
                                     expressSlotViewModel.producer)
            .delay(0.1, on: QueueScheduler.main)
            .startWithValues { [weak self] dateViewModels, slotViewModels, expressSlotViewModel in
                guard let self = self,
                      slotViewModels.isEmpty,
                      !dateViewModels.isEmpty,
                      !self.isDateSelected,
                      self.selectedSlot == nil else { return }
                
                if self.selectedDateViewModel.value?.date.isToday ?? false {
                    if expressSlotViewModel == nil && (self.isExpressLoaded && self.isSlotLoaded) {
                        self.openNextPage()
                    }
                } else {
                    if self.isSlotLoaded {
                        self.openNextPage()
                    }
                }
            }
    }
    
    lazy var expressSlotViewModel: SignalProducer<DeliveryOptionCellViewModel?, NoError> = {
            SignalProducer.combineLatest(
                expressSectionViewModel.skipNil(),
                isLookingForDriverFailed,
                selectedDateViewModel.producer
                    .filter({ $0?.date.isToday ?? false })
                    .debounce(0.3, on: QueueScheduler.main))
                .flatMap(FlattenStrategy.latest) { [weak self] (expressSectionViewModel, lookingForDriverFailed, selectedSlot) ->
                    SignalProducer<(DeliveryOptionSectionViewModel, Bool, Slot?), NoError> in
                    guard let self = self else { return SignalProducer.empty }
                    self.isExpressLoaded = false
                    expressSectionViewModel.isLoading = true
                    return SignalProducer.combineLatest(
                        SignalProducer(value: expressSectionViewModel),
                        SignalProducer(value: lookingForDriverFailed),
                        self.getExpressSlot())
                }
                .map { [weak self] (expressSectionViewModel, lookingForDriverFailed, slot) -> DeliveryOptionCellViewModel? in
                    expressSectionViewModel.isLoading = false
                    self?.isExpressLoaded = true
                    if let slot = slot {
                        expressSectionViewModel.isAvailable = slot.available && !lookingForDriverFailed
                        expressSectionViewModel.unavailableReason = slot.unavailableReason
                        self?.expressIsLoading.value = false
                        if slot.available && !lookingForDriverFailed {
                            let viewModel = DeliveryOptionCellViewModel(
                                slot: slot,
                                timeZone: self?.order.timeZone,
                                selected: false,
                                isExpress: true,
                                expressTitle: self?.onDemandConfig?.expressSlotTitle
                            )
                            if let startTime = slot.startTime,
                               let selectedSlot = self?.selectedSlot,
                               let selectedStartTime = selectedSlot.startTime,
                               selectedSlot.isOnDemand  {
                                // for express slot, keep selected state on 5 minute range
                                viewModel.selected = startTime.timeIntervalSince(selectedStartTime) < (5 * 60)
                            }
                            return viewModel
                        }
                    }
                    self?.expressIsLoading.value = false
                    return nil
                }.prefix(value: nil)
                .replayLazily(upTo: 1)
        }()
    
    private func getExpressSlot() -> SignalProducer<Slot?, NoError> {
        SignalProducer { [weak self] (observer, _) in
            API.sharedInstance()
                .getAvailableOnDemandSlot(orderNumber: self?.order.number ?? "",
                                          stockLocationID: self?.stockLocationId ?? 0,
                                          fetchSuccess: { (slot) in
                                            observer.send(value: slot)
                                          }) { (errorMessage) in
                                                observer.send(value:nil)
                                                observer.sendCompleted()
                                          };
        }
    }

    private func saveSlotViewModels(slots: [Slot]) {
        var viewModels: [DeliveryOptionCellViewModel] = []

        slots.forEach { (slot) in
            var isSelected = false
            
            if let startTime = slot.startTime, let selectedSlotDate = selectedSlot?.startTime {
                if Calendar.current.isDate(startTime, inSameDayAs: selectedSlotDate) {
                    isSelected = Calendar.current.isDate(startTime, equalTo: selectedSlotDate, toGranularity: .hour)
                }
            }
            
            viewModels.append(DeliveryOptionCellViewModel(slot: slot, timeZone: order.timeZone, selected: isSelected))
        }

        if !slots.isEmpty, earliestAvailableSlotDay == nil, let selectedDate = selectedDateViewModel.value?.date {
            updateEarliestAvailableSlotDay(date: selectedDate)
        }

        slotViewModels.value = viewModels
        isLoading.value = false

        let isVIPSlotAvailable = slots.filter { $0.isVIP }.count > 0
        if isVIPSlotAvailable && !HFUserDefault.sharedInstance().isVIPOnlySlotPopupShown {
            showVIPSlotAvailabilityPopup?()
            HFUserDefault.sharedInstance().isVIPOnlySlotPopupShown = true
        }
    }

    func selectDate(at index: Int) {
        // Validate index bounds
        guard index >= 0 && index < dateViewModels.value.count else {
            isLoading.value = false
            return
        }

        isLoading.value = true

        // Check if the same date is already selected
        if selectedDateViewModel.value?.date == dateViewModels.value[index].date {
            isLoading.value = false
            return
        }

        // Update selection state for all date view models
        for i in 0..<dateViewModels.value.count {
            dateViewModels.value[i].isSelected = (i == index)
        }

        // Set the selected date and fetch slots
        selectedDateViewModel.value = dateViewModels.value[index]
        fetchSlots(date: dateViewModels.value[index].date)
    }
    
    private func openNextPage() {
        if let index = self.selectedDateIndexPath()?.item, index < dateViewModels.value.count - 1 {
            self.selectDate(at: index + 1)
        }
    }
    
    func selectSlot(at rowIndex: Int) {
        for index in 0..<slotViewModels.value.count {
            slotViewModels.value[index].selected = index == rowIndex
        }
    }
    
    func selectedDateIndexPath() -> IndexPath? {
        for index in 0..<dateViewModels.value.count {
            if (dateViewModels.value[index].isSelected) {
                return IndexPath(item: index, section: 0)
            }
        }
        
        return nil
    }

    private func saveDateViewModels(slotDates: [SlotDate]) {
        let dateViewModels = slotDates.map { slotDate in
            let isSelected = {
                guard let selectedSlotStartTime = selectedSlot?.startTime else {
                    return false
                }

                return Calendar.current.isDate(slotDate.date, inSameDayAs: selectedSlotStartTime)
            }()

            return DeliveryTimeDateViewModel(slotDate: slotDate, isSelected: isSelected)
        }

        self.dateViewModels.value = dateViewModels

        if !slotDates.isEmpty {
            // Find the index of the selected date, or default to 0 if none is selected
            let selectedIndex = selectedDateIndexPath()?.item ?? 0

            // Ensure the index is valid before selecting
            if selectedIndex < dateViewModels.count {
                selectDate(at: selectedIndex)
            } else {
                // Fallback to first date if selected index is invalid
                selectDate(at: 0)
            }
        } else {
            isLoading.value = false
        }
    }
    
    func selectedTimeIndex() -> Int? {
        for index in 0..<slotViewModels.value.count {
            if (slotViewModels.value[index].selected) {
                return index
            }
        }
        
        return nil
    }
    
    func fetchSlotDates() {
        if !API.sharedInstance().isOnline() {
            Popup.show(NSLocalizedString("No Connection", comment: ""), message: NSLocalizedString("We cannot carry out this request because your phone is not connected to the internet. Please connect to wifi or turn on your mobile data.", comment: ""), dismissViewController: false)
            return
        }
        
        isLoading.value = true

        API.sharedInstance()
            .fetchAvailableSlotDates(stockLocationID: stockLocationId.intValue) { [weak self] slotDates in
                self?.saveDateViewModels(slotDates: slotDates)
            } failure: { [weak self] errorMessage in
                self?.isLoading.value = false
                AlertController.showError(withMessage: errorMessage)
            }
    }

    private func fetchSlots(date: Date) {
        guard let orderNumber = order.number, let token = order.token else {
            // If order details are missing, create a new order first
            createOrderAndFetchSlots(date: date)
            return
        }

        if let slots = dateSlotCache[date] {
            // Use cached slots if available
            saveSlotViewModels(slots: slots)
        } else if isFetching[date, default: false] {
            // Already fetching this date, just ensure loading state is set
            isLoading.value = true
        } else {
            // Start fetching slots for this date
            isLoading.value = true
            isFetching[date] = true
            isSlotLoaded = false

            API.sharedInstance().getAvailableSlots(orderNumber: orderNumber, orderToken: token, stockLocationID: stockLocationId.intValue, location: location, date: date) { [weak self] (slots, operationMessage, errorMessage) in
                guard let self = self else { return }

                self.isSlotLoaded = true
                self.isFetching[date] = false
                self.operationMessage.value = operationMessage
                let isSelectedDate = date == self.selectedDateViewModel.value?.date

                if let errorMessage = errorMessage {
                    AlertController.showError(withMessage: errorMessage)

                    // Always stop loading on error, regardless of selected date
                    if isSelectedDate {
                        self.isLoading.value = false
                    }
                    return
                }

                // Cache the slots for future use
                self.dateSlotCache[date] = slots

                // Only update UI if this is still the selected date
                if isSelectedDate {
                    self.saveSlotViewModels(slots: slots)
                }
            }
        }
    }

    private func createOrderAndFetchSlots(date: Date) {
        // Create a new order for this stock location
        API.sharedInstance().createOrder { [weak self] (operation, newOrder) in
            guard let self = self, let newOrder = newOrder else {
                self?.isLoading.value = false
                return
            }

            // Update the app's order reference
            App.sharedInstance().order = newOrder
            self.order = newOrder

            // Now fetch slots with the new order
            self.fetchSlots(date: date)

        } failure: { [weak self] (operation, error) in
            self?.isLoading.value = false
            AlertController.showError(withMessage: error?.localizedDescription ?? "Failed to create order")
        }
    }
    
    func updateDeliverySlot(withOrderNumber orderNumber: String?, shipmentID: NSNumber?, slot: Slot?) {
        updateDeliverySlotOrderStartedBlock?()
        
        API.sharedInstance().updateDeliverySlot(withOrderNumber: orderNumber, shipmentID: shipmentID, slotID: slot?.slotID, success: { [weak self] (_, _) in
            self?.updateDeliverySlotOrderSuccessBlock?(slot, nil)
        }) { [weak self] (operation, error) in
            self?.didUpdateSlotFailed(message: operation?.errorMessage())
        }
    }
    
    func updateShipment(withSlotRow row: Int,
                        onDemandAvailable: Bool,
                        onDemandExist: Bool) {
        guard let slot = slotViewModels.value[safe: row]?.slot else { return }
        updateShipment(with: order, slot: slot, smallBasketExperimentType: smallBasketExperiment, onDemandAvailable: onDemandAvailable, onDemandExist: onDemandExist)
    }

    func updateShipment(with order: Order?,
                        slot: Slot?,
                        smallBasketExperimentType: SmallBasketExperimentType?,
                        onDemandAvailable: Bool = false,
                        onDemandExist: Bool = false) {
        guard let shipmentID = order?.selectedShipment()?.shipmentID, let slot = slot, let order = order else {
            return
        }
        
        updateDeliverySlotOrderStartedBlock?()
        self._isUpdateShipmentLoading.property.value = true
        API.sharedInstance().setOrderShipment(
            order: order,
            slotID: slot.slotID?.intValue ?? 0,
            shipmentID: shipmentID.intValue,
            isOnDemand: false,
            useOrderFee: smallBasketExperimentType == .withOrderFee,
            skipRefund: isFromMergedCheckoutScreen,
            skipUnassignSlot: isFromMergedCheckoutScreen
        ) { [weak self] order, error in
            guard let self = self else { return }
            self._isUpdateShipmentLoading.property.value = false
            self.trackDeliverySlotSelected(slot: slot, onDemandAvailable: onDemandAvailable, onDemandExist: onDemandExist)

            if let error = error {
                self._updateShipmentErrorMessage.property.value = error.localizedDescription
                self.didUpdateSlotFailed(message: error.localizedDescription)
                return
            }
            self.updateDeliverySlotOrderSuccessBlock?(slot, nil)
            self.updateShipmentSuccess(isExpress: false)
        }
    }
    
    func updateShipmentWithExpressSlot(_ slot: Slot?) {
        guard let shipmentID = order.selectedShipment()?.shipmentID else {
            return
        }
        Tracker.sharedInstance().trackOnDemandRequest(withOrderNumber: order.number,
                                                      supplierID: order.currentStockLocation()?.supplier?.supplierID ?? 0,
                                                      stockLocationID: stockLocationId)
        Tracker.sharedInstance().trackViewRequestDriverPopup(withOrderNumber: order.number,
                                                             supplierID: order.currentStockLocation()?.supplier?.supplierID ?? 0,
                                                             stockLocationID: stockLocationId)
        
        _isLookingForDriver.property.value = true
        onDemandCancelled = false
        API.sharedInstance().setOrderShipment(
            order: order,
            slotID: 0,
            shipmentID: shipmentID.intValue,
            isOnDemand: true,
            useOrderFee: self.smallBasketExperiment == .withOrderFee,
            skipRefund: isFromMergedCheckoutScreen,
            skipUnassignSlot: isFromMergedCheckoutScreen
        ) { [weak self] order, error in
            guard let self = self else { return }
            self._isLookingForDriver.property.value = false
            self.trackDeliverySlotSelected(slot: slot, onDemandAvailable: true, onDemandExist: true)
            if self.onDemandCancelled {
                return
            }
            if let _ = error {
                Tracker.sharedInstance().trackViewNoDriverPopup(withOrderNumber: self.order.number,
                                                                supplierID: self.order.currentStockLocation()?.supplier?.supplierID ?? 0,
                                                                stockLocationID: self.stockLocationId)
                self._isLookingForDriverFailed.property.value = true
                return
            }
            var onDemandSLAMinutesNumber: NSNumber?
            if let onDemandSLAMinutes = self.onDemandConfig?.onDemandSLAMinutes {
                onDemandSLAMinutesNumber = NSNumber(value: onDemandSLAMinutes)
            }
            self.updateDeliverySlotOrderSuccessBlock?(slot, onDemandSLAMinutesNumber)
            self.selectedSlot = slot
            if ApptimizeService.isShowExpressSelectionBottomSheet {
                self._isConfirmingExpressShipment.property.value = true
            } else {
                self.updateShipmentSuccess(isExpress: true)
            }
        }
    }
    
    func updateShipmentSuccess(isExpress: Bool) {
        self._isUpdateShipmentSuccess.property.value = true
        if isExpress {
            BannerManager.sharedInstance()?.showBanner(
                withText: NSLocalizedString("Express delivery confirmed!", comment: ""),
                userInfo: nil)
        }
    }
    
    func didUpdateSlotFailed(message: String?) {
        let proceedOrder = message == "order already processed by shopper and driver"
        updateDeliverySlotOrderFailedBlock?(proceedOrder, message)
    }

    func updateEarliestAvailableSlotDay(date: Date) {
        if let _ = earliestAvailableSlotDay {
            return
        }

        let calendar = Calendar.current
        let dateComponents = calendar.dateComponents([.day], from: calendar.startOfDay(for: Date()), to: calendar.startOfDay(for: date))

        if let dayDiff = dateComponents.day {
            earliestAvailableSlotDay = NSNumber(value: dayDiff)
        }
    }
    
    private var lowestDeliveryFee: NSDecimalNumber? {
        guard let selectedDate = selectedDateViewModel.value?.date,
              let slots = dateSlotCache[selectedDate] else { return nil }
        return slots.min(by: { ($0.cost ?? .zero).compare($1.cost ?? .zero) != .orderedDescending })?.cost
    }
    
    private func trackDeliverySlotSelected(slot: Slot?,
                                           onDemandAvailable: Bool,
                                           onDemandExist: Bool) {
        guard let slot else { return }

        let slots = slotViewModels.value.compactMap { $0.slot }
        let vipSlotCount = slots.map { $0.isVIP ? 1 : 0 }.reduce(0, { result, new in result + new })

        let properties = SelectDeliverySlotTrackingProperties(
            slot: slot,
            slots: slots,
            orderNumber: order.number,
            timeZone: order.timeZone ?? "",
            stockLocationID: stockLocationId.intValue,
            supplierID: supplierId?.intValue,
            isDeliveryDiscountApplied: basketInfo?.isDeliveryDiscountApplied?.boolValue,
            currency: order.currency,
            deliveryDiscount: basketInfo?.basketConfig?.deliveryFeeDiscountDecimal.doubleValue,
            deliveryDiscountUSD: basketInfo?.basketConfig?.deliveryFeeDiscountDecimalUSD.doubleValue,
            deliveryFee: order.serviceFee?.doubleValue,
            deliveryFeeUSD: order.serviceFeeUSD?.doubleValue,
            lowestDeliveryFee: lowestDeliveryFee?.doubleValue,
            onDemandAvailable: onDemandAvailable,
            onDemandExist: onDemandExist,
            isSaverDate: selectedDateViewModel.value?.showSaverRibbon ?? false,
            vipSlotCount: vipSlotCount,
            isExtraSlotsAvailable: isExtraSlotsAvailable,
            slotAvailabilityReason: slot.availabilityReason
        )

        Tracker.sharedInstance().trackSelectDeliverySlot(properties: properties)
    }
}

