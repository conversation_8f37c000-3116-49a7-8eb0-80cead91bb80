<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>ApptimizeAppKey</key>
	<string>$(APPTIMIZE_APP_KEY)</string>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>3.132</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>$(GOOGLE_LOGIN_URL_SCHEME)</string>
				<string>$(HF_URL_SCHEME)</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>fb$(FB_APP_ID)</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>io.branch.sdk</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>$(HF_URL_SCHEME)</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>2</string>
	<key>FacebookAppID</key>
	<string>$(FB_APP_ID)</string>
	<key>FacebookAutoLogAppEventsEnabled</key>
	<false/>
	<key>FacebookClientToken</key>
	<string>$(FB_APP_CLIENT_TOKEN)</string>
	<key>FacebookDisplayName</key>
	<string>$(FB_DISPLAY_NAME)</string>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>happyfresh</string>
		<string>boostapp</string>
		<string>fbauth2</string>
		<string>fb-messenger-api</string>
		<string>fbapi</string>
		<string>fbshareextension</string>
		<string>googlechromes</string>
		<string>comgooglemaps</string>
	</array>
	<key>LSMinimumSystemVersion</key>
	<string>12.0</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
	</dict>
	<key>NSBluetoothPeripheralUsageDescription</key>
	<string>HappyFresh wants to access your Bluetooth Peripheral.</string>
	<key>NSCameraUsageDescription</key>
	<string>HappyFresh wants to access your Camera.</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>HappyFresh needs your location to help you find nearby stores.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>HappyFresh needs your location to help you find nearby stores.</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>HappyFresh wants to access your microphone so you can attach voice notes.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>HappyFresh wants to access your Photo Library.</string>
	<key>NSUserTrackingUsageDescription</key>
	<string>Share your data for a more personalised experience</string>
	<key>UIAppFonts</key>
	<array>
		<string>montserrat-semibold.ttf</string>
		<string>montserrat-bold.ttf</string>
		<string>montserrat-regular.ttf</string>
		<string>montserrat-medium.ttf</string>
		<string>montserrat-black.ttf</string>
		<string>montserrat-extrabold.ttf</string>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen-May2024.storyboard</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
	</array>
	<key>UIStatusBarHidden</key>
	<true/>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIUserInterfaceStyle</key>
	<string>Light</string>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<true/>
	<key>appsflyer_onelink_domain</key>
	<string>$(APPSFLYER_ONELINK)</string>
	<key>branch_app_domain</key>
	<string>$(BRANCH_IO_DEEPLINK_URL)</string>
	<key>branch_key</key>
	<dict>
		<key>live</key>
		<string>${BRANCH_IO_KEY}</string>
		<key>test</key>
		<string>$(BRANCH_IO_TEST_KEY)</string>
	</dict>
	<key>branch_universal_link_domains</key>
	<string>$(BRANCH_IO_DEEPLINK_URL)</string>
	<key>hf_url_scheme</key>
	<string>$(HF_URL_SCHEME)</string>
	<key>marketing_click_recording_domain</key>
	<string>$(HF_MARKETING_CLICK_RECORDING_DOMAIN)</string>
</dict>
</plist>
