//
//  HFUserDefault.swift
//  HappyFresh
//
//  Created by <PERSON><PERSON><PERSON> on 14/03/22.
//  Copyright © 2022 HappyFresh Inc. All rights reserved.
//

import Foundation

extension HFUserDefault {

    private struct Keys {
        static let vipOnlySlotPopupShown = "vipOnlySlotPopupShown"
        static let isHFMExpressCoachmarkShown = "isHFMExpressCoachmarkShown"
    }

    //Value is [orderNumber:shouldShowClearancePopup]
    @objc var shouldShowClearancePopupDictionary: [String : Bool] {
        get {
            return (self.userDefaults?.dictionary(forKey: ShouldShowClearancePopupKey) as? [String : Bool]) ?? [:]
        }
        set {
            self.userDefaults.set(newValue, forKey: ShouldShowClearancePopupKey)
        }
    }
    
    @objc func setShouldShowClearancePopup(order: Order?, shouldShowClearancePopup: Bool) {
        guard let orderNumber = order?.number else { return }
        var currentValue = HFUserDefault.sharedInstance().shouldShowClearancePopupDictionary
        currentValue[orderNumber] = shouldShowClearancePopup
        HFUserDefault.sharedInstance().shouldShowClearancePopupDictionary = currentValue
    }
    
    func getShouldShowClearencePopup(order: Order) -> Bool {
        guard let orderNumber = order.number else { return true }
        return HFUserDefault.sharedInstance().shouldShowClearancePopupDictionary[orderNumber] ?? true
    }
    
    @objc var clearanceAddedProducts: [String : [NSNumber]] {
        get {
            return (self.userDefaults.dictionary(forKey: ClearanceAddedProductsKey) as? [String : [NSNumber]]) ?? [:]
        }
        set {
            self.userDefaults.set(newValue, forKey: ClearanceAddedProductsKey)
        }
    }
    
    //For the experiment (should remove after experiment finished)
    @objc var haveShownClearancePopup: Bool {
        get {
            return self.userDefaults.bool(forKey: ClearancePopupShownKey)
        }
        set {
            self.userDefaults.set(newValue, forKey: ClearancePopupShownKey)
        }
    }
    
    var isFlashSaleInfoFlyoutShown: Bool {
        get {
            return self.userDefaults.bool(forKey: FlashSaleInfoFlyoutShownKey)
        }
        set {
            self.userDefaults.set(newValue, forKey: FlashSaleInfoFlyoutShownKey)
        }
    }
    
    @objc var isSavedItemCoachmarkShown: Bool {
        get {
            return self.userDefaults.bool(forKey: SavedItemsCoachmarkShown)
        }
        set {
            self.userDefaults.set(newValue, forKey: SavedItemsCoachmarkShown)
        }
    }
    
    @objc var isFloatingCartCoachmarkShown: Bool {
        get {
            return self.userDefaults.bool(forKey: FloatingCartCoachmarkShownKey)
        }
        set {
            self.userDefaults.set(newValue, forKey: FloatingCartCoachmarkShownKey)
        }
    }
    
    @objc var isGlobalHomeFloatingCartCoachmarkShown: Bool {
        get {
            return self.userDefaults.bool(forKey: GlobalHomeFloatingCartCoachmarkShownKey)
        }
        set {
            self.userDefaults.set(newValue, forKey: GlobalHomeFloatingCartCoachmarkShownKey)
        }
    }
    
    var isNotificationCoachmarkShown: Bool {
        get {
            return self.userDefaults.bool(forKey: NotificationCoachmarkShownKey)
        }
        set {
            self.userDefaults.set(newValue, forKey: NotificationCoachmarkShownKey)
        }
    }
    
    var isRecipeCoachmarkShown: Bool {
        get {
            return self.userDefaults.bool(forKey: RecipeCoachmarkShownKey)
        }
        set {
            self.userDefaults.set(newValue, forKey: RecipeCoachmarkShownKey)
        }
    }
    
    var overrideStoreHomeCuratedSectionExperiment: Bool? {
        get {
            return self.userDefaults.object(forKey: OverrideStoreHomeCuratedSectionExperimentKey) as? Bool
        }
        set {
            self.userDefaults.set(newValue, forKey: OverrideStoreHomeCuratedSectionExperimentKey)
        }
    }
    
    @objc var visitorID: String? {
        set {
            UserDefaults.standard.set(newValue, forKey: VisitorIDKey)
        }
        
        get {
            UserDefaults.standard.string(forKey: VisitorIDKey)
        }
    }

    var isVIPOnlySlotPopupShown: Bool {
        get {
            userDefaults.bool(forKey: Keys.vipOnlySlotPopupShown)
        }
        set {
            userDefaults.set(newValue, forKey: Keys.vipOnlySlotPopupShown)
        }
    }
    
    var isHFMExpressCoachmarkShown: Bool {
        get {
            userDefaults.bool(forKey: Keys.isHFMExpressCoachmarkShown)
        }
        set {
            userDefaults.set(newValue, forKey: Keys.isHFMExpressCoachmarkShown)
        }
    }
}
