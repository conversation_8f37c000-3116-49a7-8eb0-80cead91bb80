//
//  InspirationsServices.h
//  HappyFresh
//
//  Created by <PERSON> on 11/7/17.
//  Copyright © 2017 HappyFresh Inc. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "AFHTTPRequestOperation+Additions.h"

@class StockLocation, ShoppingList, Recipe, Product;
NS_ASSUME_NONNULL_BEGIN


@interface InspirationsServices : NSObject

- (void)getRecipesListWithStockLocationID:(NSNumber * _Nullable)stockLocationID
                                success:(void (^) (NSArray <Recipe *> * _Nonnull  recipes))success
                                failure:(void (^) (AFHTTPRequestOperation *operation, NSError *error))failure;

- (void)getInspirationDetailWithInspirationID:(NSNumber *)inspirationID
                                stockLocation:(StockLocation * _Nullable)stockLocation
                                      success:(void (^) (AFHTTPRequestOperation *operation, Recipe *recipe, NSArray<Product *> *products))success
                                      failure:(void (^) (AFHTTPRequestOperation *operation, NSE<PERSON><PERSON> *error))failure;

- (void)getRecipePreviewWithSuccess:(void (^) (NSArray <Recipe *> * _Nonnull recipes))success
                            failure:(void (^) (AFHTTPRequestOperation *operation, NSError *error))failure;

@end

// Forward declarations for Swift classes
@class RecipeIngredientsResponse;
@class StockItem;
NS_ASSUME_NONNULL_END

