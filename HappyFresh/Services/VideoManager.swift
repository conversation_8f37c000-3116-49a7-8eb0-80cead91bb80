//
//  VideoManager.swift
//  HappyFresh
//
//  Created by Augment Agent on 2025-07-07.
//

import Foundation
import AVFoundation
import UIKit

enum VideoState {
    case loading
    case ready
    case playing
    case paused
    case failed
}

class VideoManager {
    static let shared = VideoManager()
    
    private var playerCache: NSCache<NSString, AVPlayer> = NSCache<NSString, AVPlayer>()
    private var playerKeys = Set<NSString>()
    private var currentlyPlayingPlayer: AVPlayer?
    private var preloadQueue: DispatchQueue
    
    private init() {
        // Configure caches
        playerCache.countLimit = 5 // Limit to 5 players in memory
        
        // Create background queue for preloading
        preloadQueue = DispatchQueue(label: "com.happyfresh.video.preload", qos: .utility)
        
        // Setup audio session for video playback
        setupAudioSession()
        
        // Listen for app lifecycle events
        setupNotifications()
    }
    
    // MARK: - Public Methods
    
    func getPlayer(for videoURL: URL) -> AVPlayer {
        let cacheKey = videoURL.absoluteString as NSString
        
        if let cachedPlayer = playerCache.object(forKey: cacheKey) {
            return cachedPlayer
        }
        
        // Always create a new AVPlayerItem to avoid reuse issues
        let playerItem = AVPlayerItem(url: videoURL)
        
        // Configure player item for better performance
        playerItem.preferredForwardBufferDuration = 2.0
        
        let player = AVPlayer(playerItem: playerItem)
        
        // Configure player for better performance
        player.automaticallyWaitsToMinimizeStalling = false
        player.volume = 0.8
        
        // Cache the player
        playerCache.setObject(player, forKey: cacheKey)
        playerKeys.insert(cacheKey)
        
        return player
    }
    
    func preloadVideo(url: URL) {
        preloadQueue.async { [weak self] in
            // Create player to trigger preloading
            _ = self?.getPlayer(for: url)
        }
    }
    
    func preloadVideos(urls: [URL]) {
        preloadQueue.async { [weak self] in
            for url in urls {
                // Create player to trigger preloading
                _ = self?.getPlayer(for: url)
            }
        }
    }
    
    func playVideo(player: AVPlayer) {
        // Pause any currently playing video
        pauseCurrentVideo()
        
        // Set as current player and play
        currentlyPlayingPlayer = player
        player.play()
    }
    
    func pauseCurrentVideo() {
        currentlyPlayingPlayer?.pause()
    }
    
    func pauseAllVideos() {
        // Pause all cached players
        for key in playerKeys {
            if let player = playerCache.object(forKey: key) {
                player.pause()
            }
        }
        currentlyPlayingPlayer = nil
    }
    
    func seekToBeginning(player: AVPlayer) {
        player.seek(to: .zero)
    }
    
    func clearCache() {
        pauseAllVideos()
        playerCache.removeAllObjects()
        playerKeys.removeAll()
    }
    
    func getVideoState(for player: AVPlayer) -> VideoState {
        guard let playerItem = player.currentItem else { return .loading }
        
        switch playerItem.status {
        case .readyToPlay:
            return player.rate > 0 ? .playing : .ready
        case .failed:
            return .failed
        case .unknown:
            return .loading
        @unknown default:
            return .loading
        }
    }
    
    // MARK: - Private Methods
    
    private func setupAudioSession() {
        do {
            try AVAudioSession.sharedInstance().setCategory(.playback, mode: .default, options: [.mixWithOthers])
            try AVAudioSession.sharedInstance().setActive(true)
        } catch {
            print("Failed to setup audio session: \(error)")
        }
    }
    
    private func setupNotifications() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appDidEnterBackground),
            name: UIApplication.didEnterBackgroundNotification,
            object: nil
        )
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appWillEnterForeground),
            name: UIApplication.willEnterForegroundNotification,
            object: nil
        )
    }
    
    @objc private func appDidEnterBackground() {
        pauseAllVideos()
    }
    
    @objc private func appWillEnterForeground() {
        // Videos will be resumed when cells become visible again
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
}
