//
//  InspirationsServices.m
//  HappyFresh
//
//  Created by <PERSON> on 11/7/17.
//  Copyright © 2017 HappyFresh Inc. All rights reserved.
//

#import "InspirationsServices.h"
#import "API.h"
#import "StockLocation+CoreDataProperties.h"
#import "Recipe+Logic.h"
#import "NSDictionary+Additions.h"
#import "Product+Logic.h"

@import MagicalRecord;

static NSString * const InspirationDetailURL = @"/api/v3/recipes/%@";
static NSString * const InspirationDetailURLWithStockLocation = @"/api/stock_locations/%@/recipes/%@";
static NSString * const RecipesListURL = @"/api/recipes/";
static NSString * const RecipesListURLWithStockLocation = @"/api/v3/stock_locations/%@/recipes/";
static NSString * const RecipePreviewURL = @"/api/v3/recipes";

@implementation InspirationsServices

- (void)getRecipesListWithStockLocationID:(NSNumber * _Nullable)stockLocationID
                                success:(void (^) (NSArray <Recipe *> *recipes))success
                                failure:(void (^) (AFHTTPRequestOperation *operation, NSError *error))failure {
    NSString *url = RecipesListURL;
    if (stockLocationID) {
        url = [NSString stringWithFormat:RecipesListURLWithStockLocation, stockLocationID];
    }
    [[API sharedInstance].manager GET:url parameters:nil success:^(AFHTTPRequestOperation *operation, id responseObject) {
            if (stockLocationID) {
                // Remove current recipes
                [Recipe MR_deleteAllMatchingPredicate:[NSPredicate predicateWithFormat:@"stockLocation.stockLocationID=%@", stockLocationID]];
            }
        NSArray *recipes = [Recipe arrayFromJSONArray:responseObject[@"recipes"] stockLocation:nil];
        [[NSManagedObjectContext MR_defaultContext] MR_saveToPersistentStoreWithCompletion:^(BOOL complete, NSError *error) {
            if (success) {
                success(recipes);
            }
        }];
    } failure:failure];
}

- (void)getInspirationDetailWithInspirationID:(NSNumber *)inspirationID
                                stockLocation:(StockLocation *)stockLocation
                                      success:(void (^)(AFHTTPRequestOperation *, Recipe *, NSArray<Product *> *))success
                                      failure:(void (^)(AFHTTPRequestOperation *, NSError *))failure {
    NSString *url = [NSString stringWithFormat:InspirationDetailURL, inspirationID];

    if (stockLocation != nil) {
        url = [NSString stringWithFormat:InspirationDetailURLWithStockLocation, stockLocation.stockLocationID, inspirationID];
    }

    [[API sharedInstance].manager GET:url parameters:nil success:^(AFHTTPRequestOperation *operation, id responseObject) {
        NSDictionary *nonNullResponseObject = [responseObject removeNSNullValues];
        NSArray<Product *> *product = [Product arrayFromJSONArray:nonNullResponseObject[@"products"] stockLocation:stockLocation];
        Recipe *recipe = [Recipe recipeFromDictionary:nonNullResponseObject stockLocation:stockLocation];

        [[NSManagedObjectContext MR_defaultContext] MR_saveToPersistentStoreWithCompletion:^(BOOL complete, NSError *error) {
            if (success) {
                success(operation, recipe, product);
            }
        }];
    } failure:failure];
}

- (void)getRecipePreviewWithSuccess:(void (^)(NSArray<Recipe *> *))success failure:(void (^)(AFHTTPRequestOperation *, NSError *))failure {
    [[API sharedInstance].manager GET:RecipePreviewURL parameters:nil success:^(AFHTTPRequestOperation *operation, id responseObject) {
        NSArray *recipes = [Recipe arrayFromJSONArray:responseObject[@"recipes"] stockLocation:nil];
        [[NSManagedObjectContext MR_defaultContext] MR_saveToPersistentStoreWithCompletion:^(BOOL complete, NSError *error) {
            if (success) {
                success(recipes);
            }
        }];
    } failure:failure];
}

@end
