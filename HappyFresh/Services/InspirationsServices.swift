//
//  InspirationsServices.swift
//  HappyFresh
//
//  Created by <PERSON> on 7/25/25.
//  Copyright © 2025 HappyFresh Inc. All rights reserved.
//

import Foundation

// MARK: - Ingredient Response Models
@objc class RecipeIngredient: NSObject {
    @objc let ingredientID: NSNumber
    @objc let name: String
    @objc let position: NSNumber
    @objc let products: [Product]

    init(ingredientID: NSNumber, name: String, position: NSNumber, products: [Product]) {
        self.ingredientID = ingredientID
        self.name = name
        self.position = position
        self.products = products
        super.init()
    }

    @objc static func arrayFromJSONArray(_ jsonArray: [[String: Any]], stockLocation: StockLocation?) -> [RecipeIngredient] {
        var ingredients: [RecipeIngredient] = []

        for json in jsonArray {
            guard let id = json["id"] as? NSNumber,
                  let name = json["name"] as? String,
                  let position = json["position"] as? NSNumber else {
                continue
            }

            let productsArray = json["products"] as? [[String: Any]] ?? []
            if let products = Product.array(fromJSONArray: productsArray, stockLocation: stockLocation) as? [Product] {
                let ingredient = RecipeIngredient(ingredientID: id, name: name, position: position, products: products)
                ingredients.append(ingredient)
            }
        }

        return ingredients
    }
}

@objc class RecipeIngredientsResponse: NSObject {
    @objc let count: NSNumber
    @objc let totalCount: NSNumber
    @objc let currentPage: NSNumber
    @objc let perPage: NSNumber
    @objc let pages: NSNumber
    @objc let ingredients: [RecipeIngredient]

    init(count: NSNumber, totalCount: NSNumber, currentPage: NSNumber, perPage: NSNumber, pages: NSNumber, ingredients: [RecipeIngredient]) {
        self.count = count
        self.totalCount = totalCount
        self.currentPage = currentPage
        self.perPage = perPage
        self.pages = pages
        self.ingredients = ingredients
        super.init()
    }

    @objc static func fromJSONDictionary(_ json: [String: Any], stockLocation: StockLocation?) -> RecipeIngredientsResponse? {
        guard let count = json["count"] as? NSNumber,
              let totalCount = json["total_count"] as? NSNumber,
              let currentPage = json["current_page"] as? NSNumber,
              let perPage = json["per_page"] as? NSNumber,
              let pages = json["pages"] as? NSNumber,
              let ingredientsArray = json["ingredients"] as? [[String: Any]] else {
            return nil
        }

        let ingredients = RecipeIngredient.arrayFromJSONArray(ingredientsArray, stockLocation: stockLocation)

        return RecipeIngredientsResponse(
            count: count,
            totalCount: totalCount,
            currentPage: currentPage,
            perPage: perPage,
            pages: pages,
            ingredients: ingredients
        )
    }
}

extension InspirationsServices {

    @objc func getRecipeIngredients(stockLocation: StockLocation,
                                    recipeID: NSNumber,
                                    success: @escaping (RecipeIngredientsResponse) -> Void,
                                    failure: @escaping (AFHTTPRequestOperation?, Error) -> Void) {

        guard let stockLocationID = stockLocation.stockLocationID else { return }
        let url = "api/v3/stock_locations/\(stockLocationID)/recipes/\(recipeID)/ingredients"

        API.sharedInstance().manager.get(url, parameters: nil, success: { operation, responseObject in
            guard let responseDict = responseObject as? [String: Any] else {
                let error = NSError(domain: "InspirationsServices", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid response format"])
                failure(operation, error)
                return
            }

            // Parse the response
            guard let ingredientsResponse = RecipeIngredientsResponse.fromJSONDictionary(responseDict, stockLocation: stockLocation) else {
                let error = NSError(domain: "InspirationsServices", code: -2, userInfo: [NSLocalizedDescriptionKey: "Failed to parse ingredients response"])
                failure(operation, error)
                return
            }

            // Convert products to stock items (similar to getSimilarProductsWithID)
//            let stockLocation = App.sharedInstance().stockLocation
//            var allProducts: [Product] = []
//
//            // Collect all products from all ingredients
//            for ingredient in ingredientsResponse.ingredients {
//                allProducts.append(contentsOf: ingredient.products)
//            }
//
//            // Convert products to stock items using the same pattern as getSimilarProductsWithID
//            let predicate = NSPredicate(format: "(variant.product IN %@) AND stockLocation == %@", allProducts, stockLocation as Any)
//            let stockItems = SortHelper.sortStockItems(with: predicate, products: allProducts)

            // Save to Core Data
            NSManagedObjectContext.mr_default().mr_saveToPersistentStore { complete, error in
                if complete {
                    success(ingredientsResponse)
                } else {
                    let saveError = error ?? NSError(domain: "InspirationsServices", code: -3, userInfo: [NSLocalizedDescriptionKey: "Failed to save to Core Data"])
                    failure(operation, saveError)
                }
            }

        }, failure: { operation, error in
            failure(operation, error)
        })
    }
}
