
#import "HFUserDefault.h"
#import "HFPlaceToken.h"

NSString * const OnboardingShownKey = @"onboarding_language_selection_shown_key";
NSString * const GooglePlaceTokenKey = @"GooglePlaceTokenKey";
NSString * const DeliveryAddressIDKey = @"DeliveryAddressIDKey";
NSString * const VoucherOnboardingKey = @"VoucherOnboardingKey";
NSString * const ReferralCustomerIDKey = @"ReferralCustomerIDKey";
NSString * const ReferralChannelKey = @"ReferralChannelKey";
NSString * const SocialSharingChannelKey = @"SocialSharingChannelKey";
NSString * const SocialSharingIDKey = @"SocialSharingIDKey";
NSString * const HfsPostPurchaseSurveyLastViewedDateKey = @"HfsPostPurchaseSurveyLastViewedDateKey";
NSString * const ChosenHfsPlusKey = @"ChosenHfsPlusKey";
NSString * const HasChosenHfsTypeKey = @"HasChosenHfsTypeKey";
NSString * const HfsChoiceSupplierIDKey = @"HfsChoiceSupplierIDKey";
NSString * const HfsPlusChoiceSupplierIDKey = @"HfsPlusChoiceSupplierIDKey";
NSString * const PromotionTncEn = @"PromotionTncEn";
NSString * const PromotionTncLocale = @"PromotionTncLocale";
NSString * const AddressCountry = @"AddressCountry";
NSString * const HasOrderReceivedKey = @"HasOrderReceivedKey";
NSString * const ShouldShowClearancePopupKey = @"ShouldShowClearancePopupKey";
NSString * const ClearanceAddedProductsKey = @"ClearanceAddedProductsKey";
NSString * const ClearancePopupShownKey = @"ClearancePopupShownKey";
NSString * const FlashSaleInfoFlyoutShownKey = @"FlashSaleInfoFlyoutShownKey";
NSString * const SavedItemsCoachmarkShown = @"SavedItemsCoachmarkShown";
NSString * const OverrideStoreHomeCuratedSectionExperimentKey = @"OverrideStoreHomeCuratedSectionExperimentKey";
NSString * const VisitorIDKey = @"VisitorIDKey";

/* Coachmark */
NSString * const GlobalCartButtonCoachmarkShownKey = @"GlobalCartButtonCoachmarkShownKey";
NSString * const GlobalCartPendingOrderCoachmarkShownKey = @"GlobalPendingOrderCoachmarkShownKey";
NSString * const GlobalMissionCoachmarkShownKey = @"GlobalMissionCoachmarkShownKey";
NSString * const PaymentPromotionCoachmarkShownKey = @"PaymentPromotionCoachmarkShownKey";
NSString * const FloatingCartCoachmarkShownKey = @"FloatingCartCoachmarkShownKey";
NSString * const ReorderAndFavoriteCoachmarkShownKey = @"ReorderAndFavoriteCoachmarkShownKey";
NSString * const GlobalHomeFloatingCartCoachmarkShownKey = @"GlobalHomeFloatingCartCoachmarkShownKey";
NSString * const NotificationCoachmarkShownKey = @"NotificationCoachmarkShownKey";
NSString * const RecipeCoachmarkShownKey = @"RecipeCoachmarkShownKey";

@interface HFUserDefault()

@end

@implementation HFUserDefault

static NSString *const DismissedRatingKey = @"DismissedRatingOrderIDKey";

+ (instancetype)sharedInstance {
    static id sharedInstance = nil;

    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        sharedInstance = [HFUserDefault new];
    });

    return sharedInstance;
}

- (instancetype)init
{
    self = [super init];
    if (self) {
        _userDefaults = [NSUserDefaults standardUserDefaults];
    }
    return self;
}

- (NSMutableSet *)getDismissedRatingOrderNumber {
    NSUserDefaults *prefs = [NSUserDefaults standardUserDefaults];
    NSArray *arrayIDs = [prefs arrayForKey:DismissedRatingKey];
    if (!arrayIDs) {
        arrayIDs = [[NSMutableArray alloc] init];
    }
    NSMutableSet *setIDs = [NSMutableSet setWithArray: arrayIDs];
    return setIDs;
}

- (void)setDismissedRatingOrderNumber:(NSMutableSet *)dismissedRatingOrderNumbers {
    NSUserDefaults *prefs = [NSUserDefaults standardUserDefaults];
    NSArray *arrayIDs = [NSArray arrayWithArray:[dismissedRatingOrderNumbers allObjects]];
    [prefs setObject:arrayIDs forKey:DismissedRatingKey];
}

- (NSNumber *)deliveryAddressID {
    return [self.userDefaults objectForKey:DeliveryAddressIDKey];
}

- (void)setDeliveryAddressID:(NSNumber *)deliveryAddressID {
    [self.userDefaults setObject:deliveryAddressID forKey:@"DeliveryAddressIDKey"];
}

- (BOOL)isHideVoucherOnboarding {
    return [self.userDefaults boolForKey:VoucherOnboardingKey];
}

- (void)setIsHideVoucherOnboarding:(BOOL)isHideVoucherOnboarding {
    [self.userDefaults setBool:isHideVoucherOnboarding forKey:VoucherOnboardingKey];
}

- (BOOL)isOnBoardingShown {
    return [self.userDefaults boolForKey:OnboardingShownKey];
}

- (void)setIsOnBoardingShown:(BOOL)isOnBoardingShown {
    [self.userDefaults setBool:isOnBoardingShown forKey:OnboardingShownKey];
}

- (NSString *)referralCustomerID {
    return [self.userDefaults objectForKey:ReferralCustomerIDKey];
}

- (void)setReferralCustomerID:(NSString *)referralCustomerID {
    [self.userDefaults setObject:referralCustomerID forKey:ReferralCustomerIDKey];
}

- (NSString *)referralChannel {
    return [self.userDefaults objectForKey:ReferralChannelKey];
}

- (void)setReferralChannel:(NSString *)referralChannel {
    [self.userDefaults setObject:referralChannel forKey:ReferralChannelKey];
}

- (NSString *)socialSharingID {
    return [self.userDefaults objectForKey:SocialSharingIDKey];
}

- (void)setSocialSharingID:(NSString *)socialSharingID {
    [self.userDefaults setObject:socialSharingID forKey:SocialSharingIDKey];
}

- (NSString *)socialSharingChannel {
    return [self.userDefaults objectForKey:SocialSharingChannelKey];
}

- (void)setSocialSharingChannel:(NSString *)socialSharingChannel {
    [self.userDefaults setObject:socialSharingChannel forKey:SocialSharingChannelKey];
}

- (void)setPromotionTncLocale:(NSString *)promotionTncLocale {
    [self.userDefaults setObject:promotionTncLocale forKey:PromotionTncLocale];
}

- (NSString *)promotionTncLocale {
    return [self.userDefaults objectForKey:PromotionTncLocale];
}

- (void)setPromotionTncEn:(NSString *)promotionTncEn {
    [self.userDefaults setObject:promotionTncEn forKey:PromotionTncEn];
}

- (NSString *)promotionTncEn {
    return [self.userDefaults objectForKey:PromotionTncEn];
}

- (void)setAddressCountry:(NSString *)addressCountry {
    [self.userDefaults setObject:addressCountry forKey:AddressCountry];
}

- (NSString *)addressCountry {
    return [self.userDefaults objectForKey:AddressCountry];
}

- (BOOL)isHasOrderReceived {
    return [self.userDefaults boolForKey:HasOrderReceivedKey];
}

- (void)setIsHasOrderReceived:(BOOL)isHasOrderReceived {
    [self.userDefaults setBool:isHasOrderReceived forKey:HasOrderReceivedKey];
}


- (BOOL)isGlobalCartButtonCoachmarkShown {
    return [self.userDefaults boolForKey:GlobalCartButtonCoachmarkShownKey];
}

- (void)setIsGlobalCartButtonCoachmarkShown:(BOOL)isGlobalCartButtonCoachmarkShown {
    [self.userDefaults setBool:isGlobalCartButtonCoachmarkShown forKey:GlobalCartButtonCoachmarkShownKey];
}

- (BOOL)isGlobalCartPendingOrderCoachmarkShown {
    return [self.userDefaults objectForKey:GlobalCartPendingOrderCoachmarkShownKey];
}

- (void)setIsGlobalCartPendingOrderCoachmarkShown:(BOOL)isGlobalCartPendingOrderCoachmarkShown {
    [self.userDefaults setBool:isGlobalCartPendingOrderCoachmarkShown forKey:GlobalCartPendingOrderCoachmarkShownKey];
}

- (BOOL)isGlobalMissionCoachmarkShown {
    return [self.userDefaults boolForKey:GlobalMissionCoachmarkShownKey];
}

- (void)setIsGlobalMissionCoachmarkShown:(BOOL)isGlobalMissionCoachmarkShown {
    [self.userDefaults setBool:isGlobalMissionCoachmarkShown forKey:GlobalMissionCoachmarkShownKey];
}

- (double)hfsPostPurchaseSurveyLastViewedDate {
    return [self.userDefaults doubleForKey:HfsPostPurchaseSurveyLastViewedDateKey];
}

- (void)setHfsPostPurchaseSurveyLastViewedDate:(double)hfsPostPurchaseSurveyLastViewedDate {
    [self.userDefaults setDouble:hfsPostPurchaseSurveyLastViewedDate forKey:HfsPostPurchaseSurveyLastViewedDateKey];
}

- (BOOL)chosenHfsPlus {
    return [self.userDefaults boolForKey:ChosenHfsPlusKey];
}

- (void)setChosenHfsPlus:(BOOL)chosenHfsPlus {
    [self.userDefaults setBool:chosenHfsPlus forKey:ChosenHfsPlusKey];
}

- (BOOL)haveChosenHfsType {
    return [self.userDefaults boolForKey:HasChosenHfsTypeKey];
}

- (void)setHaveChosenHfsType:(BOOL)haveChosenHfsType {
    [self.userDefaults setBool:haveChosenHfsType forKey:HasChosenHfsTypeKey];
}

- (NSNumber *)hfsPlusChoiceSupplierID {
    return [self.userDefaults objectForKey:HfsPlusChoiceSupplierIDKey];
}

- (void)setHfsPlusChoiceSupplierID:(NSNumber *)hfsChoiceSupplierID {
    [self.userDefaults setObject:hfsChoiceSupplierID forKey:HfsPlusChoiceSupplierIDKey];
}

- (BOOL)isPaymentPromotionCoachmarkShown {
    return [self.userDefaults boolForKey:PaymentPromotionCoachmarkShownKey];
}

- (void)setIsPaymentPromotionCoachmarkShown:(BOOL)isPaymentPromotionCoachmarkShown {
    [self.userDefaults setBool:isPaymentPromotionCoachmarkShown forKey:PaymentPromotionCoachmarkShownKey];
}

#pragma mark - Google Place Token

- (HFPlaceToken *)googlePlaceToken {
    NSData *data = [self.userDefaults objectForKey:GooglePlaceTokenKey];
    HFPlaceToken *placeToken = [NSKeyedUnarchiver unarchivedObjectOfClass:[HFPlaceToken class]
                                                                 fromData:data
                                                                    error:nil];
    if (placeToken == nil || [placeToken isTokenExpired]) {
        placeToken = [HFPlaceToken createInstance];
        [self setGooglePlaceToken:placeToken];
    }
    return placeToken;
}

- (void)setGooglePlaceToken:(HFPlaceToken *)placeToken {
    NSData *data = [NSKeyedArchiver archivedDataWithRootObject:placeToken
                                         requiringSecureCoding:NO
                                                         error:nil];
    [self.userDefaults setObject:data forKey:GooglePlaceTokenKey];
}

@end
