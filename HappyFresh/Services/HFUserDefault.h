
#import <Foundation/Foundation.h>

@class HFPlaceToken;

extern NSString * const ShouldShowClearancePopupKey;
extern NSString * const ClearanceAddedProductsKey;
extern NSString * const ClearancePopupShownKey;
extern NSString * const FlashSaleInfoFlyoutShownKey;
extern NSString * const SavedItemsCoachmarkShown;
extern NSString * const FloatingCartCoachmarkShownKey;
extern NSString * const ReorderAndFavoriteCoachmarkShownKey;
extern NSString * const GlobalHomeFloatingCartCoachmarkShownKey;
extern NSString * const NotificationCoachmarkShownKey;
extern NSString * const RecipeCoachmarkShownKey;
extern NSString * const OverrideStoreHomeCuratedSectionExperimentKey;
extern NSString * const VisitorIDKey;

@interface HFUserDefault : NSObject

+ (nonnull instancetype)sharedInstance;

@property (nonatomic) NSUserDefaults *userDefaults;
@property (assign, nonatomic) BOOL isOnBoardingShown;
@property (assign) HFPlaceToken *googlePlaceToken;
@property (nonatomic) NSNumber *deliveryAddressID;
@property (assign, nonatomic) BOOL isHideVoucherOnboarding;
@property (nonatomic) NSString *referralCustomerID;
@property (nonatomic) NSString *referralChannel;
@property (nonatomic) NSString *socialSharingID;
@property (nonatomic) NSString *socialSharingChannel;
@property (assign, nonatomic) double hfsPostPurchaseSurveyLastViewedDate;
@property (assign, nonatomic) BOOL chosenHfsPlus;
@property (assign, nonatomic) BOOL haveChosenHfsType;
@property (nonatomic, nullable) NSNumber *hfsPlusChoiceSupplierID;
@property (assign, nonatomic) NSString *promotionTncEn;
@property (assign, nonatomic) NSString *promotionTncLocale;
@property (assign, nonatomic) NSString *addressCountry;
@property (assign, nonatomic) BOOL isHasOrderReceived;
/* Coachmark */
@property (assign, nonatomic) BOOL isGlobalCartButtonCoachmarkShown;
@property (assign, nonatomic) BOOL isGlobalCartPendingOrderCoachmarkShown;
@property (assign, nonatomic) BOOL isGlobalMissionCoachmarkShown;
@property (assign, nonatomic) BOOL isPaymentPromotionCoachmarkShown;

-(NSMutableSet *)getDismissedRatingOrderNumber;
-(void)setDismissedRatingOrderNumber: (NSMutableSet *)dismissedRatingOrderNumbers;

@end
