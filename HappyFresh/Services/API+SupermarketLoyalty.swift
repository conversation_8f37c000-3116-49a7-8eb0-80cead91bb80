//
//  API+SupermarketLoyalty.swift
//  HappyFresh
//
//  Created by <PERSON><PERSON>@HappyFresh on 15/02/23.
//  Copyright © 2023 HappyFresh Inc. All rights reserved.
//

import Foundation

extension API: SupermarketLoyaltyMembershipService {
    func fetchWidgetConfiguration() async throws -> SupermarketLoyaltyMembershipWidgetConfiguration {
        do {
            let data = try await get(endpoint: "api/widgets/my_super_indo_membership")
            let decoder = JSONDecoder()
            if UserService.shared.isMsiIdLinked {
                let value = try decoder.decode(SupermarketLoyaltyMembershipWidgetConfiguration.Linked.self, from: data)
                return .linked(value)
            } else {
                let value = try decoder.decode(SupermarketLoyaltyMembershipWidgetConfiguration.Form.self, from: data)
                return .form(value)
            }
        } catch {
            throw HFAlert(message: NSError.genericLocalizedMessage)
        }
    }
    
    func link(parameters: [String:String]) async throws -> User {
        let data = try await post(endpoint: "api/users/link_my_super_indo", parameters: parameters)
        let dict = try JSONSerialization.jsonObject(with: data) as! [AnyHashable: Any]
        return User.init(from: dict)
    }
    
    func updateGlobalUser(user: User) {
        UserService.shared.user = user
    }
}

extension SupermarketLoyaltyMembershipWidgetConfiguration.Form: Decodable {
    enum CodingKeys: String, CodingKey {
        case title = "title"
        case imageURL = "image_url"
        case heading = "heading"
        case body = "body"
        case phoneFieldName = "phone_field_name"
        case phoneFieldDescription = "phone_field_description"
        case phoneFieldPlaceholder = "phone_field_placeholder"
        case phoneFieldPopupImageURL = "phone_field_popup_image_url"
        case phoneFieldPopupTitle = "phone_field_popup_title"
        case phoneFieldPopupDescription = "phone_field_popup_description"
        case phoneFieldPopupButtonText = "phone_field_popup_button_text"
        case buttonText = "button_text"
        case buttonDescription = "button_description"
        case successPopupImageURL = "success_popup_image_url"
        case successPopupTitle = "success_popup_title"
        case successPopupDescription = "success_popup_description"
        case successPopupButtonText = "success_popup_button_text"
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        self.title = try container.decodeIfPresent(String.self, forKey: .title)
        self.imageURL = try container.decodeIfPresent(URL.self, forKey: .imageURL)
        self.heading = try container.decodeIfPresent(String.self, forKey: .heading)
        self.body = try container.decodeIfPresent(String.self, forKey: .body)
        self.phoneFieldName = try container.decodeIfPresent(String.self, forKey: .phoneFieldName)
        self.phoneFieldDescription = try container.decodeIfPresent(String.self, forKey: .phoneFieldDescription)
        self.phoneFieldPlaceholder = try container.decodeIfPresent(String.self, forKey: .phoneFieldPlaceholder)
        self.phoneFieldPopupImageURL = try container.decodeIfPresent(URL.self, forKey: .phoneFieldPopupImageURL)
        self.phoneFieldPopupTitle = try container.decodeIfPresent(String.self, forKey: .phoneFieldPopupTitle)
        self.phoneFieldPopupDescription = try container.decodeIfPresent(String.self, forKey: .phoneFieldPopupDescription)
        self.phoneFieldPopupButtonText = try container.decodeIfPresent(String.self, forKey: .phoneFieldPopupButtonText)
        self.buttonText = try container.decodeIfPresent(String.self, forKey: .buttonText)
        self.buttonDescription = try container.decodeIfPresent(String.self, forKey: .buttonDescription)
        self.successPopupImageURL = try container.decodeIfPresent(URL.self, forKey: .successPopupImageURL)
        self.successPopupTitle = try container.decodeIfPresent(String.self, forKey: .successPopupTitle)
        self.successPopupDescription = try container.decodeIfPresent(String.self, forKey: .successPopupDescription)
        self.successPopupButtonText = try container.decodeIfPresent(String.self, forKey: .successPopupButtonText)
    }
}

extension SupermarketLoyaltyMembershipWidgetConfiguration.Linked: Decodable {
    enum CodingKeys: String, CodingKey {
        case title
        case imageURL = "image_url"
        case msiIDFieldName = "msi_id_field_name"
        case msiIDFieldValue = "msi_id_field_value"
        case digitalStampFieldName = "digital_stamp_field_name"
        case digitalStampFieldValue = "digital_stamp_field_value"
        case messageFieldValue = "message_field_value"
        case messageFieldState = "message_field_state"
        case tncFieldValue = "tnc_field_value"
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        self.title = try container.decodeIfPresent(String.self, forKey: .title)
        self.imageURL = try container.decodeIfPresent(URL.self, forKey: .imageURL)
        self.msiIDFieldName = try container.decodeIfPresent(String.self, forKey: .msiIDFieldName)
        self.msiIDFieldValue = try container.decodeIfPresent(String.self, forKey: .msiIDFieldValue)
        self.digitalStampFieldName = try container.decodeIfPresent(String.self, forKey: .digitalStampFieldName)
        self.digitalStampFieldValue = try container.decodeIfPresent(String.self, forKey: .digitalStampFieldValue)
        self.messageFieldValue = try container.decodeIfPresent(String.self, forKey: .messageFieldValue)
        self.messageFieldState = try container.decodeIfPresent(String.self, forKey: .messageFieldState)
        self.tncFieldValue = try container.decodeIfPresent(String.self, forKey: .tncFieldValue)
    }
}
