# Uncomment this line to define a global platform for your project
platform :ios, '14.0'

inhibit_all_warnings!
use_frameworks!

def service_pods
    pod 'AppAuth', '~> 1.7.3'
    pod 'AppsFlyerFramework', '~> 6.15.2'
    pod 'FBSDKCoreKit', '~> 18.0.0'
    pod 'FBSDKLoginKit', '~> 18.0.0'
    pod 'FirebaseAnalytics', '~> 10.23.0'
    pod 'FirebaseCrashlytics', '~> 10.23.0'
    pod 'FirebasePerformance', '~> 10.23.0'
    pod 'GoogleMaps', '~> 8.3.1'
    pod 'GoogleSignIn', '~> 7.0'
    pod 'MoEngage-iOS-SDK', '~> 9.17.5'
    pod 'MoEngageCards', '~> 4.16.1'
    pod 'MoEngageInApp', '~> 6.00.3'
    pod 'Mixpanel-swift', '~> 4.0.4'
    pod 'Sentry', '~> 8.36.0'
    pod 'UXCam', '~> 3.4.5'
    pod 'FingerprintPro', '~> 2.2.0'
    pod 'FingerprintJS', '~> 1.5.0'
    pod 'Anura', :git => '*************************:hf/tpd/cx/ios/anura-ios.git'
    pod 'AnuraGrowthBookIntegration', :git => '*************************:hf/tpd/cx/ios/anura-ios.git'
end

def important_pods
    pod 'AFNetworking', '~> 2.7.0', :subspecs => ['Reachability', 'Serialization', 'Security', 'NSURLSession', 'NSURLConnection']
    pod 'IQKeyboardManager', '~> 6.4.0'
    pod 'MagicalRecord', '~> 2.2'
    pod 'ReactiveCocoa', '~> 9.0'
    pod 'SnapKit', '~> 5.7.1'
end

def ui_pods
    pod 'KLCPopup', :git => 'https://github.com/happyfresh/KLCPopup'
    pod 'iCarousel', :git => 'https://github.com/happyfresh/iCarousel'
    pod 'PanModal', :git => 'https://github.com/happyfresh/PanModal'
    pod 'PullUpController', :git => 'https://github.com/happyfresh/PullUpController'
    pod 'PulsingHalo'
    pod 'SVProgressHUD'
    pod 'Shimmer'
    pod 'UICollectionViewLeftAlignedLayout'
    pod 'UITextView+Placeholder', '~> 1.3.3'
    pod 'Instructions', :git => 'https://github.com/ephread/Instructions', :tag => '2.3.0'
    pod 'lottie-ios'
    pod 'PINRemoteImage'
    pod 'IGListKit', '~> 5.0.0'
    pod 'youtube-ios-player-helper', '~> 1.0.4'
end

def util_pods
    pod 'DateTools'
    pod 'DateToolsSwift'
    pod 'Mantle'
    pod 'Masonry', '~> 1.1.0'
    pod 'PhoneCountryCodePicker'
    pod 'ReactiveObjC', '~> 3.1.0'
    pod 'libPhoneNumber-iOS', '~> 0.8'
    pod 'Valet', '~> 4.1.1'
    pod 'Siren', '~> 5.7.1'
end

target 'HappyFresh' do
    project 'HappyFresh',
        'Staging' => :release,
        'Staging Debug' => :debug,
        'Production' => :release,
        'Production Debug' => :debug
    service_pods
    important_pods
    ui_pods
    util_pods

    target 'HappyFreshTests' do
        inherit! :search_paths
        pod 'Specta', '~> 1.0.5'
        pod 'OCMock', '~> 3.4.1'
        pod 'Expecta', '~>1.0.5'
        pod 'Quick', '~> 5.0.1'
        pod 'Nimble', '~> 10.0.0'
        pod 'Fakery', '~> 5.1.0'
    end
end

post_install do |installer|
    installer.pods_project.build_configurations.each do |config|
        config.build_settings["EXCLUDED_ARCHS[sdk=iphonesimulator*]"] = "arm64"
    end

    installer.generated_projects.each do |project|
        project.targets.each do |target|
            target.build_configurations.each do |config|
                config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '14.0'
            end
        end
    end
end
