//
//  SupermarketLoyaltyMembershipViewModelTests.swift
//  HappyFreshTests
//
//  Created by <PERSON><PERSON>@HappyFresh on 14/02/23.
//  Copyright © 2023 HappyFresh Inc. All rights reserved.
//

import XCTest
@testable import HappyFresh

final class SupermarketLoyaltyMembershipViewModelTests: XCTestCase {
    func test_load_form_create_form_widget() async {
        let sut = makeSUT(stub: Stub(responseSuccessFetchWidget: makeConfigForm()))
        
        await sut.load()
        
        XCTAssertTrue(sut.widgets.value[0] is SupermarketLoyaltyMembershipLogoWidgetViewModel)
        XCTAssertTrue(sut.widgets.value[1] is SupermarketLoyaltyMembershipHeadingWidgetViewModel)
        XCTAssertTrue(sut.widgets.value[2] is SupermarketLoyaltyMembershipBodyWidgetViewModel)
        XCTAssertTrue(sut.widgets.value[3] is SupermarketLoyaltyMembershipInputWidgetViewModel)
        XCTAssertTrue(sut.widgets.value[4] is SupermarketLoyaltyMembershipInputWidgetViewModel)
        XCTAssertTrue(sut.widgets.value[5] is SupermarketLoyaltyMembershipSubmissionWidgetViewModel)
    }
    
    func test_load_linked_create_linked_widget() async {
        let sut = makeSUT(stub: Stub(responseSuccessFetchWidget: makeConfigLinked()))
        
        await sut.load()
        
        XCTAssertTrue(sut.widgets.value[0] is SupermarketLoyaltyMembershipLogoWidgetViewModel)
        XCTAssertTrue(sut.widgets.value[1] is SupermarketLoyaltyMembershipHeadingWidgetViewModel)
        XCTAssertTrue(sut.widgets.value[2] is SupermarketLoyaltyMembershipValueWidgetViewModel)
    }
    
    private func makeSUT(stub: Stub) -> SupermarketLoyaltyMembershipViewModel {
        return SupermarketLoyaltyMembershipViewModel(
            service: stub,
            tracking: stub,
            screenSourceName: "",
            delegate: DelegateStub())
    }
    
    private func makeConfigForm() -> SupermarketLoyaltyMembershipWidgetConfiguration {
        .form(SupermarketLoyaltyMembershipWidgetConfiguration.Form(
            title: "Form.title",
            imageURL: URL(string: "foo")!,
            heading: "Form.heading",
            body: "Form.body",
            emailFieldName: "Form.emailFieldName",
            emailFieldDescription: "Form.emailFieldDescription",
            emailFieldPlaceholder: "Form.emailFieldPlaceholder",
            emailFieldPopupImageURL: URL(string:"Form.emailFieldPopupImageURL")!,
            emailFieldPopupTitle: "Form.emailFieldPopupTitle",
            emailFieldPopupDescription: "Form.emailFieldPopupDescription",
            emailFieldPopupButtonText: "Form.emailFieldPopupButtonText",
            msiIDFieldName: "Form.msiIDFieldName",
            msiIDFieldDescription: "Form.msiIDFieldDescription",
            msiIDFieldPlaceholder: "Form.msiIDFieldPlaceholder",
            msiIDFieldPopupImageURL: URL(string:"Form.msiIDFieldPopupImageURL")!,
            msiIDFieldPopupTitle: "Form.msiIDFieldPopupTitle",
            msiIDFieldPopupDescription: "Form.msiIDFieldPopupDescription",
            msiIDFieldPopupButtonText: "Form.msiIDFieldPopupButtonText",
            buttonText: "Form.buttonText",
            buttonDescription: "Form.buttonDescription",
            successPopupImageURL: URL(string:"Form.successPopupImageURL")!,
            successPopupTitle: "Form.successPopupTitle",
            successPopupDescription: "Form.successPopupDescription",
            successPopupButtonText: "Form.successPopupButtonText"
        ))
    }
    
    private func makeConfigLinked() -> SupermarketLoyaltyMembershipWidgetConfiguration {
        .linked(SupermarketLoyaltyMembershipWidgetConfiguration.Linked(
            title: "Form.title",
            imageURL: URL(string: "foo")!,
            heading: "Form.heading",
            msiIDFieldName: "Form.msiIDFieldName",
            msiIDFieldDescription: "Form.msiIDFieldDescription",
            msiIDFieldValue: "Form.msiIDFieldValue"
        ))
    }
}

private struct Stub: SupermarketLoyaltyMembershipService, SupermarketLoyaltyMembershipTrackingService {
    var responseSuccessFetchWidget: SupermarketLoyaltyMembershipWidgetConfiguration! = nil
    var responseSuccessLink: User! = nil
    
    func fetchWidgetConfiguration() async throws -> SupermarketLoyaltyMembershipWidgetConfiguration {
        responseSuccessFetchWidget
    }
    
    func link(parameters: [String : String]) async throws -> User {
        responseSuccessLink
    }
    
    func updateGlobalUser(user: User) {
    }
    
    func msiScreenViewed(triggerSource: String, msiLinked: Bool) {
    }
    
    func msiInfoClicked(section: String) {
    }
    
    func msiIdSubmitted(success: Bool, errorMessage: String?) {
    }
    
    func msiScreenClosed() {
    }
}

private class DelegateStub: SupermarketLoyaltyMembershipDelegate {
    func didFinishLink() {
    }
}
